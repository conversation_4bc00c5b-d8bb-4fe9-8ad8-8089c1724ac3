import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';

export type ThemeMode = 'light' | 'dark' | 'system';
export type ColorScheme = 'light' | 'dark';

interface ThemeColors {
  // Background colors
  background: string;
  surface: string;
  card: string;
  header: string;
  
  // Text colors
  text: string;
  textSecondary: string;
  textMuted: string;
  
  // UI colors
  primary: string;
  tint: string;
  border: string;
  separator: string;
  
  // Icon colors
  icon: string;
  iconActive: string;
  iconInactive: string;
  
  // Tab colors
  tabBackground: string;
  tabIconDefault: string;
  tabIconSelected: string;
  
  // Status colors
  success: string;
  warning: string;
  error: string;
  info: string;
  
  // Gradient colors
  gradientStart: string;
  gradientEnd: string;
}

interface ThemeContextType {
  themeMode: ThemeMode;
  colorScheme: ColorScheme;
  colors: ThemeColors;
  setThemeMode: (mode: ThemeMode) => void;
  isDark: boolean;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

const THEME_STORAGE_KEY = '@habit_royale_theme_mode';

// Define comprehensive color schemes
const createThemeColors = (scheme: ColorScheme): ThemeColors => {
  const baseColors = Colors[scheme];
  
  if (scheme === 'dark') {
    return {
      // Background colors
      background: '#000000',
      surface: '#1F1F1F',
      card: '#2A2A2A',
      header: '#1e1e1e',
      
      // Text colors
      text: '#FFFFFF',
      textSecondary: '#ECEDEE',
      textMuted: '#aaaaaa',
      
      // UI colors
      primary: '#FFEB3B',
      tint: baseColors.tint,
      border: '#333333',
      separator: '#444444',
      
      // Icon colors
      icon: baseColors.icon,
      iconActive: '#FFEB3B',
      iconInactive: baseColors.tabIconDefault,
      
      // Tab colors
      tabBackground: '#1a1a1a',
      tabIconDefault: baseColors.tabIconDefault,
      tabIconSelected: baseColors.tabIconSelected,
      
      // Status colors
      success: '#4CAF50',
      warning: '#FF9800',
      error: '#E53935',
      info: '#1E88E5',
      
      // Gradient colors
      gradientStart: '#1a1a1a',
      gradientEnd: '#0f0f0f',
    };
  } else {
    return {
      // Background colors
      background: '#FFFFFF',
      surface: '#F5F5F5',
      card: '#FFFFFF',
      header: '#FFFFFF',
      
      // Text colors
      text: '#000000',
      textSecondary: baseColors.text,
      textMuted: '#666666',
      
      // UI colors
      primary: '#FFEB3B',
      tint: baseColors.tint,
      border: '#E0E0E0',
      separator: '#EEEEEE',
      
      // Icon colors
      icon: baseColors.icon,
      iconActive: '#FFEB3B',
      iconInactive: baseColors.tabIconDefault,
      
      // Tab colors
      tabBackground: '#FFFFFF',
      tabIconDefault: baseColors.tabIconDefault,
      tabIconSelected: baseColors.tabIconSelected,
      
      // Status colors
      success: '#4CAF50',
      warning: '#FF9800',
      error: '#E53935',
      info: '#1E88E5',
      
      // Gradient colors
      gradientStart: '#FFFFFF',
      gradientEnd: '#F5F5F5',
    };
  }
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const systemColorScheme = useColorScheme();
  const [themeMode, setThemeModeState] = useState<ThemeMode>('system');
  const [isLoading, setIsLoading] = useState(true);

  // Determine the actual color scheme based on theme mode
  const colorScheme: ColorScheme = themeMode === 'system' 
    ? (systemColorScheme ?? 'dark') 
    : themeMode === 'dark' 
    ? 'dark' 
    : 'light';

  const colors = createThemeColors(colorScheme);
  const isDark = colorScheme === 'dark';

  // Load saved theme mode from storage
  useEffect(() => {
    const loadThemeMode = async () => {
      try {
        const savedThemeMode = await AsyncStorage.getItem(THEME_STORAGE_KEY);
        if (savedThemeMode && ['light', 'dark', 'system'].includes(savedThemeMode)) {
          setThemeModeState(savedThemeMode as ThemeMode);
        }
      } catch (error) {
        console.error('Error loading theme mode:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadThemeMode();
  }, []);

  // Save theme mode to storage
  const setThemeMode = async (mode: ThemeMode) => {
    try {
      await AsyncStorage.setItem(THEME_STORAGE_KEY, mode);
      setThemeModeState(mode);
    } catch (error) {
      console.error('Error saving theme mode:', error);
    }
  };

  // Don't render children until theme is loaded
  if (isLoading) {
    return null;
  }

  const value: ThemeContextType = {
    themeMode,
    colorScheme,
    colors,
    setThemeMode,
    isDark,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
