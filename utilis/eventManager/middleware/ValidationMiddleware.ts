import { Event, EventMiddleware, EventType, EventPriority } from '../types';

export class ValidationMiddleware implements EventMiddleware {
  public readonly name = 'ValidationMiddleware';
  public readonly order = 1; // First middleware to run

  public async process(event: Event): Promise<Event | null> {
    try {
      // Validate event structure
      if (!this.isValidEvent(event)) {
        console.error(`ValidationMiddleware: Invalid event structure for ${event.id}`);
        return null;
      }

      // Validate event type
      if (!this.isValidEventType(event.type)) {
        console.error(`ValidationMiddleware: Invalid event type ${event.type} for ${event.id}`);
        return null;
      }

      // Validate event priority
      if (!this.isValidEventPriority(event.priority)) {
        console.error(`ValidationMiddleware: Invalid event priority ${event.priority} for ${event.id}`);
        return null;
      }

      // Validate event data based on type
      if (!this.isValidEventData(event)) {
        console.error(`ValidationMiddleware: Invalid event data for ${event.id}`);
        return null;
      }

      // Sanitize and normalize event data
      const sanitizedEvent = this.sanitizeEvent(event);

      console.log(`ValidationMiddleware: Event ${event.id} validated successfully`);
      return sanitizedEvent;
    } catch (error) {
      console.error(`ValidationMiddleware: Error processing event ${event.id}:`, error);
      return null;
    }
  }

  private isValidEvent(event: Event): boolean {
    return !!(
      event.id &&
      event.type &&
      event.priority &&
      event.data &&
      event.createdAt
    );
  }

  private isValidEventType(type: string): boolean {
    return Object.values(EventType).includes(type as EventType);
  }

  private isValidEventPriority(priority: string): boolean {
    return Object.values(EventPriority).includes(priority as EventPriority);
  }

  private isValidEventData(event: Event): boolean {
    const data = event.data;

    // Check required base fields
    if (!data.timestamp || !data.source) {
      return false;
    }

    // Validate timestamp format
    if (!this.isValidTimestamp(data.timestamp)) {
      return false;
    }

    // Type-specific validation
    switch (event.type) {
      case EventType.USER_SIGNUP:
      case EventType.USER_LOGIN:
      case EventType.USER_LOGOUT:
        return this.validateUserEventData(data);

      case EventType.PROGRAM_JOIN:
      case EventType.PROGRAM_LEAVE:
      case EventType.PROGRAM_SETUP_COMPLETE:
        return this.validateProgramEventData(data);

      case EventType.PAYMENT_SUCCESS:
      case EventType.PAYMENT_FAILED:
      case EventType.PAYMENT_INITIATED:
        return this.validatePaymentEventData(data);

      case EventType.NOTIFICATION_SENT:
        return this.validateNotificationEventData(data);

      default:
        // For other event types, just check basic structure
        return true;
    }
  }

  private validateUserEventData(data: any): boolean {
    return !!(data.userEmail || data.userId);
  }

  private validateProgramEventData(data: any): boolean {
    return !!(
      (data.userEmail || data.userId) &&
      data.programId &&
      data.programName
    );
  }

  private validatePaymentEventData(data: any): boolean {
    return !!(
      (data.userEmail || data.userId) &&
      data.paymentIntentId &&
      typeof data.amount === 'number' &&
      data.currency
    );
  }

  private validateNotificationEventData(data: any): boolean {
    return !!(
      (data.userEmail || data.userId) &&
      data.title &&
      data.message &&
      data.type &&
      data.priority
    );
  }

  private isValidTimestamp(timestamp: string): boolean {
    const date = new Date(timestamp);
    return !isNaN(date.getTime());
  }

  private sanitizeEvent(event: Event): Event {
    return {
      ...event,
      data: {
        ...event.data,
        // Ensure timestamp is in ISO format
        timestamp: new Date(event.data.timestamp).toISOString(),
        // Trim string fields
        source: event.data.source?.trim(),
        userEmail: event.data.userEmail?.trim()?.toLowerCase(),
        // Remove any null or undefined values
        ...this.removeNullValues(event.data),
      },
    };
  }

  private removeNullValues(obj: any): any {
    const cleaned: any = {};
    for (const [key, value] of Object.entries(obj)) {
      if (value !== null && value !== undefined) {
        if (typeof value === 'object' && !Array.isArray(value)) {
          cleaned[key] = this.removeNullValues(value);
        } else {
          cleaned[key] = value;
        }
      }
    }
    return cleaned;
  }
}
