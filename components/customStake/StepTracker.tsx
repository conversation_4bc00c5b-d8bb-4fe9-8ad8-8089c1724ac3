import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';

interface StepTrackerProps {
  currentStep: number;
  totalSteps: number;
  onStepPress: (step: number) => void;
  stepLabels: string[];
}

export const StepTracker: React.FC<StepTrackerProps> = ({
  currentStep,
  totalSteps,
  onStepPress,
  stepLabels,
}) => {
  const { colors } = useTheme();

  const styles = {
    container: {
      backgroundColor: colors.background,
      paddingHorizontal: 20,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    stepContainer: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
    },
    stepItem: {
      alignItems: 'center' as const,
      flex: 1,
    },
    stepButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      borderWidth: 2,
      marginBottom: 8,
    },
    stepButtonInactive: {
      backgroundColor: colors.surface,
      borderColor: colors.border,
    },
    stepButtonActive: {
      backgroundColor: colors.primary,
      borderColor: colors.primary,
    },
    stepButtonCompleted: {
      backgroundColor: colors.success,
      borderColor: colors.success,
    },
    stepNumber: {
      fontSize: 16,
      fontFamily: 'MontserratBold',
    },
    stepNumberInactive: {
      color: colors.textSecondary,
    },
    stepNumberActive: {
      color: '#000000',
    },
    stepNumberCompleted: {
      color: '#FFFFFF',
    },
    stepLabel: {
      fontSize: 12,
      fontFamily: 'MontserratMedium',
      textAlign: 'center' as const,
      maxWidth: 80,
    },
    stepLabelInactive: {
      color: colors.textSecondary,
    },
    stepLabelActive: {
      color: colors.text,
    },
    stepLabelCompleted: {
      color: colors.text,
    },
    connector: {
      height: 2,
      flex: 1,
      marginHorizontal: 8,
      marginBottom: 32,
    },
    connectorInactive: {
      backgroundColor: colors.border,
    },
    connectorActive: {
      backgroundColor: colors.primary,
    },
    connectorCompleted: {
      backgroundColor: colors.success,
    },
  };

  const getStepStatus = (step: number) => {
    if (step < currentStep) return 'completed';
    if (step === currentStep) return 'active';
    return 'inactive';
  };

  const getStepStyles = (status: string) => {
    switch (status) {
      case 'completed':
        return {
          button: styles.stepButtonCompleted,
          number: styles.stepNumberCompleted,
          label: styles.stepLabelCompleted,
        };
      case 'active':
        return {
          button: styles.stepButtonActive,
          number: styles.stepNumberActive,
          label: styles.stepLabelActive,
        };
      default:
        return {
          button: styles.stepButtonInactive,
          number: styles.stepNumberInactive,
          label: styles.stepLabelInactive,
        };
    }
  };

  const getConnectorStyle = (step: number) => {
    if (step < currentStep) return styles.connectorCompleted;
    if (step === currentStep) return styles.connectorActive;
    return styles.connectorInactive;
  };

  return (
    <View style={styles.container}>
      <View style={styles.stepContainer}>
        {Array.from({ length: totalSteps }, (_, index) => {
          const stepNumber = index + 1;
          const status = getStepStatus(stepNumber);
          const stepStyles = getStepStyles(status);
          const isClickable = stepNumber <= currentStep || stepNumber === currentStep + 1;

          return (
            <React.Fragment key={stepNumber}>
              <View style={styles.stepItem}>
                <TouchableOpacity
                  style={[styles.stepButton, stepStyles.button]}
                  onPress={() => isClickable && onStepPress(stepNumber)}
                  disabled={!isClickable}
                  activeOpacity={isClickable ? 0.7 : 1}
                >
                  {status === 'completed' ? (
                    <MaterialIcons
                      name="check"
                      size={20}
                      color="#FFFFFF"
                    />
                  ) : (
                    <Text style={[styles.stepNumber, stepStyles.number]}>
                      {stepNumber}
                    </Text>
                  )}
                </TouchableOpacity>
                <Text style={[styles.stepLabel, stepStyles.label]}>
                  {stepLabels[index] || `Step ${stepNumber}`}
                </Text>
              </View>
              
              {stepNumber < totalSteps && (
                <View style={[styles.connector, getConnectorStyle(stepNumber)]} />
              )}
            </React.Fragment>
          );
        })}
      </View>
    </View>
  );
};
