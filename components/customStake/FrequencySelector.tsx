import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { ReportingFrequency, FREQUENCY_OPTIONS } from '@/types/customStake';

interface FrequencySelectorProps {
  selectedFrequency: ReportingFrequency;
  onFrequencySelect: (frequency: ReportingFrequency) => void;
  visible: boolean;
  onClose: () => void;
}

export const FrequencySelector: React.FC<FrequencySelectorProps> = ({
  selectedFrequency,
  onFrequencySelect,
  visible,
  onClose,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  const handleSelect = (frequency: ReportingFrequency) => {
    onFrequencySelect(frequency);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Select Reporting Frequency</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
              accessibilityLabel="Close frequency selector"
              accessibilityRole="button"
            >
              <MaterialIcons name="close" size={24} color={colors.text} />
            </TouchableOpacity>
          </View>
          
          {FREQUENCY_OPTIONS.map((option) => (
            <TouchableOpacity
              key={option.key}
              style={styles.categoryItem}
              onPress={() => handleSelect(option.key)}
              accessibilityLabel={`Select ${option.label} frequency`}
              accessibilityRole="button"
              accessibilityHint={option.description}
            >
              <View style={styles.categoryContent}>
                <View style={styles.categoryIconContainer}>
                  <MaterialIcons 
                    name={option.icon as any} 
                    size={24} 
                    color={colors.text} 
                  />
                </View>
                <View style={styles.categoryTextContainer}>
                  <Text style={styles.categoryText}>{option.label}</Text>
                  <Text style={styles.categoryDescription}>{option.description}</Text>
                </View>
              </View>
              {selectedFrequency === option.key && (
                <MaterialIcons 
                  name="check-circle" 
                  size={20} 
                  color={colors.primary} 
                />
              )}
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </Modal>
  );
};

interface FrequencyButtonProps {
  selectedFrequency: ReportingFrequency;
  onPress: () => void;
}

export const FrequencyButton: React.FC<FrequencyButtonProps> = ({
  selectedFrequency,
  onPress,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  const selectedOption = FREQUENCY_OPTIONS.find(opt => opt.key === selectedFrequency);

  return (
    <TouchableOpacity
      style={styles.blankButton}
      onPress={onPress}
      accessibilityLabel={`Selected frequency: ${selectedOption?.label || 'frequency'}`}
      accessibilityRole="button"
      accessibilityHint="Tap to change reporting frequency"
    >
      <Text style={styles.blankText}>
        {selectedOption?.label.toLowerCase() || 'frequency'}
      </Text>
      <MaterialIcons name="keyboard-arrow-down" size={16} color="#000000" />
    </TouchableOpacity>
  );
};

interface ThemeColors {
  surface: string;
  text: string;
  textMuted: string;
  primary: string;
  background: string;
  border: string;
}

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: colors.surface,
    borderRadius: 24,
    padding: 24,
    width: '90%',
    maxHeight: '75%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 12,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontFamily: 'MontserratBold',
    color: colors.text,
    flex: 1,
    textAlign: 'center',
  },
  closeButton: {
    padding: 4,
    borderRadius: 20,
    backgroundColor: colors.surface,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 16,
    marginVertical: 4,
    borderRadius: 12,
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
  },
  categoryContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  categoryTextContainer: {
    flex: 1,
  },
  categoryText: {
    fontSize: 16,
    fontFamily: 'MontserratMedium',
    color: colors.text,
  },
  categoryDescription: {
    fontSize: 12,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
    marginTop: 2,
  },
  blankButton: {
    backgroundColor: '#FFEB3B',
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: '#FFEB3B',
    marginHorizontal: 4,
    marginVertical: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 80,
  },
  blankText: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    color: '#000000',
    textAlign: 'center',
    marginRight: 4,
  },
});
