import { StyleSheet, Platform } from 'react-native';

interface ThemeColors {
  background: string;
  surface: string;
  text: string;
  border: string;
  primary: string;
}

export const createCustomStakeStyles = (colors: ThemeColors) => StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: colors.background,
  },
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 12,
    paddingBottom: 12,
    backgroundColor: colors.background,
  },
  backButton: {
    padding: 8,
    borderRadius: 12,
    backgroundColor: colors.surface,
  },
  headerTitle: {
    flex: 1,
    fontSize: 20,
    fontFamily: 'MontserratBold',
    color: colors.text,
    textAlign: 'center',
    marginLeft: -44, // Compensate for back button width
  },
  headerSpacer: {
    width: 44,
  },
  scrollContent: {
    flex: 1,
  },
  scrollContentContainer: {
    flexGrow: 1,
  },
  submitContainer: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    paddingBottom: Platform.OS === 'ios' ? 35 : 20,
    backgroundColor: colors.background,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  submitButton: {
    backgroundColor: '#FFEB3B', // App's standard gold color
    borderRadius: 12, // Match step 2 buttons
    paddingVertical: 16, // Match step 2 buttons
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#FFEB3B',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 8,
  },
  submitButtonDisabled: {
    opacity: 0.6,
    shadowOpacity: 0.1,
    elevation: 2,
  },
  submitButtonText: {
    fontSize: 16, // Match step 2 buttons
    fontFamily: 'MontserratBold',
    color: '#000000', // Black text for consistency
    letterSpacing: 0.8,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    borderRadius: 16,
    padding: 0,
    maxWidth: 400,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 10,
  },
  modalHeader: {
    padding: 20,
    paddingBottom: 10,
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 20,
    fontFamily: 'MontserratBold',
    textAlign: 'center',
  },
  modalContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  modalMessage: {
    fontSize: 16,
    fontFamily: 'MontserratRegular',
    textAlign: 'center',
    lineHeight: 24,
  },
  modalButtons: {
    padding: 20,
    paddingTop: 10,
  },
  modalButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
  },
  modalButtonText: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    color: '#000',
  },
});
