import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { Program, DayTile, ParticipantList } from '@/types/CommonInterface';
import { ContentArea } from './ContentArea';
import ParticipantsStatusDashboard from './ParticipantsStatusDashboard';
import MoneyOverview from './MoneyOverview';
import { InfoButton } from './InfoButton';
import CategorySetup from './CategorySetup';
import ProgramEnd from './programEnd/ProgramEnd';
import { useRouter } from 'expo-router';

interface ProgramDayRendererProps {
  program: Program;
  programDay: number;
  currentDay: number;
  userId: string;
  participants: ParticipantList[]; // For ParticipantsStatusDashboard (day-specific)
  allParticipants?: ParticipantList[]; // For money calculation (all program participants)
  userDays: DayTile[];
  loading?: boolean;
  submissionTriggered?: boolean;
  onSubmissionTrigger?: () => void;
  showProgramInfo?: boolean;
  needsAttention?: boolean;
  onProgramAttention?: (programId: string) => void;
  attentionReason?: string;
  // Setup-related props
  setupStatus?: boolean | null;
  onSetupStatusUpdate?: (status: boolean) => void;
  onProgramUpdate?: (program: Program) => void;
  onSignedUpProgramsUpdate?: (programs: Program[]) => void;
  // Additional props for full feature parity
  userName?: string;
  disqualificationReason?: string | null;
  onArchive?: (programId: string) => void;
  archiveLoading?: boolean;
  setupLoading?: boolean;
  endedProgramParticipants?: number;
  // Calendar view specific props
  isCalendarView?: boolean;
}

export const ProgramDayRenderer: React.FC<ProgramDayRendererProps> = ({
  program,
  programDay,
  currentDay,
  userId,
  userName,
  participants,
  allParticipants,
  userDays,
  loading = false,
  submissionTriggered = false,
  onSubmissionTrigger,
  showProgramInfo = true,
  needsAttention = false,
  onProgramAttention,
  attentionReason,
  setupStatus,
  onSetupStatusUpdate,
  onProgramUpdate,
  onSignedUpProgramsUpdate,
  disqualificationReason,
  onArchive,
  archiveLoading = false,
  setupLoading = false,
  endedProgramParticipants,
  isCalendarView = false,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const router = useRouter();

  // Determine if the selected day is past, present, or future
  const isPastDay = isCalendarView && programDay < currentDay;
  const isFutureDay = isCalendarView && programDay > currentDay;
  const isCurrentDay = programDay === currentDay;

  if (!program) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Loading program details...</Text>
      </View>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming': return colors.textMuted;
      case 'ongoing': return colors.primary;
      case 'ended': return colors.success || '#4CAF50';
      case 'disqualified': return colors.error;
      default: return colors.textMuted;
    }
  };

  const calculateMoneyStats = () => {
    // Use allParticipants for money calculation if available, otherwise fall back to participants
    const participantsForMoney = allParticipants || participants;
    if (!program || !participantsForMoney.length) return null;

    const betAmount = program.betAmount || 0;
    const activeParticipants = participantsForMoney.filter((p) => p.livesLeft >= 0);
    const eliminatedParticipants = participantsForMoney.filter((p) => p.livesLeft < 0);

    return {
      activeCount: activeParticipants.length,
      activeMoney: activeParticipants.length * betAmount,
      disqualifiedCount: eliminatedParticipants.length,
      disqualifiedMoney: eliminatedParticipants.length * betAmount,
      userMoneyBack: betAmount,
      userShare:
        eliminatedParticipants.length > 0
          ? (betAmount * eliminatedParticipants.length) / activeParticipants.length
          : 0,
    };
  };

  const renderSetupRequired = () => {
    // Handle upcoming programs that need setup
    if (program.status === 'upcoming' && setupStatus === false) {
      return (
        <View style={styles.setupContainer}>
          <InfoButton selectedProgramId={program.id} />

          <Text style={styles.setupTitle}>Almost There!</Text>
          <Text style={styles.setupSubtitle}>
            Connect your account to unlock the challenge.
          </Text>
          <Text style={styles.setupInfo}>
            Complete this step to become eligible for all challenge features.
          </Text>

          <CategorySetup
            category={program?.category}
            selectedProgram={program}
            updateSetupStatus={(updatedSetupStatus) => {
              onSetupStatusUpdate?.(updatedSetupStatus);
            }}
            updateSelectedProgram={(updatedSelectedProgram) => {
              onProgramUpdate?.(updatedSelectedProgram);
            }}
            updateSignedUpProgram={(updateSignedUpProgram) => {
              onSignedUpProgramsUpdate?.(updateSignedUpProgram);
            }}
          />
        </View>
      );
    }
    return null;
  };

  const renderAttentionRequired = () => {
    if (!needsAttention || !onProgramAttention) return null;

    return (
      <View style={styles.attentionContainer}>
        <MaterialIcons name="warning" size={24} color={colors.error} />
        <Text style={styles.attentionTitle}>Attention Required</Text>
        <Text style={styles.attentionDescription}>
          {attentionReason === 'setup'
            ? 'Complete your program setup to get started.'
            : attentionReason === 'disqualified'
            ? 'You have been disqualified from this program.'
            : 'This program needs your attention.'}
        </Text>
        <TouchableOpacity
          style={styles.attentionButton}
          onPress={() => onProgramAttention(program.id)}
        >
          <Text style={styles.attentionButtonText}>
            {attentionReason === 'setup' ? 'Complete Setup' : 'View Details'}
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderProgramInfo = () => {
    if (!showProgramInfo) return null;

    const duration = typeof program.duration === 'string' 
      ? parseInt(program.duration, 10) 
      : program.duration;

    return (
      <View style={styles.programInfo}>
        <Text style={styles.programTitle}>{program.name}</Text>
        <View style={styles.programMeta}>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(program.status || 'upcoming') + '20' }]}>
            <Text style={[styles.statusText, { color: getStatusColor(program.status || 'upcoming') }]}>
              {(program.status || 'upcoming').toUpperCase()}
            </Text>
          </View>
          <Text style={styles.dayProgressText}>
            Day {programDay} of {duration || 'N/A'}
          </Text>
        </View>
      </View>
    );
  };

  // Handle different program statuses
  if (program.status === 'upcoming' && setupStatus === false) {
    return renderSetupRequired();
  }

  if (program.status === 'upcoming' && setupStatus === true) {
    // Show "Get Ready!" message for upcoming programs with completed setup
    return (
      <View style={styles.messageContainer}>
        <Text style={styles.messageTitle}>Get Ready!</Text>
        <Text style={styles.messageSubtitle}>
          Your challenge kicks off soon. Focus up and prepare to give it your best!
        </Text>
        <InfoButton selectedProgramId={program.id} />
      </View>
    );
  }

  if (program.status === 'ended') {
    // Show full ProgramEnd component for ended programs
    // ProgramEnd component handles its own archive functionality
    return (
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <ProgramEnd
          programId={program.id}
          duration={typeof program.duration === 'string'
            ? parseInt(program.duration, 10)
            : program.duration || 0}
          currentUserId={userId}
          betAmount={Number(program.betAmount)}
          totalPlayers={endedProgramParticipants || 0}
        />
      </ScrollView>
    );
  }

  if (program.status === 'disqualified') {
    // Show detailed disqualification message
    return (
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.messageContainer}>
          <Text style={styles.messageTitle}>You're disqualified</Text>
          {disqualificationReason === "1a" ? (
            <>
              <Text style={styles.messageSubtitle}>
                You've been disqualified because you were inactive and ran out of lives.
              </Text>
              <Text style={styles.messageInfo}>
                Our system detected that you didn't submit your daily check-ins for multiple days.
                After using all your available lives, you were automatically disqualified from the program.
                Remember that consistent participation is key to success in these challenges.
              </Text>
            </>
          ) : disqualificationReason === "1b" ? (
            <>
              <Text style={styles.messageSubtitle}>
                You've been disqualified because you had too many failed submissions and ran out of lives.
              </Text>
              <Text style={styles.messageInfo}>
                Your submissions did not meet the program requirements on multiple occasions.
                After using all your available lives to cover these failed submissions,
                you were disqualified from the program. Make sure to carefully read the
                submission guidelines for future challenges.
              </Text>
            </>
          ) : disqualificationReason === "1c" ? (
            <>
              <Text style={styles.messageSubtitle}>
                You've been disqualified because you ran out of lives.
              </Text>
              <Text style={styles.messageInfo}>
                You've used all your available lives in this challenge. Lives are a crucial
                resource that help you recover from missed or failed submissions. Once depleted,
                disqualification is automatic according to program rules. Consider purchasing
                additional lives in future challenges if needed.
              </Text>
            </>
          ) : disqualificationReason === "2" ? (
            <>
              <Text style={styles.messageSubtitle}>
                You've been disqualified due to malpractice.
              </Text>
              <Text style={styles.messageInfo}>
                Our moderators have identified violations of program rules or submission guidelines.
                This may include falsified submissions, inappropriate content, or other forms of
                misconduct that compromise the integrity of the challenge. If you believe this
                was in error, you can file a dispute through the app.
              </Text>
              <TouchableOpacity
                style={styles.disputeButton}
                onPress={() => router.push('/Disputes')}
              >
                <Text style={styles.disputeButtonText}>File a Dispute</Text>
              </TouchableOpacity>
            </>
          ) : (
            <>
              <Text style={styles.messageSubtitle}>
                Unfortunately, you've been disqualified from this program.
              </Text>
              <Text style={styles.messageInfo}>
                Your participation in this challenge has ended due to disqualification.
                This could be due to various reasons including rule violations,
                missed submissions, or running out of lives. We encourage you to
                review the program guidelines for future challenges.
              </Text>
            </>
          )}

          {onArchive && (
            <TouchableOpacity
              style={[styles.archiveButton, archiveLoading && styles.archiveButtonDisabled]}
              onPress={() => onArchive(program.id)}
              disabled={archiveLoading}
            >
              {archiveLoading ? (
                <ActivityIndicator size="small" color={colors.background} />
              ) : (
                <Text style={styles.archiveButtonText}>Archive Program</Text>
              )}
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={styles.exploreButton}
            onPress={() => router.push('/(tabs)')}
          >
            <Text style={styles.exploreButtonText}>Explore New Programs</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    );
  }

  if (needsAttention) {
    return renderAttentionRequired();
  }

  return (
    <ScrollView
      style={[styles.content, styles.scrollViewNoBorder]}
      showsVerticalScrollIndicator={false}
      keyboardShouldPersistTaps="handled"
    >
      {/* Program Info */}
      {renderProgramInfo()}

      {/* Participants Dashboard - Hide for future days in calendar view */}
      {program.status === 'ongoing' && !isFutureDay && (
        participants.length > 0 ? (
          <View style={styles.section}>
            <ParticipantsStatusDashboard
              key={`participants-${program.id}-${programDay}`}
              participants={participants}
              currentUserId={userId}
              today={programDay === currentDay}
            />
          </View>
        ) : loading ? (
          <View style={styles.section}>
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color={colors.primary} />
              <Text style={styles.loadingText}>Loading participants...</Text>
            </View>
          </View>
        ) : null
      )}

      {/* Content Area for ongoing programs */}
      {program.status === 'ongoing' && (
        <View style={styles.section}>
          {loading && userDays.length === 0 && participants.length === 0 ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color={colors.primary} />
              <Text style={styles.loadingText}>Loading submission status...</Text>
            </View>
          ) : (
            <ContentArea
              userId={userId}
              selectedProgram={program}
              selectedDay={programDay}
              currentDay={currentDay}
              days={userDays}
              participantsList={participants}
              submissionTriggered={submissionTriggered}
              loadingStatuses={[
                // Only pass loading states that should affect submissions
                // Don't include general calendar data loading that doesn't affect this specific program
                loading && (userDays.length === 0 || participants.length === 0)
              ]}
              submissionTrigger={onSubmissionTrigger || (() => {})}
            />
          )}
        </View>
      )}

      {/* Info Button */}
      <InfoButton selectedProgramId={program.id} />

      {/* Money Overview - Hide for past and future days in calendar view */}
      {program.status === 'ongoing' && !(isCalendarView && (isPastDay || isFutureDay)) && (
        loading && participants.length === 0 ? (
          <View style={styles.section}>
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color={colors.primary} />
              <Text style={styles.loadingText}>Loading money overview...</Text>
            </View>
          </View>
        ) : (
          <MoneyOverview stats={calculateMoneyStats()} />
        )
      )}
    </ScrollView>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  content: {
    flex: 1,
    paddingHorizontal: 10,
  },
  scrollViewNoBorder: {
    borderWidth: 0,
    borderColor: 'transparent',
  },
  programInfo: {
    backgroundColor: colors.surface,
    paddingVertical: 12,
    paddingHorizontal: 14,
    borderRadius: 10,
    marginBottom: 8,
  },
  programTitle: {
    fontSize: 15,
    fontFamily: 'MontserratBold',
    color: colors.text,
    marginBottom: 6,
  },
  programMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  statusBadge: {
    paddingHorizontal: 7,
    paddingVertical: 3,
    borderRadius: 10,
  },
  statusText: {
    fontSize: 9,
    fontFamily: 'MontserratBold',
  },
  dayProgressText: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    color: colors.textSecondary,
  },
  section: {
    marginBottom: 8,
    marginHorizontal: 10,
  },
  setupContainer: {
    backgroundColor: colors.surface,
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderRadius: 10,
    alignItems: 'center',
    marginBottom: 8,
  },
  setupTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: colors.text,
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  setupSubtitle: {
    fontSize: 16,
    fontFamily: 'MontserratMedium',
    color: colors.textSecondary,
    marginBottom: 8,
    textAlign: 'center',
  },
  setupInfo: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
    marginBottom: 16,
    textAlign: 'center',
    lineHeight: 20,
  },
  attentionContainer: {
    backgroundColor: colors.surface,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 12,
    borderWidth: 1,
    borderColor: colors.error + '30',
  },
  attentionTitle: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    color: colors.error,
    marginTop: 8,
    marginBottom: 4,
  },
  attentionDescription: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: 12,
  },
  attentionButton: {
    backgroundColor: colors.error,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  attentionButtonText: {
    fontSize: 14,
    fontFamily: 'MontserratBold',
    color: colors.background,
  },
  loadingContainer: {
    padding: 24,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 15,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
    marginTop: 8,
  },
  messageContainer: {
    backgroundColor: colors.surface,
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 10,
    alignItems: 'center',
    marginBottom: 8,
  },
  messageTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: colors.text,
    marginBottom: 6,
    textAlign: 'center',
  },
  messageSubtitle: {
    fontSize: 16,
    fontFamily: 'MontserratRegular',
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 22,
  },
  messageInfo: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 20,
    paddingHorizontal: 10,
  },
  disputeButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  disputeButtonText: {
    color: colors.background,
    fontSize: 16,
    fontFamily: 'MontserratSemiBold',
    textAlign: 'center',
  },
  archiveButton: {
    backgroundColor: colors.textMuted,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  archiveButtonDisabled: {
    opacity: 0.6,
  },
  archiveButtonText: {
    color: colors.background,
    fontSize: 16,
    fontFamily: 'MontserratSemiBold',
    textAlign: 'center',
  },
  exploreButton: {
    backgroundColor: colors.surface,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.textMuted + '20',
  },
  exploreButtonText: {
    color: colors.primary,
    fontSize: 16,
    fontFamily: 'MontserratSemiBold',
    textAlign: 'center',
  },
});
