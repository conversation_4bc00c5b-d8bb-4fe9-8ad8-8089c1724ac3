import React, { useMemo } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
} from "react-native";
import { FlashList } from "@shopify/flash-list";
import { useRouter } from "expo-router";
import { useTheme } from "@/contexts/ThemeContext";

interface Participant {
  id: string;
  fname: string;
  status: string;
  borderColor: string;
  livesLeft: number;
}

interface ParticipantsStatusDashboardProps {
  participants: Participant[];
  currentUserId: string;
  today: boolean; // Add this prop
}

const getInitials = (name: string): string => {
  const parts = name.trim().split(" ");
  return parts.length === 1
    ? parts[0].charAt(0).toUpperCase()
    : (parts[0].charAt(0) + parts[parts.length - 1].charAt(0)).toUpperCase();
};

const getStatusColor = (status: string, colors: any) => {
  switch (status.toLowerCase()) {
    case "submitted":
    case "bailed":
      return colors.success; // Green

    case "upcoming":
    case "not submitted yet":
      return colors.primary; // Yellow
    default:
      return colors.primary; // Default yellow
  }
};

const ParticipantsStatusDashboard: React.FC<
  ParticipantsStatusDashboardProps
> = ({ participants, currentUserId, today }) => {
  const router = useRouter();
  const { colors } = useTheme();
  const styles = createStyles(colors);

  // Fix summary counts to check for "upcoming" status
  const submittedCount = participants.filter(
    (p) => p.status.toLowerCase() === "verified"
  ).length;

  const bailedCount = participants.filter(
    (p) => p.status.toLowerCase() === "bailed"
  ).length;

  const notSubmittedCount = participants.filter(
    (p) => p.status.toLowerCase() === "not_submitted" // Changed from "not submitted yet" to "upcoming"
  ).length;

  // Current user details
  const currentUser = participants.find((p) => p.id === currentUserId);

  // Modal state for viewing full participant list with filtering
  const [modalVisible, setModalVisible] = React.useState(false);
  const [statusFilter, setStatusFilter] = React.useState("All");

  const filters = ["All", "Submitted", "Bailed", "Not Submitted Yet"];

  // Update filtered participants logic to handle "Not Submitted Yet" filter
  const filteredParticipants = useMemo(() => {
    if (statusFilter === "All") return participants;

    if (statusFilter === "Not Submitted Yet") {
      return participants.filter((p) => p.status.toLowerCase() === "upcoming");
    }

    return participants.filter(
      (p) => p.status.toLowerCase() === statusFilter.toLowerCase()
    );
  }, [participants, statusFilter]);

  const renderParticipant = ({ item }: { item: Participant }) => (
    <View style={styles.participantRow}>
      <View style={[styles.circle, { borderColor: item.borderColor }]}>
        <Text style={styles.circleText}>{getInitials(item.fname)}</Text>
      </View>
      {item.id === currentUserId ? (
        <Text style={[styles.participantName, styles.youText]}>You</Text>
      ) : (
        <TouchableOpacity
          style={styles.nameButton}
          onPress={() => router.push(`/UserProfile?userId=${item.id}`)}
        >
          <Text style={styles.participantName}>{item.fname}</Text>
        </TouchableOpacity>
      )}
      <Text style={styles.participantStatus}>
        {item.status === "upcoming"
          ? today
            ? "Not Submitted Yet"
            : "Not Submitted"
          : item.status}
      </Text>
    </View>
  );

  return (
    <View style={styles.dashboardContainer}>
      {/* Enhanced Combined Dashboard */}
      <View style={styles.combinedContainer}>
        {/* Centered Header: User Status */}
        <View style={styles.centeredHeader}>
          <View style={styles.userAvatar}>
            <Text style={styles.avatarText}>YOU</Text>
          </View>
          {currentUser ? (
            <Text style={styles.userStatusText}>
              {currentUser.status === "upcoming"
                ? "Ready to Submit"
                : currentUser.status === "verified"
                ? "Submission Verified"
                : currentUser.status === "bailed"
                ? "Used Bail Option"
                : "Status Unknown"}
            </Text>
          ) : (
            <Text style={styles.userStatusText}>No Status Available</Text>
          )}
          {currentUser && (
            <View
              style={[
                styles.statusIndicator,
                { backgroundColor: getStatusColor(currentUser.status, colors) }
              ]}
            />
          )}
        </View>

        {/* Stats Grid */}
        <View style={styles.statsGrid}>
          <View style={[styles.statCard, styles.submittedCard]}>
            <Text style={styles.statNumber}>{submittedCount}</Text>
            <Text style={styles.statText}>Done</Text>
          </View>
          <View style={[styles.statCard, styles.bailedCard]}>
            <Text style={styles.statNumber}>{bailedCount}</Text>
            <Text style={styles.statText}>Bailed</Text>
          </View>
          <View style={[styles.statCard, styles.pendingCard]}>
            <Text style={styles.statNumber}>{notSubmittedCount}</Text>
            <Text style={styles.statText}>{today ? "Pending" : "Missing"}</Text>
          </View>
        </View>

        {/* Subtle View All Button - Bottom Right */}
        <TouchableOpacity
          style={styles.viewAllButton}
          onPress={() => setModalVisible(true)}
        >
          <Text style={styles.viewAllText}>View All</Text>
        </TouchableOpacity>
      </View>

      {/* Modal for Participants List with Filtering */}
      <Modal
        visible={modalVisible}
        animationType="slide"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <Text style={styles.modalTitle}>Participants</Text>
          <View style={styles.filterContainer}>
            {filters.map((filter) => (
              <TouchableOpacity
                key={filter}
                style={[
                  styles.filterButton,
                  filter === statusFilter && styles.activeFilterButton,
                ]}
                onPress={() => setStatusFilter(filter)}
              >
                <Text
                  style={[
                    styles.filterButtonText,
                    filter === statusFilter && styles.activeFilterButtonText,
                  ]}
                >
                  {filter}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
          <FlashList
            data={filteredParticipants}
            keyExtractor={(item) => item.id}
            renderItem={renderParticipant}
            estimatedItemSize={50}
            contentContainerStyle={styles.modalListContainer}
          />
          <TouchableOpacity
            style={styles.modalCloseButton}
            onPress={() => setModalVisible(false)}
          >
            <Text style={styles.modalCloseButtonText}>Close</Text>
          </TouchableOpacity>
        </View>
      </Modal>
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  dashboardContainer: {
    backgroundColor: colors.background,
    outline: 'none',
  },
  combinedContainer: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 12,
    marginTop: 8,
    marginBottom: 4,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
    position: 'relative',
  },
  centeredHeader: {
    alignItems: "center",
    marginBottom: 12,
  },
  userAvatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: colors.primary + '20',
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
  },
  avatarText: {
    fontSize: 10,
    color: colors.primary,
    fontFamily: 'MontserratBold',
  },
  userStatusText: {
    fontSize: 15,
    color: colors.text,
    fontFamily: 'MontserratBold',
    textAlign: 'center',
    marginBottom: 4,
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  viewAllButton: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    paddingVertical: 4,
    paddingHorizontal: 8,
    outline: 'none',
  },
  viewAllText: {
    fontSize: 10,
    color: colors.textMuted,
    fontFamily: 'MontserratMedium',
  },
  statsGrid: {
    flexDirection: "row",
    marginBottom: 10,
    gap: 6,
  },
  statCard: {
    flex: 1,
    backgroundColor: colors.background,
    borderRadius: 8,
    padding: 8,
    alignItems: "center",
  },
  submittedCard: {
    borderWidth: 1,
    borderColor: (colors.success || '#4CAF50') + '30',
    backgroundColor: (colors.success || '#4CAF50') + '08',
  },
  bailedCard: {
    borderWidth: 1,
    borderColor: (colors.warning || '#FF9800') + '30',
    backgroundColor: (colors.warning || '#FF9800') + '08',
  },
  pendingCard: {
    borderWidth: 1,
    borderColor: colors.primary + '30',
    backgroundColor: colors.primary + '08',
  },
  statNumber: {
    fontSize: 16,
    color: colors.text,
    fontFamily: 'MontserratBold',
    marginBottom: 1,
  },
  statText: {
    fontSize: 9,
    color: colors.textMuted,
    fontFamily: 'MontserratMedium',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: colors.background,
    padding: 16,
  },
  modalTitle: {
    fontSize: 22,
    color: colors.primary,
    fontWeight: "bold",
    textAlign: "center",
    marginBottom: 10,
  },
  filterContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    marginBottom: 10,
  },
  filterButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 8,
    backgroundColor: colors.surface,
    outline: 'none',
  },
  activeFilterButton: {
    backgroundColor: colors.primary,
  },
  filterButtonText: {
    color: colors.text,
    fontSize: 14,
  },
  activeFilterButtonText: {
    color: colors.background,
    fontWeight: "bold",
  },
  modalListContainer: {
    paddingBottom: 20,
  },
  participantRow: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  circle: {
    width: 40,
    height: 40,
    borderRadius: 4,
    borderWidth: 2,
    backgroundColor: colors.surface,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 10,
  },
  circleText: {
    color: colors.text,
    fontSize: 10,
    fontWeight: "bold",
  },
  nameButton: {
    flex: 1,
    outline: 'none',
  },
  participantName: {
    color: colors.text,
    fontSize: 14,
    flex: 1,
  },
  youText: {
    fontWeight: "bold",
    color: colors.primary,
    flex: 1,
  },
  participantStatus: {
    color: colors.primary,
    fontSize: 14,
    fontWeight: "bold",
  },
  modalCloseButton: {
    backgroundColor: colors.primary,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: "center",
    alignSelf: "center",
    marginTop: 20,
    outline: 'none',
  },
  modalCloseButtonText: {
    color: colors.background,
    fontSize: 16,
    fontWeight: "bold",
  },
});

export default ParticipantsStatusDashboard;
