{"version": 3, "file": "githubOAuth.js", "sourceRoot": "", "sources": ["../src/githubOAuth.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,uDAAsD;AACtD,uDAAmD;AACnD,sDAAwC;AAExC,6BAA6B;AAC7B,MAAM,aAAa,GAAG;IACpB,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB;IACvC,aAAa,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB;IAC/C,MAAM,EAAE,CAAC,MAAM,EAAE,YAAY,CAAC;CAC/B,CAAC;AAkBF;;;GAGG;AACU,QAAA,mBAAmB,GAAG,IAAA,iBAAS,EAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAC9D,mBAAmB;IACnB,GAAG,CAAC,GAAG,CAAC,6BAA6B,EAAE,GAAG,CAAC,CAAC;IAC5C,GAAG,CAAC,GAAG,CAAC,8BAA8B,EAAE,oBAAoB,CAAC,CAAC;IAC9D,GAAG,CAAC,GAAG,CAAC,8BAA8B,EAAE,cAAc,CAAC,CAAC;IAExD,4BAA4B;IAC5B,IAAI,GAAG,CAAC,MAAM,KAAK,SAAS,EAAE;QAC5B,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACzB,OAAO;KACR;IAED,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC;QAEzC,sBAAsB;QACtB,IAAI,KAAK,EAAE;YACT,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;YAC5C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;OAoBpB,CAAC,CAAC;YACH,OAAO;SACR;QAED,+BAA+B;QAC/B,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE;YACnB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;OAoBpB,CAAC,CAAC;YACH,OAAO;SACR;QAED,iCAAiC;QACjC,MAAM,aAAa,GAAG,MAAM,KAAK,CAAC,6CAA6C,EAAE;YAC/E,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBACP,QAAQ,EAAE,kBAAkB;gBAC5B,cAAc,EAAE,kBAAkB;aACnC;YACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gBACnB,SAAS,EAAE,aAAa,CAAC,SAAS;gBAClC,aAAa,EAAE,aAAa,CAAC,aAAa;gBAC1C,IAAI,EAAE,IAAc;aACrB,CAAC;SACH,CAAC,CAAC;QAEH,MAAM,SAAS,GAAG,MAAM,aAAa,CAAC,IAAI,EAAyB,CAAC;QAEpE,IAAI,SAAS,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE;YAC9C,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,SAAS,CAAC,iBAAiB,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC;YAC9F,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;wBAgBH,SAAS,CAAC,iBAAiB,IAAI,SAAS,CAAC,KAAK,IAAI,eAAe;;;;;OAKlF,CAAC,CAAC;YACH,OAAO;SACR;QAED,mCAAmC;QACnC,MAAM,YAAY,GAAG,MAAM,KAAK,CAAC,6BAA6B,EAAE;YAC9D,OAAO,EAAE;gBACP,eAAe,EAAE,SAAS,SAAS,CAAC,YAAY,EAAE;gBAClD,QAAQ,EAAE,gCAAgC;aAC3C;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE;YACpB,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC;YAC5E,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;OAoBpB,CAAC,CAAC;YACH,OAAO;SACR;QAED,MAAM,QAAQ,GAAG,MAAM,YAAY,CAAC,IAAI,EAAgB,CAAC;QAEzD,sEAAsE;QACtE,sEAAsE;QACtE,MAAM,EAAE,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAC7B,MAAM,QAAQ,GAAG;YACf,WAAW,EAAE,SAAS,CAAC,YAAY;YACnC,SAAS,EAAE,SAAS,CAAC,UAAU,IAAI,QAAQ;YAC3C,KAAK,EAAE,SAAS,CAAC,KAAK;YACtB,IAAI,EAAE;gBACJ,EAAE,EAAE,QAAQ,CAAC,EAAE;gBACf,KAAK,EAAE,QAAQ,CAAC,KAAK;gBACrB,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,KAAK,EAAE,QAAQ,CAAC,KAAK;gBACrB,UAAU,EAAE,QAAQ,CAAC,UAAU;aAChC;YACD,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE;YACvD,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,gBAAgB;SACnE,CAAC;QAEF,MAAM,EAAE,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC,GAAG,CAAC,KAAe,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAE9E,sBAAsB;QACtB,GAAG,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBA+DW,QAAQ,CAAC,UAAU;yBAClB,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,KAAK;kBACtC,QAAQ,CAAC,KAAK;;;;;;;;;;;;;;;;;;0BAkBN,KAAK;;4BAEH,QAAQ,CAAC,KAAK;2BACf,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,KAAK;iCACzB,QAAQ,CAAC,UAAU;;;;;;;;KAQ/C,CAAC,CAAC;KAEJ;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACrD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;KAoBpB,CAAC,CAAC;KACJ;AACH,CAAC,CAAC,CAAC;AAEH;;;GAGG;AACU,QAAA,cAAc,GAAG,IAAA,cAAM,EAAC,KAAK,EAAE,OAAO,EAAE,EAAE;IACrD,IAAI;QACF,wBAAwB;QACxB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;YACjB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;SAC/C;QAED,MAAM,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;QAE/B,IAAI,CAAC,KAAK,EAAE;YACV,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;SAChD;QAED,MAAM,EAAE,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAC7B,MAAM,QAAQ,GAAG,MAAM,EAAE,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC;QAE7E,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YACpB,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;SACrD;QAED,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;QAElC,6BAA6B;QAC7B,IAAI,SAAS,EAAE,SAAS,IAAI,SAAS,CAAC,SAAS,CAAC,MAAM,EAAE,GAAG,IAAI,IAAI,EAAE,EAAE;YACrE,yBAAyB;YACzB,MAAM,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;SAC5C;QAED,oDAAoD;QACpD,MAAM,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;QAE5B,OAAO;YACL,OAAO,EAAE,IAAI;YACb,WAAW,EAAE,SAAS,EAAE,WAAW;YACnC,IAAI,EAAE,SAAS,EAAE,IAAI;SACtB,CAAC;KAEH;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;QACvD,IAAI,KAAK,YAAY,KAAK,EAAE;YAC1B,MAAM,KAAK,CAAC;SACb;QACD,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;KACpD;AACH,CAAC,CAAC,CAAC"}