import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  TouchableOpacity
} from 'react-native';

import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { useTheme } from '@/contexts/ThemeContext';
import { Participant } from './ProgramEnd';

interface Props {
  participants: Participant[];
  duration: number;
  currentUserId: string;
  stats: { totalSubmissions: number; completionRate: number };
  compact?: boolean;
  showFullView?: boolean;
  setShowFullView?: (show: boolean) => void;
  fullHeight?: boolean;
}

  const getStatusEmoji = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'bailed':
        return '🔨';
      case 'verified':
      case 'submitted':  // Add this case
        return '✔️';
      case 'not_submitted':
      case 'upcoming':
      default:
        return '❌';
    }
  };

const SubmissionStatusBoard: React.FC<Props> = ({
  participants,
  duration,
  currentUserId,
  stats,
  compact = false,
  showFullView = false,
  setShowFullView,
  fullHeight = false,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  if (!participants) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  const playersToShow =
    compact && !showFullView
      ? [
          ...participants.filter((p) => p.id === currentUserId),
          ...participants.filter((p) => p.id !== currentUserId).slice(0, 2),
        ]
      : participants;

  return (
    <View style={[
      styles.mainContainer, 
      fullHeight && { height: '100%' }
    ]}>
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={true}
        contentContainerStyle={{ paddingBottom: 0 }}
      >
        <View>
          {/* Fixed Header Row */}
          <View style={styles.row}>
            <View style={styles.nameCell}>
              <Text style={styles.headerText}>Players</Text>
            </View>
            {Array.from({ length: duration }, (_, i) => (
              <View key={`week-${i + 1}`} style={styles.cell}>
                <Text style={styles.headerText}>D{i + 1}</Text>
              </View>
            ))}
          </View>

          {/* Scrollable Player Rows */}
          <ScrollView 
            style={[
              styles.playersScrollView,
              fullHeight && { height: undefined, flex: 1 }
            ]}
            showsVerticalScrollIndicator={true}
            contentContainerStyle={styles.playersScrollViewContent}
          >
            {/* Current User Row (Always First) */}
            {playersToShow
              .filter(player => player.id === currentUserId)
              .map(player => (
                <View key={player.id} style={styles.row}>
                  <View style={styles.nameCell}>
                    <Text style={[styles.playerName, styles.currentUserName]}>
                      You
                    </Text>
                  </View>
                  {Array.from({ length: duration }, (_, i) => {
                    const dayKey = `Day ${i + 1}`;
                    const submission = player.submissions[dayKey];
                    const status = submission?.status || 'not_submitted';
                    return (
                      <View key={`${player.id}-week-${i + 1}`} style={styles.cell}>
                        <Text style={styles.statusEmoji}>
                          {getStatusEmoji(status)}
                        </Text>
                      </View>
                    );
                  })}
                </View>
              ))}

            {/* Other Players */}
            {playersToShow
              .filter(player => player.id !== currentUserId)
              .map(player => (
                <View key={player.id} style={styles.row}>
                  <View style={styles.nameCell}>
                    <Text style={styles.playerName}>{player.fname}</Text>
                  </View>
                  {Array.from({ length: duration }, (_, i) => {
                    const dayKey = `Day ${i + 1}`;
                    const submission = player.submissions[dayKey];
                    const status = submission?.status || 'not_submitted';
                    return (
                      <View key={`${player.id}-week-${i + 1}`} style={styles.cell}>
                        <Text style={styles.statusEmoji}>
                          {getStatusEmoji(status)}
                        </Text>
                      </View>
                    );
                  })}
                </View>
              ))}
          </ScrollView>
        </View>
      </ScrollView>
      
      {/* Show More button outside the table */}
      {compact && !showFullView && participants.length > 3 && (
        <TouchableOpacity 
          style={styles.showMoreButton}
          onPress={() => setShowFullView && setShowFullView(true)}
        >
          <Text style={styles.showMoreText}>Show More</Text>
          <MaterialIcons name="expand-more" size={14} color="#FFEB3B" />
        </TouchableOpacity>
      )}
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  mainContainer: {
    height: '100%',
    // Remove backgroundColor since it will be provided by the parent
    borderRadius: 8,
    paddingBottom: 5,
  },
  loadingContainer: {
    padding: 12, // Reduced from 20
    alignItems: 'center',
    justifyContent: 'center',
  },
  row: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    height: 30, // Significantly reduced from 36
  },
  nameCell: {
    width: 80, // Reduced from 90
    padding: 4, // Reduced from 8
    justifyContent: 'center',
    backgroundColor: colors.surface,
  },
  cell: {
    width: 35, // Reduced from 40
    padding: 2, // Reduced from 4
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.surface,
  },
  headerText: {
    color: colors.primary,
    fontFamily: 'MontserratBold',
    fontSize: 11, // Reduced from 12
    lineHeight: 13, // Reduced from 14
  },
  playerName: {
    color: colors.text,
    fontFamily: 'MontserratBold',
    fontSize: 11, // Reduced from 12
  },
  currentUserName: {
    color: colors.primary,
  },
  statusEmoji: {
    fontSize: 12, // Reduced from 14
  },
  playersScrollView: {
    height: 108, // Slightly reduced from 112
    paddingBottom: 0,
  },
  playersScrollViewContent: {
    flexGrow: 1,
    paddingBottom: 0, // Ensure no padding at the bottom
  },
  showMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 3,
    paddingHorizontal: 8,
    marginTop: 0, // Remove top margin
    marginBottom: 5, // Add bottom margin
    backgroundColor: colors.card,
    borderRadius: 4,
    alignSelf: 'center',
    opacity: 0.9,
    position: 'relative',
    zIndex: 1,
  },
  showMoreText: {
    color: colors.primary,
    fontSize: 10,
    fontFamily: 'MontserratBold',
    marginRight: 3,
  },
});

export default SubmissionStatusBoard;














