// Pool Challenges Tab - Earn money by joining pool challenges with other participants

import React, { useCallback, useEffect, useState, memo } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Image,
} from "react-native";
import { MaterialCommunityIcons, FontAwesome5 } from "@expo/vector-icons";
import { LinearGradient } from 'expo-linear-gradient';
import { firestoreService } from "../../services/database";
import { getId } from "../../utilis/variables";
import { useRouter } from "expo-router";
import Header from "@/components/Header";
import { FlashList } from "@shopify/flash-list";
import CategoryFilter from "@/components/pageFunctions/Explore/CategoryFilter";
import { useTheme } from "@/contexts/ThemeContext";
import { measureDatabaseQuery, useRenderTiming, useTabSwitchTiming } from "@/utils/performance";
import { Program } from "@/types/CommonInterface";

// Create category icons function that uses theme colors
const getCategoryIcons = (primaryColor: string): Record<string, JSX.Element> => ({
  gym: (
    <MaterialCommunityIcons name="weight-lifter" size={30} color={primaryColor} />
  ),
  cardio: <FontAwesome5 name="running" size={30} color={primaryColor} />,
  coding: <MaterialCommunityIcons name="laptop" size={30} color={primaryColor} />,
  journaling: <FontAwesome5 name="book-open" size={30} color={primaryColor} />,
  affirmations: (
    <MaterialCommunityIcons name="message-text" size={30} color={primaryColor} />
  ),
  writing: <MaterialCommunityIcons name="pencil" size={30} color={primaryColor} />,
});

// Memoized ProgramCard component
const ProgramCard = memo(
  ({ item, onPress, colors }: { item: Program; onPress: (p: Program) => void; colors: any }) => {
    const categoryIcons = getCategoryIcons(colors.primary);
    const formatDate = (dateString: string) => {
      const date = new Date(dateString);
      return `${date.getDate()} ${date.toLocaleString("default", {
        month: "short",
      })}`;
    };

    const dateRange = `${item.startDate ? formatDate(item.startDate) : 'TBD'} - ${item.endDate ? formatDate(item.endDate) : 'TBD'}`;

    return (
      <TouchableOpacity
        style={[
          styles.card,
          { backgroundColor: colors.surface, borderColor: colors.border },
          !item.registrationsOpen && { borderColor: colors.error, borderWidth: 2 },
        ]}
        onPress={() => onPress(item)}
        activeOpacity={0.9}
      >
        {/* PROGRAM IMAGE THUMBNAIL */}
        {(item.image?.url || true) && (
          <View style={styles.imageContainer}>
            <Image
              source={{ uri: item.image?.url || 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=450&fit=crop' }}
              style={styles.programThumbnail}
              resizeMode="cover"
              loadingIndicatorSource={require('@/assets/images/icon.png')}
              fadeDuration={200}
            />
            {/* Fadeout gradient overlay */}
            <LinearGradient
              colors={['rgba(0,0,0,0)', 'rgba(0,0,0,0.3)', colors.surface]}
              locations={[0, 0.4, 1]}
              style={styles.imageGradientOverlay}
            />
          </View>
        )}

        {/* HEADER */}
        <View style={[styles.cardHeader, item.image?.url && { marginTop: 0, paddingTop: 8 }]}>
          {!item.image?.url && (
            <View style={[styles.iconContainer, { backgroundColor: colors.card }]}>
              {(item.category && categoryIcons[item.category]) || (
                <MaterialCommunityIcons name="tag" size={30} color={colors.primary} />
              )}
            </View>
          )}
          <View style={styles.cardTextContainer}>
            <View style={styles.titleRow}>
              <Text
                style={[styles.programName, { color: colors.text }]}
                numberOfLines={2}
                ellipsizeMode="tail"
              >
                {item.name}
              </Text>
              <View style={[styles.categoryBadge, { backgroundColor: colors.primary }]}>
                <Text style={styles.categoryText}>
                  {String(item.category).charAt(0).toUpperCase() + String(item.category).slice(1)}
                </Text>
              </View>
            </View>
            {item.headline ? (
              <Text
                style={[styles.headlineText, { color: colors.textSecondary }]}
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {item.headline}
              </Text>
            ) : null}
          </View>
        </View>

        {/* DATE & CHALLENGE INFO */}
        <View style={styles.cardBody}>
          <View style={styles.cardInfoRow}>
            <Text style={[styles.programDetails, { color: colors.textSecondary }]}>
              {item.duration ? Math.round(Number(item.duration) / 7) : 0} Week Challenge
            </Text>
            <Text style={[styles.programDates, { color: colors.primary }]}>{dateRange}</Text>
          </View>
        </View>

        {/* FOOTER */}
        <View style={[styles.cardFooter, { borderTopColor: colors.separator }]}>
          <View style={styles.cardFooterItem}>
            <Text style={[styles.footerLabel, { color: colors.textMuted }]}>Bet Amount</Text>
            <Text style={[styles.footerValue, { color: colors.text }]}>${item.betAmount}</Text>
          </View>
          <View style={styles.cardFooterItem}>
            <Text style={[styles.footerLabel, { color: colors.textMuted }]}>Pool Amount</Text>
            <Text style={[styles.footerValue, { color: colors.text }]}>
              ${(item.participantsCount || 0) * (item.betAmount || 0)}
            </Text>
          </View>
          <View style={styles.cardFooterItem}>
            <Text style={[styles.footerLabel, { color: colors.textMuted }]}>Current Size</Text>
            <Text style={[styles.footerValue, { color: colors.text }]}>{item.participantsCount}</Text>
          </View>
          <View style={styles.cardFooterItem}>
            <Text style={[styles.footerLabel, { color: colors.textMuted }]}>Registration</Text>
            <Text
              style={[
                styles.footerValue,
                { color: item.registrationsOpen ? "#4CAF50" : "#FF5252" },
              ]}
            >
              {item.registrationsOpen ? "Open" : "Closed"}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  }
);

const PoolChallenges: React.FC = () => {
  useRenderTiming('PoolChallengesScreen');
  useTabSwitchTiming('PoolChallenges');
  const { colors } = useTheme();
  const [programs, setPrograms] = useState<Program[]>([]);
  const [filteredPrograms, setFilteredPrograms] = useState<Program[]>([]);
  const [userId, setUserId] = useState<string>("");
  const [isLoadingPrograms, setIsLoadingPrograms] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string>("All");

  const router = useRouter();

  const fetchPrograms = useCallback(async () => {
    setIsLoadingPrograms(true);
    try {
      const id = String(await getId());
      setUserId(id);

      // Parallel fetch for better performance with monitoring
      const [programsResult, userProgramsResult] = await Promise.all([
        measureDatabaseQuery('getAllPrograms', () => firestoreService.programs.getAllPrograms()),
        measureDatabaseQuery('getUserPrograms', () => firestoreService.users.getUserPrograms(id))
      ]);

      if (!programsResult.success) {
        throw new Error(programsResult.error || 'Failed to fetch programs');
      }

      const programList = (programsResult.data || []) as Program[];
      setPrograms(programList);

      const signedUpProgramIds = userProgramsResult.success
        ? (userProgramsResult.data || []).map(p => p.programId)
        : [];

      const notJoined = programList.filter(
        (program) => !signedUpProgramIds.includes(program.id || '')
      );
      setFilteredPrograms(notJoined);
    } catch (error) {
      console.error("Error fetching programs:", error);
    } finally {
      setIsLoadingPrograms(false);
    }
  }, []);

  useEffect(() => {
    fetchPrograms();
  }, [fetchPrograms]);

  // Re-filter programs based on selected category
  const displayedPrograms = filteredPrograms.filter((p) =>
    selectedCategory === "All" ? true : p.category === selectedCategory
  );

  const handleProgramPress = useCallback(
    (program: Program) => {
      router.push(`/ProgramDetails?programId=${program.id}`);
    },
    [router]
  );

  const renderProgramCard = useCallback(
    ({ item }: { item: Program }) => (
      <ProgramCard item={item} onPress={handleProgramPress} colors={colors} />
    ),
    [handleProgramPress, colors]
  );

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Header />
      <Text style={[styles.headerText, { color: colors.primary }]}>Pool Challenges</Text>

      {/* Description */}
      <View style={styles.descriptionContainer}>
        <Text style={[styles.descriptionText, { color: colors.textSecondary }]}>
          Compete with others and earn from the shared pool!
        </Text>
      </View>

      {/* Horizontal Scroll for Category Filter Buttons */}
      <View>
        <CategoryFilter
          selectedCategory={selectedCategory}
          onSelectCategory={setSelectedCategory}
        />
      </View>

      {isLoadingPrograms ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      ) : displayedPrograms.length === 0 ? (
        <View style={styles.noProgramsContainer}>
          <Text style={[styles.noProgramsText, { color: colors.primary }]}>Coming soon... Stay tuned!</Text>
        </View>
      ) : (
        <FlashList
          data={displayedPrograms}
          renderItem={renderProgramCard}
          keyExtractor={(item) => item.id}
          contentContainerStyle={{ paddingBottom: 20 }}
          estimatedItemSize={200}
          getItemType={() => 'program-card'}
        />
      )}


    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor will be set dynamically based on theme
  },
  headerText: {
    // color will be set dynamically based on theme
    fontSize: 22,
    fontFamily: "MontserratBold",
    textAlign: "center",
    marginVertical: 6,
  },
  descriptionContainer: {
    paddingHorizontal: 20,
    marginBottom: 10,
  },
  descriptionText: {
    // color will be set dynamically based on theme
    fontSize: 14,
    fontFamily: "MontserratRegular",
    textAlign: "center",
    lineHeight: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  noProgramsContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 20,
  },
  noProgramsText: {
    // color will be set dynamically based on theme
    fontSize: 18,
    fontFamily: "MontserratBold",
    textAlign: "center",
  },
  card: {
    // backgroundColor and borderColor will be set dynamically based on theme
    borderRadius: 15,
    marginVertical: 10,
    marginHorizontal: 12, // Reduced from 20 to 12 to make cards wider
    padding: 0, // Remove padding to allow image to touch edges
    boxShadow: '0 5px 10px rgba(0, 0, 0, 0.3)',
    elevation: 8,
    borderWidth: 1,
    overflow: "hidden", // Ensure image respects card border radius
  },
  cardHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 10,
    paddingHorizontal: 15, // Add horizontal padding back for content
    paddingTop: 15, // Add top padding for content
  },
  iconContainer: {
    width: 50,
    height: 50,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 25,
    // backgroundColor will be set dynamically based on theme
    marginRight: 10,
  },
  imageContainer: {
    position: 'relative',
    width: "100%",
  },
  programThumbnail: {
    width: "100%",
    aspectRatio: 16 / 9, // This ensures 16:9 ratio with no cropping
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    marginBottom: 0, // Remove margin to touch card edges
  },
  imageGradientOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '60%', // Cover bottom 60% of the image
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
  },
  cardTextContainer: {
    flex: 1,
  },
  titleRow: {
    flexDirection: "row",
    alignItems: "flex-start",
    justifyContent: "space-between",
    marginBottom: 3, // Reduced from 6 to 3 to decrease gap
  },
  categoryBadge: {
    // backgroundColor will be set dynamically based on theme
    paddingHorizontal: 6, // Reduced from 8 to 6
    paddingVertical: 2, // Reduced from 4 to 2
    borderRadius: 10, // Reduced from 12 to 10
    alignSelf: "flex-start",
    marginTop: 2, // Added top padding/margin
  },
  categoryText: {
    color: "#000",
    fontSize: 12,
    fontFamily: "MontserratBold",
    textTransform: "capitalize",
  },
  programName: {
    // color will be set dynamically based on theme
    fontSize: 16,
    fontFamily: "MontserratBold",
    flex: 1,
    marginRight: 8,
  },
  headlineText: {
    // color will be set dynamically based on theme
    fontSize: 13,
    fontFamily: "MontserratRegular",
    marginTop: 2,
  },
  cardBody: {
    marginVertical: 8,
    paddingHorizontal: 15, // Add horizontal padding for content
  },
  cardInfoRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 5,
  },
  programDetails: {
    // color will be set dynamically based on theme
    fontSize: 14,
    fontFamily: "MontserratRegular",
  },
  programDates: {
    // color will be set dynamically based on theme
    fontSize: 14,
    fontFamily: "MontserratBold",
  },
  cardFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    borderTopWidth: 1,
    // borderTopColor will be set dynamically based on theme
    paddingTop: 8,
    paddingHorizontal: 15, // Add horizontal padding for content
    paddingBottom: 15, // Add bottom padding for content
    marginTop: 8,
  },
  cardFooterItem: {
    alignItems: "center",
    flex: 1,
  },
  footerLabel: {
    // color will be set dynamically based on theme
    fontSize: 12,
    fontFamily: "MontserratRegular",
  },
  footerValue: {
    // color will be set dynamically based on theme
    fontSize: 14,
    fontFamily: "MontserratBold",
  },
});

export default PoolChallenges;

