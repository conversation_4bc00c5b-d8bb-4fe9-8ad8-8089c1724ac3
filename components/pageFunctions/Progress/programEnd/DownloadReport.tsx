import React from 'react';
import { TouchableOpacity, Text, StyleSheet, View, Alert } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';
import { useTheme } from '@/contexts/ThemeContext';

interface ProgramStats {
  submissionStats?: {
    totalSubmissions?: number;
    completionRate?: number;
  };
  moneyStats?: {
    totalPool?: number;
    distributedAmount?: number;
  };
  disqualifiedStats?: {
    count?: number;
    loserPool?: number;
  };
  winnerShare?: {
    amount?: number;
    rank?: number;
  };
}

interface ProgramData {
  name?: string;
  description?: string;
  duration?: number;
  startDate?: string;
  endDate?: string;
  betAmount?: number;
  category?: string;
  totalPlayers?: number;
}

interface DownloadReportProps {
  programId: string;
  programData: ProgramData;
  stats: ProgramStats;
}

const DownloadReport: React.FC<DownloadReportProps> = ({
  programId,
  programData,
  stats
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const generateHTML = () => {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <style>
            body { 
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
              padding: 20px; 
              color: #333;
              max-width: 800px;
              margin: 0 auto;
            }
            h1 { 
              color: #000;
              font-size: 24px;
              margin-bottom: 20px;
              text-align: center;
            }
            .section { 
              margin: 20px 0; 
              padding: 15px; 
              background: #f8f8f8; 
              border-radius: 8px;
              box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .section-title {
              font-size: 18px;
              font-weight: bold;
              margin-bottom: 10px;
              color: #2A2A2A;
            }
            .data-row {
              display: flex;
              justify-content: space-between;
              padding: 8px 0;
              border-bottom: 1px solid #eee;
            }
            .label {
              font-weight: 500;
            }
            .value {
              color: #666;
            }
            @media print {
              body {
                padding: 0;
              }
              .section {
                break-inside: avoid;
              }
            }
          </style>
        </head>
        <body>
          <h1>Program Report - ${programData.name || 'N/A'}</h1>
          
          <div class="section">
            <div class="section-title">Program Details</div>
            <div class="data-row">
              <span class="label">Duration:</span>
              <span class="value">${programData.duration || 0} days</span>
            </div>
            <div class="data-row">
              <span class="label">Category:</span>
              <span class="value">${programData.category || 'N/A'}</span>
            </div>
            <div class="data-row">
              <span class="label">Total Participants:</span>
              <span class="value">${programData.totalPlayers || 0}</span>
            </div>
            <div class="data-row">
              <span class="label">Bet Amount:</span>
              <span class="value">$${programData.betAmount || 0}</span>
            </div>
          </div>

          <div class="section">
            <div class="section-title">Performance Statistics</div>
            <div class="data-row">
              <span class="label">Total Submissions:</span>
              <span class="value">${stats.submissionStats?.totalSubmissions || 0}</span>
            </div>
            <div class="data-row">
              <span class="label">Completion Rate:</span>
              <span class="value">${stats.submissionStats?.completionRate || 0}%</span>
            </div>
          </div>

          <div class="section">
            <div class="section-title">Financial Summary</div>
            <div class="data-row">
              <span class="label">Total Pool:</span>
              <span class="value">$${stats.moneyStats?.totalPool || 0}</span>
            </div>
            <div class="data-row">
              <span class="label">Distributed Amount:</span>
              <span class="value">$${stats.moneyStats?.distributedAmount || 0}</span>
            </div>
          </div>

          <div class="section">
            <div class="section-title">Disqualification Summary</div>
            <div class="data-row">
              <span class="label">Disqualified Players:</span>
              <span class="value">${stats.disqualifiedStats?.count || 0}</span>
            </div>
            <div class="data-row">
              <span class="label">Loser Pool:</span>
              <span class="value">$${stats.disqualifiedStats?.loserPool || 0}</span>
            </div>
          </div>
        </body>
      </html>
    `;
  };

  const handleDownload = async () => {
    try {
      // Show loading indicator or disable button here if needed

      // Generate PDF file
      const { uri } = await Print.printToFileAsync({
        html: generateHTML(),
        base64: false,
        width: 612, // Standard US Letter width in points (8.5 inches * 72)
        height: 792, // Standard US Letter height in points (11 inches * 72)
        margins: {
          left: 36, // 0.5 inch margins
          top: 36,
          right: 36,
          bottom: 36
        }
      });

      // Check if sharing is available
      const canShare = await Sharing.isAvailableAsync();
      
      if (canShare) {
        // Generate filename with date
        const date = new Date().toISOString().split('T')[0];
        const filename = `${programData.name || 'Program'}_Report_${date}.pdf`;

        // Share the PDF
        await Sharing.shareAsync(uri, {
          UTI: '.pdf',
          mimeType: 'application/pdf',
          dialogTitle: 'Download Program Report'
        });
      } else {
        Alert.alert(
          "Error",
          "Sharing is not available on this device"
        );
      }
    } catch (error) {
      console.error('Error generating PDF:', error);
      Alert.alert(
        "Error",
        "Failed to generate PDF report. Please try again."
      );
    }
  };

  return (
    <TouchableOpacity style={styles.container} onPress={handleDownload}>
      <View style={styles.content}>
        <Text style={styles.mainText}>Download Report</Text>
        <Text style={styles.subText}>as PDF</Text>
      </View>
      <MaterialIcons name="file-download" size={24} color={colors.primary} />
    </TouchableOpacity>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    backgroundColor: colors.card,
    borderRadius: 8,
    padding: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: colors.primary,
    height: '100%',
  },
  content: {
    justifyContent: 'center',
  },
  mainText: {
    fontSize: 12,
    fontFamily: 'MontserratBold',
    color: colors.primary,
    marginBottom: 2,
  },
  subText: {
    fontSize: 9,
    fontFamily: 'MontserratRegular',
    color: colors.text,
    opacity: 0.8,
  },
});

export default DownloadReport;








