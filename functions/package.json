{"name": "habit-royale-functions", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "20"}, "main": "lib/index.js", "dependencies": {"expo-server-sdk": "^3.7.0", "firebase-admin": "^12.0.0", "firebase-functions": "^6.3.2"}, "devDependencies": {"@types/node": "^18.19.117", "typescript": "^4.9.5"}, "private": true}