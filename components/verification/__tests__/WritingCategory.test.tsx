import { getVerificationConfig, VERIFICATION_CONFIGS } from '../../../types/verification';

describe('Writing Category Integration', () => {
  it('should return camera-only verification config for writing category', () => {
    const config = getVerificationConfig('writing');
    
    expect(config).toEqual(VERIFICATION_CONFIGS.CAMERA_ONLY);
    expect(config.type).toBe('camera');
    expect(config.camera?.allowMultiple).toBe(false);
    expect(config.camera?.allowsEditing).toBe(true);
  });

  it('should handle unknown categories with default camera-only config', () => {
    const config = getVerificationConfig('unknown-category');
    
    expect(config).toEqual(VERIFICATION_CONFIGS.CAMERA_ONLY);
    expect(config.type).toBe('camera');
  });

  it('should have writing category in the verification mapping', () => {
    const { CATEGORY_VERIFICATION_MAP } = require('../../../types/verification');
    
    expect(CATEGORY_VERIFICATION_MAP).toHaveProperty('writing');
    expect(CATEGORY_VERIFICATION_MAP.writing).toEqual(VERIFICATION_CONFIGS.CAMERA_ONLY);
  });

  it('should group writing with other camera-only categories', () => {
    const writingConfig = getVerificationConfig('writing');
    const affirmationsConfig = getVerificationConfig('affirmations');
    const journalingConfig = getVerificationConfig('journaling');
    
    expect(writingConfig.type).toBe(affirmationsConfig.type);
    expect(writingConfig.type).toBe(journalingConfig.type);
    expect(writingConfig.type).toBe('camera');
  });

  it('should be different from gym and text-based categories', () => {
    const writingConfig = getVerificationConfig('writing');
    const gymConfig = getVerificationConfig('gym');
    const cardioConfig = getVerificationConfig('cardio');
    
    expect(writingConfig.type).not.toBe(gymConfig.type);
    expect(writingConfig.type).not.toBe(cardioConfig.type);
    expect(writingConfig.type).toBe('camera');
    expect(gymConfig.type).toBe('camera+gps');
    expect(cardioConfig.type).toBe('text');
  });
});
