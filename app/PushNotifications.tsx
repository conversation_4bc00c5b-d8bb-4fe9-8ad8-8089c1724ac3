import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  Switch,
  TouchableOpacity,
  Alert,
  ScrollView,
} from "react-native";
import { useRouter } from "expo-router";
import * as Notifications from "expo-notifications";

const PushNotifications: React.FC = () => {
  const router = useRouter();
  
  // Always-on notifications (untogglable)
  const alwaysOnValue = true;
  
  // Togglable notifications
  const [programAlerts, setProgramAlerts] = useState(true);
  const [marketingNotifications, setMarketingNotifications] = useState(false);
  const [chatRoomNotifications, setChatRoomNotifications] = useState(false);

  // State to track notification permissions
  const [permissionGranted, setPermissionGranted] = useState(false);

  useEffect(() => {
    const checkPermissions = async () => {
      const { status } = await Notifications.getPermissionsAsync();
      console.log("Current permission status:", status);
      setPermissionGranted(status === "granted");
    };
    checkPermissions();
  }, []);

  const requestPermission = async () => {
    const { status } = await Notifications.requestPermissionsAsync();
    if (status === "granted") {
      setPermissionGranted(true);
      Alert.alert("Permission Granted", "You will now receive notifications.");
    } else {
      Alert.alert("Permission Denied", "Notifications are disabled.");
    }
  };

  const testNotification = async () => {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title: "Test Notification",
          body: "This is a sample notification.",
        },
        trigger: null, // fires immediately
      });
      Alert.alert("Notification Sent", "A test notification was sent.");
    } catch (error) {
      console.error("Error sending test notification:", error);
      Alert.alert("Error", "Failed to send test notification.");
    }
  };

  return (
    <ScrollView contentContainerStyle={styles.scrollContainer}>
      {/* Extra top margin to avoid collision with camera view */}
      <View style={styles.topSpacer} />
      
      <Text style={styles.header}>Push Notifications</Text>
      
      {/* Notification Settings List */}
      <View style={styles.notificationItem}>
        <Text style={styles.label}>Critical Program Alerts</Text>
        <Switch value={alwaysOnValue} disabled />
      </View>
      <View style={styles.notificationItem}>
        <Text style={styles.label}>Reminders</Text>
        <Switch value={alwaysOnValue} disabled />
      </View>
      <View style={styles.notificationItem}>
        <Text style={styles.label}>Program Alerts</Text>
        <Switch value={programAlerts} onValueChange={setProgramAlerts} />
      </View>
      <View style={styles.notificationItem}>
        <Text style={styles.label}>Marketing Notifications</Text>
        <Switch
          value={marketingNotifications}
          onValueChange={setMarketingNotifications}
        />
      </View>
      <View style={styles.notificationItem}>
        <Text style={styles.label}>Chat Room Notifications</Text>
        <Switch
          value={chatRoomNotifications}
          onValueChange={setChatRoomNotifications}
        />
      </View>
      
      {/* Back Button */}
      <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
        <Text style={styles.backButtonText}>Back</Text>
      </TouchableOpacity>
      
      {/* Permission Banner fixed at the bottom */}
      {!permissionGranted && (
        <View style={styles.permissionBannerWrapper}>
          <View style={styles.permissionBanner}>
            <Text style={styles.permissionText}>
              Please enable notifications to receive updates.
            </Text>
            <TouchableOpacity
              style={styles.permissionButton}
              onPress={requestPermission}
            >
              <Text style={styles.permissionButtonText}>Grant Permission</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </ScrollView>
  );
};

export default PushNotifications;

const styles = StyleSheet.create({
  scrollContainer: {
    padding: 20,
    paddingBottom: 80, // extra bottom padding so the permission banner doesn't cover content
  },
  topSpacer: {
    height: 100, // Adjust this value to push content down below the camera area
  },
  header: {
    fontSize: 26,
    color: "#FFCC00",
    fontWeight: "bold",
    textAlign: "center",
    marginBottom: 20,
  },
  notificationItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "#1F1F1F",
    padding: 15,
    borderRadius: 10,
    marginBottom: 15,
  },
  label: {
    fontSize: 16,
    color: "white",
  },
  backButton: {
    marginTop: 30,
    alignSelf: "center",
    backgroundColor: "#2A2A2A",
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  backButtonText: {
    color: "#FFEB3B",
    fontSize: 16,
    fontWeight: "bold",
  },
  testButton: {
    marginTop: 15,
    alignSelf: "center",
    backgroundColor: "#FFEB3B",
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  testButtonText: {
    color: "black",
    fontSize: 16,
    fontWeight: "bold",
  },
  permissionBannerWrapper: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
    backgroundColor: "transparent",
  },
  permissionBanner: {
    backgroundColor: "#E53935",
    padding: 15,
    borderRadius: 10,
    alignItems: "center",
  },
  permissionText: {
    color: "white",
    fontSize: 16,
    marginBottom: 10,
  },
  permissionButton: {
    backgroundColor: "#FFEB3B",
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  permissionButtonText: {
    color: "black",
    fontSize: 16,
    fontWeight: "bold",
  },
});
