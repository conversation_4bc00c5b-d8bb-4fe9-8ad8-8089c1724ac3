import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  StyleSheet
} from "react-native";
import { useTheme } from "@/contexts/ThemeContext";

interface BailoutModalProps {
  onSubmitBailout: () => Promise<string>; // Should return a success message
  onClose: () => void;
}

const BailoutModal: React.FC<BailoutModalProps> = ({ onSubmitBailout, onClose }) => {
  const { colors } = useTheme();
  const [loading, setLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const handleSubmit = async () => {
    setLoading(true);
    setSuccessMessage(null);
    try {
      // Execute the bailout logic and get a success message.
      const message = await onSubmitBailout();
      setSuccessMessage(message);
    } catch (error) {
      setSuccessMessage("Error processing your request. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const styles = createStyles(colors);

  return (
    <View style={styles.modalContainer}>
      {successMessage ? (
        // Display success message within the modal
        <Text style={styles.successText}>{successMessage}</Text>
      ) : (
        <>
          <Text style={styles.modalTitle}>Bailout Request</Text>
          <Text style={styles.modalMessage}>
            Are you sure you want to bailout? This action is irreversible.
          </Text>
          <TouchableOpacity
            style={[styles.modalButton, loading && styles.disabledButton]}
            disabled={loading}
            onPress={handleSubmit}
          >
            {loading ? (
              <ActivityIndicator size="small" color={colors.text} />
            ) : (
              <Text style={styles.modalButtonText}>Submit Bailout</Text>
            )}
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.modalButton, loading && styles.disabledButton]}
            disabled={loading}
            onPress={onClose}
          >
            <Text style={styles.modalButtonText}>Cancel</Text>
          </TouchableOpacity>
        </>
      )}
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  modalContainer: {
    backgroundColor: colors.card,
    padding: 20,
    borderRadius: 12,
    alignItems: "center",
  },
  modalTitle: {
    fontSize: 20,
    color: colors.primary,
    fontWeight: "bold",
    marginBottom: 10,
  },
  modalMessage: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: "center",
    marginBottom: 20,
  },
  modalButton: {
    backgroundColor: colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginTop: 10,
  },
  modalButtonText: {
    color: colors.background,
    fontSize: 16,
    fontWeight: "bold",
  },
  disabledButton: {
    opacity: 0.7,
  },
  successText: {
    fontSize: 18,
    color: colors.success,
    textAlign: "center",
  },
});

export default BailoutModal;
