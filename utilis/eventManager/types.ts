// Event Management System Types and Interfaces

export enum EventType {
  // User Events
  USER_SIGNUP = 'user.signup',
  USER_LOGIN = 'user.login',
  USER_LOGOUT = 'user.logout',
  USER_PROFILE_UPDATE = 'user.profile_update',
  USER_EMAIL_VERIFICATION = 'user.email_verification',
  
  // Program Events
  PROGRAM_JOIN = 'program.join',
  PROGRAM_LEAVE = 'program.leave',
  PROGRAM_START = 'program.start',
  PROGRAM_END = 'program.end',
  PROGRAM_SETUP_COMPLETE = 'program.setup_complete',
  PROGRAM_SUBMISSION = 'program.submission',
  PROGRAM_MILESTONE = 'program.milestone',
  
  // Payment Events
  PAYMENT_INITIATED = 'payment.initiated',
  PAYMENT_SUCCESS = 'payment.success',
  PAYMENT_FAILED = 'payment.failed',
  PAYMENT_REFUND = 'payment.refund',
  
  // Notification Events
  NOTIFICATION_SENT = 'notification.sent',
  NOTIFICATION_READ = 'notification.read',
  NOTIFICATION_FAILED = 'notification.failed',
  
  // System Events
  SYSTEM_ERROR = 'system.error',
  SYSTEM_MAINTENANCE = 'system.maintenance',
  SYSTEM_UPDATE = 'system.update',
  
  // Dispute Events
  DISPUTE_CREATED = 'dispute.created',
  DISPUTE_RESOLVED = 'dispute.resolved',
  DISPUTE_ESCALATED = 'dispute.escalated',
  
  // Achievement Events
  ACHIEVEMENT_UNLOCKED = 'achievement.unlocked',
  STREAK_MILESTONE = 'streak.milestone',
  POINTS_EARNED = 'points.earned',
}

export enum EventPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export enum NotificationType {
  ACCOUNT = 'account',
  PROGRAM = 'program',
  POINTS = 'points',
  REMINDER = 'reminder',
  SYSTEM = 'system',
  ACHIEVEMENT = 'achievement',
}

export interface BaseEventData {
  userId?: string;
  userEmail?: string;
  timestamp: string;
  source: string; // Which part of the app triggered this event
  metadata?: Record<string, any>;
}

export interface UserEventData extends BaseEventData {
  firstName?: string;
  lastName?: string;
  deviceInfo?: {
    platform: string;
    version?: string;
  };
}

export interface ProgramEventData extends BaseEventData {
  programId: string;
  programName: string;
  programCategory?: string;
  dayNumber?: number;
  submissionStatus?: string;
}

export interface PaymentEventData extends BaseEventData {
  paymentIntentId: string;
  amount: number;
  currency: string;
  paymentMethod?: string;
  programId?: string;
}

export interface NotificationEventData extends BaseEventData {
  notificationId?: string;
  title: string;
  message: string;
  type: NotificationType;
  priority: EventPriority;
  deliveryMethod?: 'firebase' | 'push' | 'email' | 'sms';
}

export interface SystemEventData extends BaseEventData {
  errorCode?: string;
  errorMessage?: string;
  stackTrace?: string;
  affectedUsers?: string[];
}

export interface DisputeEventData extends BaseEventData {
  disputeId: string;
  programId: string;
  subject: string;
  status: string;
}

export interface AchievementEventData extends BaseEventData {
  achievementType: string;
  achievementName: string;
  points?: number;
  streakCount?: number;
}

export type EventData = 
  | UserEventData 
  | ProgramEventData 
  | PaymentEventData 
  | NotificationEventData 
  | SystemEventData 
  | DisputeEventData 
  | AchievementEventData;

export interface Event {
  id: string;
  type: EventType;
  priority: EventPriority;
  data: EventData;
  createdAt: string;
  processedAt?: string;
  retryCount?: number;
  maxRetries?: number;
}

export interface EventHandler {
  name: string;
  handle: (event: Event) => Promise<void>;
  shouldHandle: (event: Event) => boolean;
  priority: number; // Lower numbers = higher priority
}

export interface EventMiddleware {
  name: string;
  process: (event: Event) => Promise<Event | null>; // Return null to stop processing
  order: number; // Processing order
}

export interface NotificationConfig {
  enabled: boolean;
  types: NotificationType[];
  priorities: EventPriority[];
  deliveryMethods: string[];
  quietHours?: {
    start: string; // HH:MM format
    end: string;   // HH:MM format
    timezone: string;
  };
}

export interface EventManagerConfig {
  maxRetries: number;
  retryDelay: number; // milliseconds
  batchSize: number;
  enableAnalytics: boolean;
  enableLogging: boolean;
  firebaseConfig: {
    collection: string;
    enablePersistence: boolean;
    enableRealtime: boolean;
  };
  notificationConfig: NotificationConfig;
}

export interface EventStats {
  totalEvents: number;
  eventsByType: Record<EventType, number>;
  eventsByPriority: Record<EventPriority, number>;
  successRate: number;
  averageProcessingTime: number;
  failedEvents: number;
  retryEvents: number;
}

export interface FirebaseEventDocument {
  id: string;
  type: EventType;
  priority: EventPriority;
  userId?: string;
  userEmail?: string;
  data: Record<string, any>;
  createdAt: string;
  processedAt?: string;
  status: 'pending' | 'processed' | 'failed' | 'retrying';
  retryCount: number;
  handlers: string[]; // Names of handlers that processed this event
  errors?: string[];
}
