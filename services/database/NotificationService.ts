import { BaseService } from './BaseService';
import {
  Notification,
  ServiceResponse,
  QueryOptions,
  COLLECTIONS,
  SUBCOLLECTIONS,
  UnsubscribeFunction,
} from './types';

export class NotificationService extends BaseService {
  private getNotificationPath(userId: string): string {
    return `${COLLECTIONS.USERS}/${userId}/${SUBCOLLECTIONS.USER_NOTIFICATIONS}`;
  }

  // Notification CRUD operations
  async createNotification(
    userId: string,
    notificationData: Omit<Notification, 'id' | 'time' | 'read'>
  ): Promise<ServiceResponse<string>> {
    const collectionPath = this.getNotificationPath(userId);
    const fullNotificationData: Omit<Notification, 'id'> = {
      ...notificationData,
      time: new Date().toISOString(),
      read: false,
    };
    return this.createDocument<Notification>(collectionPath, fullNotificationData);
  }

  async getNotification(
    userId: string,
    notificationId: string
  ): Promise<ServiceResponse<Notification | null>> {
    const collectionPath = this.getNotificationPath(userId);
    return this.getDocument<Notification>(collectionPath, notificationId);
  }

  async updateNotification(
    userId: string,
    notificationId: string,
    notificationData: Partial<Notification>
  ): Promise<ServiceResponse<void>> {
    const collectionPath = this.getNotificationPath(userId);
    return this.updateDocument<Notification>(collectionPath, notificationId, notificationData);
  }

  async deleteNotification(userId: string, notificationId: string): Promise<ServiceResponse<void>> {
    const collectionPath = this.getNotificationPath(userId);
    return this.deleteDocument(collectionPath, notificationId);
  }

  // Notification management
  async markAsRead(userId: string, notificationId: string): Promise<ServiceResponse<void>> {
    return this.updateNotification(userId, notificationId, { read: true });
  }

  async markAsUnread(userId: string, notificationId: string): Promise<ServiceResponse<void>> {
    return this.updateNotification(userId, notificationId, { read: false });
  }

  async markAllAsRead(userId: string): Promise<ServiceResponse<void>> {
    try {
      const unreadResult = await this.getUnreadNotifications(userId);
      if (!unreadResult.success || !unreadResult.data) {
        return this.createErrorResponse(unreadResult as any);
      }

      const operations = unreadResult.data.map(notification => ({
        type: 'update' as const,
        collection: this.getNotificationPath(userId),
        documentId: notification.id!,
        data: { read: true },
      }));

      const result = await this.executeBatch(operations);
      if (result.success) {
        return this.createSuccessResponse(undefined);
      } else {
        return this.createErrorResponse({
          operation: 'update',
          collection: this.getNotificationPath(userId),
          originalError: new Error(result.error || 'Failed to mark all as read'),
          timestamp: new Date(),
        });
      }
    } catch (error) {
      const dbError = this.handleError('update', this.getNotificationPath(userId), error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  // Notification querying
  async getAllNotifications(userId: string): Promise<ServiceResponse<Notification[]>> {
    const collectionPath = this.getNotificationPath(userId);
    const options: QueryOptions = {
      orderBy: { field: 'time', direction: 'desc' }
    };
    return this.getCollection<Notification>(collectionPath, options);
  }

  async getUnreadNotifications(userId: string): Promise<ServiceResponse<Notification[]>> {
    try {
      // Get all notifications and filter client-side to avoid index requirements
      const allResult = await this.getAllNotifications(userId);
      if (!allResult.success) {
        return this.createErrorResponse(allResult as any);
      }

      const unreadNotifications = (allResult.data || [])
        .filter(notification => !notification.read)
        .sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime());

      return this.createSuccessResponse(unreadNotifications);
    } catch (error) {
      const dbError = this.handleError('read', this.getNotificationPath(userId), error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  async getNotificationsByType(
    userId: string,
    type: Notification['type']
  ): Promise<ServiceResponse<Notification[]>> {
    try {
      // Get all notifications and filter client-side to avoid index requirements
      const allResult = await this.getAllNotifications(userId);
      if (!allResult.success) {
        return this.createErrorResponse(allResult as any);
      }

      const filteredNotifications = (allResult.data || [])
        .filter(notification => notification.type === type)
        .sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime());

      return this.createSuccessResponse(filteredNotifications);
    } catch (error) {
      const dbError = this.handleError('read', this.getNotificationPath(userId), error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  async getNotificationsByPriority(
    userId: string,
    priority: Notification['priority']
  ): Promise<ServiceResponse<Notification[]>> {
    try {
      // Get all notifications and filter client-side to avoid index requirements
      const allResult = await this.getAllNotifications(userId);
      if (!allResult.success) {
        return this.createErrorResponse(allResult as any);
      }

      const filteredNotifications = (allResult.data || [])
        .filter(notification => notification.priority === priority)
        .sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime());

      return this.createSuccessResponse(filteredNotifications);
    } catch (error) {
      const dbError = this.handleError('read', this.getNotificationPath(userId), error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  async getRecentNotifications(
    userId: string,
    limitCount: number = 20
  ): Promise<ServiceResponse<Notification[]>> {
    const collectionPath = this.getNotificationPath(userId);
    const options: QueryOptions = {
      orderBy: { field: 'time', direction: 'desc' },
      limit: limitCount
    };
    return this.getCollection<Notification>(collectionPath, options);
  }

  // Notification statistics
  async getUnreadCount(userId: string): Promise<ServiceResponse<number>> {
    const result = await this.getUnreadNotifications(userId);
    if (!result.success) {
      return this.createErrorResponse(result as any);
    }
    return this.createSuccessResponse(result.data?.length || 0);
  }

  async getNotificationStats(userId: string): Promise<ServiceResponse<{
    total: number;
    unread: number;
    byType: Record<Notification['type'], number>;
    byPriority: Record<Notification['priority'], number>;
  }>> {
    try {
      const allResult = await this.getAllNotifications(userId);
      if (!allResult.success) {
        return this.createErrorResponse(allResult as any);
      }

      const notifications = allResult.data || [];
      const total = notifications.length;
      const unread = notifications.filter(n => !n.read).length;

      const byType: Record<Notification['type'], number> = {
        account: 0,
        program: 0,
        points: 0,
        reminder: 0,
      };

      const byPriority: Record<Notification['priority'], number> = {
        low: 0,
        medium: 0,
        high: 0,
      };

      notifications.forEach(notification => {
        byType[notification.type]++;
        byPriority[notification.priority]++;
      });

      const stats = {
        total,
        unread,
        byType,
        byPriority,
      };

      return this.createSuccessResponse(stats);
    } catch (error) {
      const dbError = this.handleError('read', this.getNotificationPath(userId), error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  // Real-time subscriptions
  subscribeToNotifications(
    userId: string,
    callback: (notifications: Notification[]) => void
  ): UnsubscribeFunction {
    const collectionPath = this.getNotificationPath(userId);
    const options: QueryOptions = {
      orderBy: { field: 'time', direction: 'desc' }
    };
    return this.subscribeToCollection<Notification>(collectionPath, callback, options);
  }

  subscribeToUnreadNotifications(
    userId: string,
    callback: (notifications: Notification[]) => void
  ): UnsubscribeFunction {
    // Subscribe to all notifications and filter client-side to avoid index requirements
    return this.subscribeToNotifications(userId, (allNotifications) => {
      const unreadNotifications = allNotifications
        .filter(notification => !notification.read)
        .sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime());
      callback(unreadNotifications);
    });
  }

  subscribeToUnreadCount(
    userId: string,
    callback: (count: number) => void
  ): UnsubscribeFunction {
    return this.subscribeToUnreadNotifications(userId, (notifications) => {
      callback(notifications.length);
    });
  }

  // Bulk operations
  async deleteAllNotifications(userId: string): Promise<ServiceResponse<void>> {
    try {
      const allResult = await this.getAllNotifications(userId);
      if (!allResult.success || !allResult.data) {
        return this.createErrorResponse(allResult as any);
      }

      const operations = allResult.data.map(notification => ({
        type: 'delete' as const,
        collection: this.getNotificationPath(userId),
        documentId: notification.id!,
      }));

      const result = await this.executeBatch(operations);
      if (result.success) {
        return this.createSuccessResponse(undefined);
      } else {
        return this.createErrorResponse({
          operation: 'delete',
          collection: this.getNotificationPath(userId),
          originalError: new Error(result.error || 'Failed to delete all notifications'),
          timestamp: new Date(),
        });
      }
    } catch (error) {
      const dbError = this.handleError('delete', this.getNotificationPath(userId), error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  async deleteReadNotifications(userId: string): Promise<ServiceResponse<void>> {
    try {
      const allResult = await this.getAllNotifications(userId);
      if (!allResult.success || !allResult.data) {
        return this.createErrorResponse(allResult as any);
      }

      const readNotifications = allResult.data.filter(n => n.read);
      const operations = readNotifications.map(notification => ({
        type: 'delete' as const,
        collection: this.getNotificationPath(userId),
        documentId: notification.id!,
      }));

      if (operations.length === 0) {
        return this.createSuccessResponse(undefined);
      }

      const result = await this.executeBatch(operations);
      if (result.success) {
        return this.createSuccessResponse(undefined);
      } else {
        return this.createErrorResponse({
          operation: 'delete',
          collection: this.getNotificationPath(userId),
          originalError: new Error(result.error || 'Failed to delete read notifications'),
          timestamp: new Date(),
        });
      }
    } catch (error) {
      const dbError = this.handleError('delete', this.getNotificationPath(userId), error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  // Helper methods for common notification types
  async createWelcomeNotification(userId: string, userName: string): Promise<ServiceResponse<string>> {
    return this.createNotification(userId, {
      title: "Welcome to Habit Royale!",
      message: `Hello ${userName}! Your account has been created successfully! Welcome onboard!`,
      type: "account",
      priority: "high"
    });
  }

  async createProgramJoinedNotification(
    userId: string,
    programName: string,
    startDate: string
  ): Promise<ServiceResponse<string>> {
    return this.createNotification(userId, {
      title: "Program Joined Successfully!",
      message: `You've joined "${programName}". The challenge begins on ${startDate}. Get ready!`,
      type: "program",
      priority: "medium"
    });
  }

  async createSetupReminderNotification(
    userId: string,
    programName: string
  ): Promise<ServiceResponse<string>> {
    return this.createNotification(userId, {
      title: "Set up your Account Now",
      message: `Go to Progress and connect your account for "${programName}" now!`,
      type: "program",
      priority: "high"
    });
  }

  async createSetupCompleteNotification(
    userId: string,
    category: string
  ): Promise<ServiceResponse<string>> {
    return this.createNotification(userId, {
      title: "Account Setup Complete!",
      message: `Your ${category} account is now connected and ready to go. Good luck for the challenge!`,
      type: "program",
      priority: "high"
    });
  }

  async createDisputeSubmittedNotification(
    userId: string,
    subject: string,
    programName: string
  ): Promise<ServiceResponse<string>> {
    return this.createNotification(userId, {
      title: "Dispute Submitted Successfully",
      message: `Your dispute regarding "${subject}" for ${programName} has been recorded. We'll review it shortly.`,
      type: "program",
      priority: "medium"
    });
  }
}
