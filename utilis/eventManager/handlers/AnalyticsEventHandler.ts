import { firestoreService } from '../../../services/database';
import { Event, EventHandler, EventType, EventPriority } from '../types';
import { getId } from '../../variables';

interface AnalyticsData {
  eventType: EventType;
  priority: EventPriority;
  timestamp: string;
  source: string;
  metadata: Record<string, any>;
  processingTime?: number;
  success: boolean;
}

interface DailyStats {
  date: string;
  totalEvents: number;
  eventsByType: Record<EventType, number>;
  eventsByPriority: Record<EventPriority, number>;
  uniqueUsers: Set<string>;
  averageProcessingTime: number;
  successRate: number;
  errors: number;
}

export class AnalyticsEventHandler implements EventHandler {
  public readonly name = 'AnalyticsEventHandler';
  public readonly priority = 3; // Lower priority for analytics

  private dailyStats: Map<string, DailyStats> = new Map();
  private batchedAnalytics: AnalyticsData[] = [];
  private batchTimeout: NodeJS.Timeout | null = null;
  private readonly batchSize = 20;
  private readonly batchDelay = 5000; // 5 seconds

  public shouldHandle(event: Event): boolean {
    // Handle all events for analytics, except system errors to avoid infinite loops
    return event.type !== EventType.SYSTEM_ERROR;
  }

  public async handle(event: Event): Promise<void> {
    const startTime = Date.now();
    
    try {
      const analyticsData: AnalyticsData = {
        eventType: event.type,
        priority: event.priority,
        timestamp: event.createdAt,
        source: event.data.source || 'unknown',
        metadata: this.extractMetadata(event),
        success: true,
      };

      // Handle specific event types
      await this.handleSpecificEventType(event, analyticsData);

      // Add to batch
      this.batchedAnalytics.push(analyticsData);

      // Update in-memory stats
      this.updateDailyStats(analyticsData);

      // Process batch if full
      if (this.batchedAnalytics.length >= this.batchSize) {
        await this.processBatch();
      } else {
        this.scheduleBatchProcessing();
      }

      // Track processing time
      analyticsData.processingTime = Date.now() - startTime;
      
    } catch (error) {
      console.error(`AnalyticsEventHandler failed for event ${event.id}:`, error);
      
      // Track the error
      await this.trackError(event, error);
      throw error;
    }
  }

  private async handleSpecificEventType(event: Event, analyticsData: AnalyticsData): Promise<void> {
    try {
      switch (event.type) {
        case EventType.USER_LOGIN:
          // Record user login in the userLogin subcollection
          if (event.data.userEmail) {
            await firestoreService.analytics.recordUserLogin(event.data.userEmail, {
              loginTime: event.data.metadata?.loginTime || event.createdAt,
              platform: event.data.deviceInfo?.platform || 'unknown',
              deviceInfo: event.data.deviceInfo,
              success: true,
            });
          }
          break;

        // Add other specific event type handling here as needed
        default:
          // No specific handling needed
          break;
      }
    } catch (error) {
      console.error(`Failed to handle specific event type ${event.type}:`, error);
      // Don't throw error to avoid breaking the analytics flow
    }
  }

  private extractMetadata(event: Event): Record<string, any> {
    const metadata: Record<string, any> = {
      eventId: event.id,
      retryCount: event.retryCount || 0,
    };

    // Extract relevant metadata based on event type
    switch (event.type) {
      case EventType.PROGRAM_JOIN:
      case EventType.PROGRAM_LEAVE:
        metadata.programId = event.data.programId;
        metadata.programName = event.data.programName;
        break;
      
      case EventType.PAYMENT_SUCCESS:
      case EventType.PAYMENT_FAILED:
        metadata.paymentIntentId = event.data.paymentIntentId;
        metadata.amount = event.data.amount;
        metadata.currency = event.data.currency;
        break;
      
      case EventType.USER_LOGIN:
        metadata.deviceInfo = event.data.deviceInfo;
        break;
    }

    return metadata;
  }

  private updateDailyStats(analyticsData: AnalyticsData): void {
    const date = new Date(analyticsData.timestamp).toISOString().split('T')[0];
    
    let stats = this.dailyStats.get(date);
    if (!stats) {
      stats = {
        date,
        totalEvents: 0,
        eventsByType: {} as Record<EventType, number>,
        eventsByPriority: {} as Record<EventPriority, number>,
        uniqueUsers: new Set(),
        averageProcessingTime: 0,
        successRate: 100,
        errors: 0,
      };
      this.dailyStats.set(date, stats);
    }

    stats.totalEvents++;
    stats.eventsByType[analyticsData.eventType] = (stats.eventsByType[analyticsData.eventType] || 0) + 1;
    stats.eventsByPriority[analyticsData.priority] = (stats.eventsByPriority[analyticsData.priority] || 0) + 1;
    
    if (analyticsData.userEmail) {
      stats.uniqueUsers.add(analyticsData.userEmail);
    }

    if (analyticsData.processingTime) {
      stats.averageProcessingTime = 
        (stats.averageProcessingTime * (stats.totalEvents - 1) + analyticsData.processingTime) / stats.totalEvents;
    }

    if (!analyticsData.success) {
      stats.errors++;
      stats.successRate = ((stats.totalEvents - stats.errors) / stats.totalEvents) * 100;
    }
  }

  private scheduleBatchProcessing(): void {
    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
    }

    this.batchTimeout = setTimeout(() => {
      this.processBatch();
    }, this.batchDelay);
  }

  private async processBatch(): Promise<void> {
    if (this.batchedAnalytics.length === 0) return;

    const batch = [...this.batchedAnalytics];
    this.batchedAnalytics = [];

    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
      this.batchTimeout = null;
    }

    try {
      // Store individual analytics records
      await this.storeAnalyticsBatch(batch);
      
      // Update aggregated stats
      await this.updateAggregatedStats();
      
      console.log(`AnalyticsEventHandler: Processed batch of ${batch.length} analytics records`);
    } catch (error) {
      console.error('AnalyticsEventHandler: Batch processing failed:', error);
      // Re-add failed analytics to batch for retry
      this.batchedAnalytics.unshift(...batch);
      throw error;
    }
  }

  private async storeAnalyticsBatch(batch: AnalyticsData[]): Promise<void> {
    try {
      // Group analytics by user
      const userAnalytics: Record<string, AnalyticsData[]> = {};

      for (const data of batch) {
        // Get user ID from metadata or current user
        const userId = data.metadata.userId || await getId();
        if (userId) {
          if (!userAnalytics[userId]) {
            userAnalytics[userId] = [];
          }
          userAnalytics[userId].push(data);
        } else {
          console.warn('Analytics record skipped - no user ID available:', data);
        }
      }

      // Store analytics records in user subcollections
      const promises = Object.entries(userAnalytics).map(([userId, analytics]) =>
        firestoreService.analytics.createAnalyticsBatch(userId, analytics)
      );

      const results = await Promise.all(promises);

      // Check for any failures
      results.forEach((result, index) => {
        if (!result.success) {
          console.error(`Failed to store analytics batch for user ${Object.keys(userAnalytics)[index]}:`, result.error);
        }
      });

    } catch (error) {
      console.error('Error in storeAnalyticsBatch:', error);
      throw error;
    }
  }

  private async updateAggregatedStats(): Promise<void> {
    for (const [date, stats] of this.dailyStats.entries()) {
      try {
        // Update daily analytics for each user who had events on this date
        const userPromises = Array.from(stats.uniqueUsers).map(async (userId) => {
          try {
            const result = await firestoreService.analytics.updateDailyAnalytics(
              userId,
              date,
              {
                totalEvents: stats.totalEvents,
                eventsByType: stats.eventsByType,
                eventsByPriority: stats.eventsByPriority,
                uniqueUsersCount: stats.uniqueUsers.size,
                averageProcessingTime: stats.averageProcessingTime,
                successRate: stats.successRate,
                errors: stats.errors,
                lastUpdated: new Date().toISOString(),
              }
            );

            if (!result.success) {
              console.error(`Failed to update daily analytics for user ${userId}, date ${date}:`, result.error);
            }
          } catch (error) {
            console.error(`Error updating daily analytics for user ${userId}, date ${date}:`, error);
          }
        });

        await Promise.all(userPromises);

      } catch (error) {
        console.error(`Error updating aggregated stats for ${date}:`, error);
      }
    }
  }

  private async trackError(event: Event, error: any): Promise<void> {
    try {
      const errorDoc = doc(collection(db, 'analytics_errors'));
      
      await setDoc(errorDoc, {
        eventId: event.id,
        eventType: event.type,
        userId: event.data.userId,
        userEmail: event.data.userEmail,
        error: error.message || String(error),
        stack: error.stack,
        timestamp: serverTimestamp(),
        eventData: this.sanitizeEventData(event.data),
      });
      
      // Update error counters
      const errorStatsDoc = doc(db, 'analytics_stats', 'errors');
      await updateDoc(errorStatsDoc, {
        totalErrors: increment(1),
        [`errorsByType.${event.type}`]: increment(1),
        lastError: serverTimestamp(),
      });
      
    } catch (trackingError) {
      console.error('Failed to track error:', trackingError);
    }
  }

  private sanitizeAnalyticsData(data: AnalyticsData): Record<string, any> {
    // Remove undefined values and prepare for Firestore
    const sanitized: Record<string, any> = {
      eventType: data.eventType,
      priority: data.priority,
      timestamp: data.timestamp,
      source: data.source,
      success: data.success,
    };

    // Only include userId and userEmail if they have values
    if (data.userId) {
      sanitized.userId = data.userId;
    }
    if (data.userEmail) {
      sanitized.userEmail = data.userEmail;
    }
    if (data.processingTime !== undefined) {
      sanitized.processingTime = data.processingTime;
    }
    if (data.metadata && Object.keys(data.metadata).length > 0) {
      sanitized.metadata = this.sanitizeEventData(data.metadata);
    }

    return sanitized;
  }

  private sanitizeEventData(data: any): Record<string, any> {
    // Remove sensitive information and non-serializable data
    const sanitized: Record<string, any> = {};

    for (const [key, value] of Object.entries(data)) {
      if (key === 'password' || key === 'token' || key === 'secret') {
        continue; // Skip sensitive fields
      }

      if (value !== undefined && value !== null && typeof value !== 'function') {
        if (typeof value === 'object' && value !== null) {
          const nestedSanitized = this.sanitizeEventData(value);
          if (Object.keys(nestedSanitized).length > 0) {
            sanitized[key] = nestedSanitized;
          }
        } else {
          sanitized[key] = value;
        }
      }
    }

    return sanitized;
  }

  // Public methods for analytics queries
  public async getEventStats(days: number = 7): Promise<any> {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - days);

    const stats = {
      totalEvents: 0,
      eventsByType: {} as Record<EventType, number>,
      eventsByPriority: {} as Record<EventPriority, number>,
      uniqueUsers: 0,
      averageProcessingTime: 0,
      successRate: 0,
      errors: 0,
    };

    // Aggregate stats from daily records
    for (const [date, dailyStats] of this.dailyStats.entries()) {
      const statsDate = new Date(date);
      if (statsDate >= startDate && statsDate <= endDate) {
        stats.totalEvents += dailyStats.totalEvents;
        stats.errors += dailyStats.errors;
        
        // Merge event type counts
        for (const [type, count] of Object.entries(dailyStats.eventsByType)) {
          stats.eventsByType[type as EventType] = (stats.eventsByType[type as EventType] || 0) + count;
        }
        
        // Merge priority counts
        for (const [priority, count] of Object.entries(dailyStats.eventsByPriority)) {
          stats.eventsByPriority[priority as EventPriority] = (stats.eventsByPriority[priority as EventPriority] || 0) + count;
        }
      }
    }

    if (stats.totalEvents > 0) {
      stats.successRate = ((stats.totalEvents - stats.errors) / stats.totalEvents) * 100;
    }

    return stats;
  }

  // Force process any pending batched analytics
  public async flush(): Promise<void> {
    if (this.batchedAnalytics.length > 0) {
      await this.processBatch();
    }
  }
}
