import * as Notifications from 'expo-notifications';
import { firestoreService } from '../../../services/database';
import { 
  Event, 
  EventHandler, 
  EventType, 
  EventPriority,
  NotificationEventData,
  UserEventData,
  ProgramEventData,
  PaymentEventData,
  AchievementEventData,
  NotificationType 
} from '../types';

export class NotificationE<PERSON><PERSON>and<PERSON> implements EventHandler {
  public readonly name = 'NotificationEventHandler';
  public readonly priority = 2; // Medium priority

  private readonly enablePushNotifications: boolean;
  private readonly enableFirebaseNotifications: boolean;

  constructor(
    enablePushNotifications: boolean = true,
    enableFirebaseNotifications: boolean = true
  ) {
    this.enablePushNotifications = enablePushNotifications;
    this.enableFirebaseNotifications = enableFirebaseNotifications;
  }

  public shouldHandle(event: Event): boolean {
    // Handle events that should generate notifications
    const notificationEvents = [
      EventType.USER_SIGNUP,
      EventType.USER_LOGIN,
      EventType.PROGRAM_JOIN,
      EventType.PROGRAM_SETUP_COMPLETE,
      EventType.PROGRAM_START,
      EventType.PROGRAM_END,
      EventType.PAYMENT_SUCCESS,
      EventType.PAYMENT_FAILED,
      EventType.DISPUTE_CREATED,
      EventType.ACHIEVEMENT_UNLOCKED,
      EventType.STREAK_MILESTONE,
      EventType.SYSTEM_MAINTENANCE,
    ];

    return notificationEvents.includes(event.type) || event.type.startsWith('notification.');
  }

  public async handle(event: Event): Promise<void> {
    try {
      const notification = this.createNotificationFromEvent(event);
      
      if (!notification) {
        console.log(`No notification created for event ${event.id}`);
        return;
      }

      // Send Firebase notification (in-app)
      if (this.enableFirebaseNotifications && notification.userEmail) {
        await this.sendFirebaseNotification(notification.userEmail, notification);
      }

      // Send push notification
      if (this.enablePushNotifications) {
        await this.sendPushNotification(notification);
      }

      console.log(`Notification sent for event ${event.id}: ${notification.title}`);
    } catch (error) {
      console.error(`NotificationEventHandler failed for event ${event.id}:`, error);
      throw error;
    }
  }

  private createNotificationFromEvent(event: Event): NotificationEventData | null {
    const baseData: any = {
      timestamp: new Date().toISOString(),
      source: 'NotificationEventHandler',
    };

    // Only include userId and userEmail if they have values
    if (event.data.userId) {
      baseData.userId = event.data.userId;
    }
    if (event.data.userEmail) {
      baseData.userEmail = event.data.userEmail;
    }

    switch (event.type) {
      case EventType.USER_SIGNUP:
        const userData = event.data as UserEventData;
        return {
          ...baseData,
          title: 'Welcome to Habit Royale!',
          message: `Hello ${userData.firstName}! Your account has been created successfully! Welcome onboard!`,
          type: NotificationType.ACCOUNT,
          priority: EventPriority.HIGH,
        };

      case EventType.USER_LOGIN:
        const loginData = event.data as UserEventData;
        return {
          ...baseData,
          title: 'New Login Detected',
          message: `Successfully logged in at ${new Date().toLocaleTimeString()} on ${loginData.deviceInfo?.platform || 'unknown'} device`,
          type: NotificationType.ACCOUNT,
          priority: EventPriority.LOW,
        };

      case EventType.PROGRAM_JOIN:
        const programData = event.data as ProgramEventData;
        return {
          ...baseData,
          title: 'Program Joined Successfully!',
          message: `You've joined "${programData.programName}". Get ready for the challenge!`,
          type: NotificationType.PROGRAM,
          priority: EventPriority.MEDIUM,
        };

      case EventType.PROGRAM_SETUP_COMPLETE:
        const setupData = event.data as ProgramEventData;
        return {
          ...baseData,
          title: 'Account Setup Complete!',
          message: `Your ${setupData.programCategory} account is now connected and ready to go. Good luck for the challenge!`,
          type: NotificationType.PROGRAM,
          priority: EventPriority.HIGH,
        };

      case EventType.PAYMENT_SUCCESS:
        const paymentData = event.data as PaymentEventData;
        return {
          ...baseData,
          title: 'Payment Successful!',
          message: `Your payment of ${paymentData.currency.toUpperCase()} ${(paymentData.amount / 100).toFixed(2)} has been processed successfully.`,
          type: NotificationType.ACCOUNT,
          priority: EventPriority.HIGH,
        };

      case EventType.PAYMENT_FAILED:
        const failedPaymentData = event.data as PaymentEventData;
        return {
          ...baseData,
          title: 'Payment Failed',
          message: `Your payment of ${failedPaymentData.currency.toUpperCase()} ${(failedPaymentData.amount / 100).toFixed(2)} could not be processed. Please try again.`,
          type: NotificationType.ACCOUNT,
          priority: EventPriority.HIGH,
        };

      case EventType.ACHIEVEMENT_UNLOCKED:
        const achievementData = event.data as AchievementEventData;
        return {
          ...baseData,
          title: 'Achievement Unlocked! 🏆',
          message: `Congratulations! You've unlocked "${achievementData.achievementName}"${achievementData.points ? ` and earned ${achievementData.points} points!` : ''}`,
          type: NotificationType.ACHIEVEMENT,
          priority: EventPriority.MEDIUM,
        };

      case EventType.STREAK_MILESTONE:
        const streakData = event.data as AchievementEventData;
        return {
          ...baseData,
          title: 'Streak Milestone! 🔥',
          message: `Amazing! You've reached a ${streakData.streakCount}-day streak! Keep it up!`,
          type: NotificationType.ACHIEVEMENT,
          priority: EventPriority.MEDIUM,
        };

      case EventType.DISPUTE_CREATED:
        return {
          ...baseData,
          title: 'Dispute Submitted Successfully',
          message: 'Your dispute has been recorded. We\'ll review it shortly.',
          type: NotificationType.PROGRAM,
          priority: EventPriority.MEDIUM,
        };

      case EventType.SYSTEM_MAINTENANCE:
        return {
          ...baseData,
          title: 'Scheduled Maintenance',
          message: 'The app will undergo maintenance shortly. Some features may be temporarily unavailable.',
          type: NotificationType.SYSTEM,
          priority: EventPriority.HIGH,
        };

      default:
        // Handle direct notification events
        if (event.type === EventType.NOTIFICATION_SENT) {
          return event.data as NotificationEventData;
        }
        return null;
    }
  }

  private async sendFirebaseNotification(
    userEmail: string,
    notification: NotificationEventData
  ): Promise<void> {
    try {
      // Prepare notification data, excluding undefined values
      const notificationData: any = {
        title: notification.title,
        message: notification.message,
        type: notification.type,
        priority: notification.priority,
        source: notification.source,
      };

      // Only include metadata if it exists and has properties
      if (notification.metadata && Object.keys(notification.metadata).length > 0) {
        notificationData.metadata = this.sanitizeMetadata(notification.metadata);
      }

      const result = await firestoreService.notifications.createNotification(userEmail, notificationData);

      if (!result.success) {
        throw new Error(result.error || 'Failed to create notification');
      }
    } catch (error) {
      console.error('Error creating Firebase notification:', error);
      throw error;
    }
  }

  private sanitizeMetadata(metadata: Record<string, any>): Record<string, any> {
    const sanitized: Record<string, any> = {};

    for (const [key, value] of Object.entries(metadata)) {
      if (value !== undefined && value !== null && typeof value !== 'function') {
        if (typeof value === 'object' && value !== null) {
          const nestedSanitized = this.sanitizeMetadata(value);
          if (Object.keys(nestedSanitized).length > 0) {
            sanitized[key] = nestedSanitized;
          }
        } else {
          sanitized[key] = value;
        }
      }
    }

    return sanitized;
  }

  private async sendPushNotification(notification: NotificationEventData): Promise<void> {
    try {
      // Check if push notifications are available and permitted
      const { status } = await Notifications.getPermissionsAsync();
      if (status !== 'granted') {
        console.log('Push notification permission not granted');
        return;
      }

      // Determine if we should send push notification based on priority
      const shouldSendPush = this.shouldSendPushNotification(notification);
      if (!shouldSendPush) {
        return;
      }

      await Notifications.scheduleNotificationAsync({
        content: {
          title: notification.title,
          body: notification.message,
          data: {
            type: notification.type,
            priority: notification.priority,
            userId: notification.userId,
            timestamp: notification.timestamp,
          },
        },
        trigger: null, // Send immediately
      });
    } catch (error) {
      console.error('Error sending push notification:', error);
      // Don't throw error for push notifications to avoid blocking other handlers
    }
  }

  private shouldSendPushNotification(notification: NotificationEventData): boolean {
    // Only send push notifications for high priority or critical notifications
    const pushPriorities = [EventPriority.HIGH, EventPriority.CRITICAL];
    return pushPriorities.includes(notification.priority);
  }

  // Utility method to send custom notifications through the event system
  public static async sendCustomNotification(
    userEmail: string,
    title: string,
    message: string,
    type: NotificationType = NotificationType.SYSTEM,
    priority: EventPriority = EventPriority.MEDIUM,
    metadata?: Record<string, any>
  ): Promise<void> {
    const { EventManager } = await import('../EventManager');
    const eventManager = EventManager.getInstance();

    const notificationData: NotificationEventData = {
      userEmail,
      timestamp: new Date().toISOString(),
      source: 'CustomNotification',
      title,
      message,
      type,
      priority,
      metadata,
    };

    await eventManager.emit(EventType.NOTIFICATION_SENT, notificationData, priority);
  }
}
