import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  Modal,
  Platform,
} from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { MaterialCommunityIcons, MaterialIcons, FontAwesome5 } from "@expo/vector-icons";
import { LinearGradient } from 'expo-linear-gradient';
import { ProtectedRoute } from "@/components/ProtectedRoute";
import { processStripePayment, processGooglePayPayment, processApplePayPayment, PaymentData } from "@/utilis/paymentService";
import { getId, getFname } from "@/utilis/variables";
import { firestoreService } from "../services/database";
import { Program } from "@/types/CommonInterface";
import { useTheme } from "@/contexts/ThemeContext";

// Category icons mapping for sophisticated design with dark background
const categoryIcons: Record<string, JSX.Element> = {
  gym: (
    <MaterialCommunityIcons name="weight-lifter" size={32} color="#000" />
  ),
  cardio: <FontAwesome5 name="running" size={32} color="#000" />,
  coding: <MaterialCommunityIcons name="laptop" size={32} color="#000" />,
  journaling: <FontAwesome5 name="book-open" size={32} color="#000" />,
  affirmations: (
    <MaterialCommunityIcons name="message-text" size={32} color="#000" />
  ),
  writing: <MaterialCommunityIcons name="pencil" size={32} color="#000" />,
};

const PaymentConfirmationComponent: React.FC = () => {
  const { colors } = useTheme();
  const { programId } = useLocalSearchParams<{ programId?: string }>();
  const router = useRouter();
  const [program, setProgram] = useState<Program | null>(null);
  const [loading, setLoading] = useState(true);
  const [termsAgreed, setTermsAgreed] = useState(false);
  const [termsVisible, setTermsVisible] = useState(false);
  const [processingPayment, setProcessingPayment] = useState(false);
  const [userId, setUserId] = useState<string>("");

  useEffect(() => {
    const fetchData = async () => {
      if (!programId) return;

      try {
        // Fetch user ID
        const id = await getId();
        if (id) {
          setUserId(id);
        }

        // Fetch program data
        const programResult = await firestoreService.programs.getProgramById(programId);
        if (programResult.success && programResult.data) {
          setProgram(programResult.data as Program);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        Alert.alert("Error", "Failed to load program details.");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [programId]);

  const handleTermsPress = () => {
    setTermsVisible(true);
  };

  const handleStripePayment = async () => {
    if (!termsAgreed) {
      Alert.alert("Terms Required", "Please agree to the terms and conditions first.");
      return;
    }

    if (!program || !userId) {
      Alert.alert("Error", "Missing program or user information.");
      return;
    }

    setProcessingPayment(true);

    try {
      const paymentData: PaymentData = {
        amount: (program.betAmount || 0) * 100, // Convert to cents
        currency: 'usd',
        description: `Habit Royale - ${program.name}`,
        programId: program.id,
        userId: userId,
      };

      const result = await processStripePayment(paymentData);

      if (result.success) {
        Alert.alert(
          "Payment Successful!",
          "Your payment has been processed. You will be redirected to complete your program signup.",
          [
            {
              text: "Continue",
              onPress: () => router.push(`/ProgramDetails?programId=${programId}&paymentCompleted=true`),
            },
          ]
        );
      } else {
        Alert.alert("Payment Failed", result.error || "An unknown error occurred.");
      }
    } catch (error) {
      console.error("Payment error:", error);
      Alert.alert("Payment Error", "Failed to process payment. Please try again.");
    } finally {
      setProcessingPayment(false);
    }
  };

  const handleGooglePayment = async () => {
    if (!termsAgreed) {
      Alert.alert("Terms Required", "Please agree to the terms and conditions first.");
      return;
    }

    if (!program || !userId) {
      Alert.alert("Error", "Missing program or user information.");
      return;
    }

    setProcessingPayment(true);

    try {
      const paymentData: PaymentData = {
        amount: (program.betAmount || 0) * 100, // Convert to cents
        currency: 'usd',
        description: `Habit Royale - ${program.name}`,
        programId: program.id,
        userId: userId,
      };

      const result = await processGooglePayPayment(paymentData);

      if (result.success) {
        Alert.alert(
          "Payment Successful!",
          "Your payment has been processed. You will be redirected to complete your program signup.",
          [
            {
              text: "Continue",
              onPress: () => router.push(`/ProgramDetails?programId=${programId}&paymentCompleted=true`),
            },
          ]
        );
      } else {
        Alert.alert("Payment Failed", result.error || "An unknown error occurred.");
      }
    } catch (error) {
      console.error("Payment error:", error);
      Alert.alert("Payment Error", "Failed to process payment. Please try again.");
    } finally {
      setProcessingPayment(false);
    }
  };

  const handleJoinWithoutPayment = () => {
    if (!termsAgreed) {
      Alert.alert("Terms Required", "Please agree to the terms and conditions first.");
      return;
    }

    if (!program || !userId) {
      Alert.alert("Error", "Missing program or user information.");
      return;
    }

    Alert.alert(
      "Join Program",
      "You are about to join this program without payment. You will not be eligible for monetary rewards but can still participate in the challenge.",
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Join Program",
          onPress: handleFreeEnrollment,
        },
      ]
    );
  };

  const handleFreeEnrollment = async () => {
    if (!program || !userId) return;
    setProcessingPayment(true);

    try {
      const result = await firestoreService.enrollUserInProgram(userId, program.id, {
        fname: (await getFname()) || 'User',
        paymentDone: false // Free enrollment
      });

      if (result.success) {
        Alert.alert(
          "Success!",
          "You have successfully joined the program for free.",
          [
            {
              text: "Go to Progress",
              onPress: () =>
                router.replace(
                  `/progress?selectedProgramId=${program.id}&t=${Date.now()}`
                ),
            },
          ]
        );
      } else {
        Alert.alert("Error", result.error || "Failed to join program");
      }
    } catch (error) {
      console.error("Error joining program:", error);
      Alert.alert("Error", "Failed to join program. Please try again.");
    } finally {
      setProcessingPayment(false);
    }
  };

  const handleApplePayment = async () => {
    if (!termsAgreed) {
      Alert.alert("Terms Required", "Please agree to the terms and conditions first.");
      return;
    }

    if (!program || !userId) {
      Alert.alert("Error", "Missing program or user information.");
      return;
    }

    setProcessingPayment(true);

    try {
      const paymentData: PaymentData = {
        amount: (program.betAmount || 0) * 100, // Convert to cents
        currency: 'usd',
        description: `Habit Royale - ${program.name}`,
        programId: program.id,
        userId: userId,
      };

      const result = await processApplePayPayment(paymentData);

      if (result.success) {
        Alert.alert(
          "Payment Successful!",
          "Your payment has been processed. You will be redirected to complete your program signup.",
          [
            {
              text: "Continue",
              onPress: () => router.push(`/ProgramDetails?programId=${programId}&paymentCompleted=true`),
            },
          ]
        );
      } else {
        Alert.alert("Payment Failed", result.error || "An unknown error occurred.");
      }
    } catch (error) {
      console.error("Payment error:", error);
      Alert.alert("Payment Error", "Failed to process payment. Please try again.");
    } finally {
      setProcessingPayment(false);
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#FFEB3B" />
      </View>
    );
  }

  if (!program) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Program not found</Text>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Text style={styles.backButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backIcon} onPress={() => router.back()}>
          <MaterialIcons name="arrow-back" size={24} color="#FFEB3B" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Payment Confirmation</Text>
      </View>

      {/* Enhanced Program Overview Card */}
      <LinearGradient
        colors={['#1a1a1a', '#2d2d2d', '#1a1a1a']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.programCard}
      >
        {/* Decorative Background Pattern */}
        <View style={styles.backgroundPattern}>
          <MaterialCommunityIcons name="hexagon-outline" size={120} color="#FFEB3B10" style={styles.patternIcon1} />
          <MaterialCommunityIcons name="circle-outline" size={80} color="#FFEB3B08" style={styles.patternIcon2} />
          <MaterialCommunityIcons name="triangle-outline" size={60} color="#FFEB3B05" style={styles.patternIcon3} />
        </View>

        {/* HEADER with Enhanced Icon and Glow Effect */}
        <View style={styles.cardHeader}>
          <LinearGradient
            colors={['#FFEB3B', '#FFC107', '#FF9800']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.iconContainer}
          >
            <View style={styles.iconGlow}>
              {(program.category && categoryIcons[program.category]) || (
                <MaterialCommunityIcons name="tag" size={32} color="#000" />
              )}
            </View>
          </LinearGradient>

          <View style={styles.cardTextContainer}>
            <Text style={styles.programName} numberOfLines={2} ellipsizeMode="tail">
              {program.name}
            </Text>
            {program.headline ? (
              <Text style={styles.programHeadline} numberOfLines={2} ellipsizeMode="tail">
                {program.headline}
              </Text>
            ) : null}
          </View>

          <LinearGradient
            colors={['#FFEB3B', '#FFC107']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.categoryBadge}
          >
            <Text style={styles.categoryText}>
              {program.category ? program.category.charAt(0).toUpperCase() + program.category.slice(1) : 'General'}
            </Text>
          </LinearGradient>
        </View>

        {/* Enhanced Stats Section */}
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <MaterialCommunityIcons name="calendar-clock" size={20} color="#FFEB3B" />
            <Text style={styles.statLabel}>Duration</Text>
            <Text style={styles.statValue}>{Math.ceil((program.duration || 0) / 7)} Weeks</Text>
          </View>

          <View style={styles.statDivider} />

          <View style={styles.statItem}>
            <MaterialCommunityIcons name="account-group" size={20} color="#FFEB3B" />
            <Text style={styles.statLabel}>Participants</Text>
            <Text style={styles.statValue}>{program.participantsCount}</Text>
          </View>

          <View style={styles.statDivider} />

          <View style={styles.statItem}>
            <MaterialCommunityIcons name="trophy" size={20} color="#FFEB3B" />
            <Text style={styles.statLabel}>Prize Pool</Text>
            <Text style={styles.statValue}>${(program.participantsCount || 0) * (program.betAmount || 0)}</Text>
          </View>
        </View>

        {/* Timeline Section */}
        <View style={styles.timelineContainer}>
          <View style={styles.timelineItem}>
            <View style={styles.timelineIcon}>
              <MaterialCommunityIcons name="play-circle" size={16} color="#4CAF50" />
            </View>
            <View style={styles.timelineContent}>
              <Text style={styles.timelineLabel}>Start Date</Text>
              <Text style={styles.timelineValue}>{program.startDate ? new Date(program.startDate).toLocaleDateString('en-US', {
                weekday: 'short',
                month: 'short',
                day: 'numeric'
              }) : 'TBD'}</Text>
            </View>
          </View>

          <View style={styles.timelineLine} />

          <View style={styles.timelineItem}>
            <View style={styles.timelineIcon}>
              <MaterialCommunityIcons name="flag-checkered" size={16} color="#FF5722" />
            </View>
            <View style={styles.timelineContent}>
              <Text style={styles.timelineLabel}>End Date</Text>
              <Text style={styles.timelineValue}>{program.endDate ? new Date(program.endDate).toLocaleDateString('en-US', {
                weekday: 'short',
                month: 'short',
                day: 'numeric'
              }) : 'TBD'}</Text>
            </View>
          </View>
        </View>

        {/* Simple Amount Section */}
        <View style={styles.amountContainer}>
          <Text style={styles.amountLabel}>Your Investment</Text>
          <Text style={styles.amountValue}>${program.betAmount}</Text>
        </View>
      </LinearGradient>

      <View style={styles.termsContainer}>
        <TouchableOpacity 
          style={styles.termsRow}
          onPress={() => setTermsAgreed(!termsAgreed)}
        >
          <View style={styles.checkbox}>
            {termsAgreed && <View style={styles.checkboxChecked} />}
          </View>
          <Text style={styles.termsText}>
            I agree to the{" "}
            <Text style={styles.termsLink} onPress={handleTermsPress}>
              Terms and Conditions
            </Text>
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.paymentButtons}>
        {/* Stripe Payment - Available on all platforms */}
        <TouchableOpacity
          style={[
            styles.paymentButton,
            styles.stripeButton,
            !termsAgreed && styles.disabledButton,
          ]}
          onPress={handleStripePayment}
          disabled={!termsAgreed || processingPayment}
        >
          {processingPayment ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <>
              <MaterialCommunityIcons name="credit-card" size={24} color="#fff" />
              <Text style={styles.paymentButtonText}>Pay with Card</Text>
            </>
          )}
        </TouchableOpacity>

        {/* Apple Pay - iOS only */}
        {Platform.OS === 'ios' && (
          <TouchableOpacity
            style={[
              styles.paymentButton,
              styles.applePayButton,
              !termsAgreed && styles.disabledButton,
            ]}
            onPress={handleApplePayment}
            disabled={!termsAgreed || processingPayment}
          >
            {processingPayment ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <>
                <MaterialCommunityIcons name="apple" size={24} color="#fff" />
                <Text style={styles.paymentButtonText}>Pay with Apple Pay</Text>
              </>
            )}
          </TouchableOpacity>
        )}

        {/* Google Pay - Android only */}
        {Platform.OS === 'android' && (
          <TouchableOpacity
            style={[
              styles.paymentButton,
              styles.googlePayButton,
              !termsAgreed && styles.disabledButton,
            ]}
            onPress={handleGooglePayment}
            disabled={!termsAgreed || processingPayment}
          >
            {processingPayment ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <>
                <MaterialCommunityIcons name="google" size={24} color="#fff" />
                <Text style={styles.paymentButtonText}>Pay with Google Pay</Text>
              </>
            )}
          </TouchableOpacity>
        )}
      </View>

      {/* Join Program Without Payment Button */}
      <View style={styles.freeJoinContainer}>
        <Text style={styles.freeJoinText}>
          Want to participate without the financial commitment?
        </Text>
        <TouchableOpacity
          style={[
            styles.paymentButton,
            styles.freeJoinButton,
            !termsAgreed && styles.disabledButton,
          ]}
          onPress={handleJoinWithoutPayment}
          disabled={!termsAgreed || processingPayment}
        >
          {processingPayment ? (
            <ActivityIndicator size="small" color="#000" />
          ) : (
            <>
              <MaterialCommunityIcons name="account-plus" size={24} color="#000" />
              <Text style={styles.freeJoinButtonText}>Join Program (Free)</Text>
            </>
          )}
        </TouchableOpacity>
      </View>

      {/* Terms and Conditions Modal */}
      <Modal visible={termsVisible} animationType="slide" transparent={true}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Terms & Conditions</Text>
              <TouchableOpacity onPress={() => setTermsVisible(false)}>
                <MaterialIcons name="close" size={24} color="#FFEB3B" />
              </TouchableOpacity>
            </View>
            <ScrollView style={styles.modalScrollView}>
              <Text style={styles.modalText}>
                {`1. By participating in this program, you acknowledge that you are entering into a legally binding agreement with HabitHustle and its affiliates.

2. Participants must be at least 13 years old. If you are under 18, parental consent is required.

3. All users must provide accurate information during sign-up, including valid identification if requested.

4. By pledging money to the challenge, you agree that this amount is non-refundable unless specified by the program rules.

5. Users who fail to check in or upload proof of progress according to the challenge rules may be disqualified and forfeit their pledge.

6. Bailouts may be used only as per the program’s specific rules. Misuse or manipulation of bailout options may lead to disqualification.

7. Users are responsible for ensuring their check-ins are accurate and truthful. Any fraudulent submissions will lead to immediate disqualification and account suspension.

8. Moderators have the authority to verify, accept, or reject proof of progress submissions. Their decisions are final.

9. In case of disputes, HabitHustle reserves the right to review the entire challenge history and make a final ruling.

10. Program creators are entitled to a 6% commission from the final reward pool of their created programs, while HabitHustle takes 6% as platform fees.

11. The remaining 88% of the losers' pool is equally distributed among the winners.

12. Monthly and fortnightly leaderboards are based on points earned by participating and completing programs. Rewards are subject to availability and discretion of the platform.

13. Users must not engage in any form of harassment, hate speech, or inappropriate behavior within the app or associated chatrooms.

14. All in-app purchases, including extra bailouts or premium subscriptions, are final and non-refundable.

15. Users can create custom programs, but they are solely responsible for the program’s legitimacy, fairness, and participant engagement.

16. Users must grant access to third-party services (e.g., Strava, GitHub, Apple Health, etc.) to track and verify their progress as required by specific programs.

17. By joining a program, users agree to share relevant data including but not limited to: GPS location, fitness data, app usage statistics, submitted images, video proof, activity metadata, and third-party connection tokens.

18. All program-related data including GPS coordinates, photos, submissions, metadata, and 3rd party access will be automatically and permanently deleted from HabitHustle servers within 15 days after the program ends.

19. Users can manually revoke third-party access at any time via their profile settings, but this may result in automatic removal from active programs.

20. The platform reserves the right to suspend or terminate user accounts in the event of policy violations, fraudulent activity, or platform misuse.

21. HabitHustle is not responsible for any financial loss, health issues, or injuries incurred while participating in a challenge. Users are advised to consult their physician or relevant professionals before joining physical or mentally demanding programs.

22. By accepting these terms, users also consent to receive program-related updates, alerts, and occasional marketing communications, which can be managed from notification settings.

23. All collected data is handled in accordance with our Privacy Policy and is encrypted in transit and at rest.

24. Your participation in any challenge implies full understanding and acceptance of all terms listed above.

25. These Terms & Conditions are subject to change. Users will be notified via the app or email in case of updates.
`}
              </Text>
            </ScrollView>
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setTermsVisible(false)}
            >
              <Text style={styles.modalCloseButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </ScrollView>
  );
};

const PaymentConfirmation: React.FC = () => {
  return (
    <ProtectedRoute>
      <PaymentConfirmationComponent />
    </ProtectedRoute>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#000",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#000",
    padding: 20,
  },
  errorText: {
    color: "#fff",
    fontSize: 18,
    marginBottom: 20,
    textAlign: "center",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    padding: 20,
    paddingTop: 60,
  },
  backIcon: {
    marginRight: 15,
  },
  headerTitle: {
    color: "#FFEB3B",
    fontSize: 20,
    fontWeight: "bold",
  },
  programCard: {
    borderRadius: 20,
    marginVertical: 15,
    marginHorizontal: 20,
    padding: 20,
    boxShadow: '0 8px 15px rgba(255, 235, 59, 0.3)',
    elevation: 12,
    borderWidth: 1,
    borderColor: "#FFEB3B30",
    position: 'relative',
    overflow: 'hidden',
  },
  backgroundPattern: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 0,
  },
  patternIcon1: {
    position: 'absolute',
    top: -20,
    right: -30,
    transform: [{ rotate: '15deg' }],
  },
  patternIcon2: {
    position: 'absolute',
    bottom: -10,
    left: -20,
    transform: [{ rotate: '-10deg' }],
  },
  patternIcon3: {
    position: 'absolute',
    top: '50%',
    right: 10,
    transform: [{ rotate: '25deg' }],
  },
  cardHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 20,
    zIndex: 1,
  },
  iconContainer: {
    width: 60,
    height: 60,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 30,
    marginRight: 15,
    boxShadow: '0 4px 8px rgba(255, 235, 59, 0.5)',
    elevation: 8,
  },
  iconGlow: {
    width: 50,
    height: 50,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 25,
    backgroundColor: 'rgba(0,0,0,0.1)',
  },
  cardTextContainer: {
    flex: 1,
    marginRight: 10,
  },
  categoryBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    alignSelf: "flex-start",
    boxShadow: '0 2px 4px rgba(255, 235, 59, 0.3)',
    elevation: 4,
  },
  categoryText: {
    color: "#000",
    fontSize: 11,
    fontWeight: "bold",
    textTransform: "uppercase",
    letterSpacing: 0.5,
  },
  programName: {
    color: "white",
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 4,
    textShadow: '1px 1px 2px rgba(0, 0, 0, 0.5)',
  },
  programHeadline: {
    color: "#CCCCCC",
    fontSize: 14,
    fontStyle: "italic",
    lineHeight: 18,
    opacity: 0.9,
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: 'rgba(255, 235, 59, 0.05)',
    borderRadius: 15,
    padding: 15,
    marginVertical: 15,
    borderWidth: 1,
    borderColor: 'rgba(255, 235, 59, 0.2)',
    zIndex: 1,
  },
  statItem: {
    alignItems: "center",
    flex: 1,
  },
  statLabel: {
    color: "#CCCCCC",
    fontSize: 11,
    marginTop: 4,
    textAlign: "center",
    textTransform: "uppercase",
    letterSpacing: 0.5,
  },
  statValue: {
    color: "white",
    fontSize: 14,
    fontWeight: "bold",
    marginTop: 2,
    textAlign: "center",
  },
  statDivider: {
    width: 1,
    height: 30,
    backgroundColor: 'rgba(255, 235, 59, 0.3)',
    marginHorizontal: 10,
  },
  timelineContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginVertical: 15,
    paddingHorizontal: 10,
    zIndex: 1,
  },
  timelineItem: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  timelineIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 235, 59, 0.1)',
    justifyContent: "center",
    alignItems: "center",
    marginRight: 10,
    borderWidth: 1,
    borderColor: 'rgba(255, 235, 59, 0.3)',
  },
  timelineContent: {
    flex: 1,
  },
  timelineLabel: {
    color: "#CCCCCC",
    fontSize: 11,
    textTransform: "uppercase",
    letterSpacing: 0.5,
  },
  timelineValue: {
    color: "white",
    fontSize: 13,
    fontWeight: "bold",
    marginTop: 2,
  },
  timelineLine: {
    width: 40,
    height: 2,
    backgroundColor: 'rgba(255, 235, 59, 0.3)',
    marginHorizontal: 10,
  },
  amountContainer: {
    backgroundColor: "#2A2A2A",
    borderRadius: 15,
    padding: 20,
    marginTop: 20,
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#FFEB3B30",
    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.3)',
    elevation: 6,
    zIndex: 1,
  },
  amountLabel: {
    color: "#FFEB3B",
    fontSize: 16,
    marginBottom: 8,
    fontWeight: "600",
    textAlign: "center",
  },
  amountValue: {
    color: "#fff",
    fontSize: 32,
    fontWeight: "bold",
    textAlign: "center",
  },
  termsContainer: {
    margin: 20,
  },
  termsRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: "#FFEB3B",
    borderRadius: 4,
    marginRight: 10,
    justifyContent: "center",
    alignItems: "center",
  },
  checkboxChecked: {
    width: 12,
    height: 12,
    backgroundColor: "#FFEB3B",
    borderRadius: 2,
  },
  termsText: {
    color: "#fff",
    fontSize: 14,
    flex: 1,
  },
  termsLink: {
    color: "#FFEB3B",
    textDecorationLine: "underline",
  },
  paymentButtons: {
    margin: 20,
    gap: 15,
  },
  paymentButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: 15,
    borderRadius: 10,
    gap: 10,
  },
  stripeButton: {
    backgroundColor: "#6772E5",
  },
  googlePayButton: {
    backgroundColor: "#4285F4",
  },
  applePayButton: {
    backgroundColor: "#000",
  },
  disabledButton: {
    backgroundColor: "#666",
    opacity: 0.6,
  },
  paymentButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "bold",
  },
  freeJoinContainer: {
    margin: 20,
    marginTop: 10,
    alignItems: "center",
  },
  freeJoinText: {
    color: "#ccc",
    fontSize: 14,
    textAlign: "center",
    marginBottom: 15,
    fontStyle: "italic",
  },
  freeJoinButton: {
    backgroundColor: "#FFEB3B",
    borderWidth: 2,
    borderColor: "#FFC107",
  },
  freeJoinButtonText: {
    color: "#000",
    fontSize: 16,
    fontWeight: "bold",
  },
  backButton: {
    backgroundColor: "#FFEB3B",
    padding: 15,
    borderRadius: 10,
    alignItems: "center",
    margin: 20,
  },
  backButtonText: {
    color: "#000",
    fontSize: 16,
    fontWeight: "bold",
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.8)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContent: {
    backgroundColor: "#1e1e1e",
    margin: 20,
    borderRadius: 15,
    padding: 20,
    maxHeight: "80%",
    width: "90%",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 15,
  },
  modalTitle: {
    color: "#FFEB3B",
    fontSize: 18,
    fontWeight: "bold",
  },
  modalScrollView: {
    maxHeight: 400,
  },
  modalText: {
    color: "#fff",
    fontSize: 14,
    lineHeight: 20,
  },
  modalCloseButton: {
    backgroundColor: "#FFEB3B",
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
    marginTop: 15,
  },
  modalCloseButtonText: {
    color: "#000",
    fontSize: 16,
    fontWeight: "bold",
  },
});

export default PaymentConfirmation;
