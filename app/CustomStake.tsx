import React, { useCallback, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  Platform,
  KeyboardAvoidingView,
  StatusBar,
  SafeAreaView,
  Modal,
} from 'react-native';
import { useRouter } from 'expo-router';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { useCustomStakeForm } from '@/hooks/useCustomStakeForm';
import { useCustomStakeValidation, useStakeCalculation } from '@/hooks/useCustomStakeValidation';
import { CustomStakeSubmissionData } from '@/types/customStake';
import { SentenceForm } from '@/components/customStake/SentenceForm';
import { PremiumFeaturesForm } from '@/components/customStake/PremiumFeaturesForm';
import { SummaryConfirmationForm } from '../components/customStake/SummaryConfirmationForm';
import { ProgressBar } from '@/components/customStake/ProgressBar';
import { createCustomStakeStyles } from '@/styles/customStakeStyles';
import { firestoreService } from '@/services/database';
import { getId } from '@/utilis/variables';

// All interfaces and constants are now imported from types/customStake.ts

// Constants moved to types/customStake.ts

const CustomStake: React.FC = () => {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const styles = createCustomStakeStyles(colors);

  // State for managing steps
  const [currentStep, setCurrentStep] = useState(1);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false); // Prevent duplicate submissions

  // Use custom hooks for form management
  const {
    formData,
    updateCommitment,
    updateEvidenceType,
    updateReportingFrequency,
    updateRecipient,
    updateTimingType,
    updateAmount,
    updateStartDate,
    updateEndDate,
    updateWeekLength,
    updateTimesPerWeek,
    updateTimesPerMonth,
    updateLocationData,
    updateAppName,
    updateStravaActivity,
    updateTiming,
    updateMotivationalNote,
    updateReminderChannels,
    updateStrictnessLevel,
    updateNotificationTiming,
    updatePremiumFeatures,
  } = useCustomStakeForm();

  // Use validation hook
  const validation = useCustomStakeValidation(formData);
  const totalStake = useStakeCalculation(formData);

  const handleNextClick = useCallback(() => {
    // Use validation from hook
    if (!validation.isValid) {
      const firstError = validation.errors[0];
      Alert.alert('Error', firstError.message);
      return;
    }

    // Move to step 2
    setCurrentStep(2);
  }, [validation]);

  const handleStep2Next = useCallback(() => {
    // Move to step 3 (summary and confirmation)
    setCurrentStep(3);
  }, []);

  const handleFinalSubmit = useCallback(async () => {
    // Prevent multiple submissions
    if (isSubmitting) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Get current user ID
      const userId = await getId();
      if (!userId) {
        Alert.alert('Error', 'User ID not found. Please sign in and try again.');
        setIsSubmitting(false);
        return;
      }

      // Create comprehensive data object
      const stakeData: CustomStakeSubmissionData = {
        ...formData,
        totalStake,
        createdAt: new Date().toISOString(),
        // Calculate actual end date for weekly/monthly commitments
        calculatedEndDate: formData.startDate && formData.weekLength ?
          (() => {
            const start = new Date(formData.startDate);
            const multiplier = formData.reportingFrequency === 'weekly' ? 7 : 30;
            const endDate = new Date(start);
            endDate.setDate(start.getDate() + (formData.weekLength * multiplier));
            return endDate.toISOString();
          })() : formData.endDate,
        // Add reporting schedule details
        reportingSchedule: {
          frequency: formData.reportingFrequency,
          ...(formData.reportingFrequency === 'weekly' && {
            dayOfWeek: formData.startDate ? new Date(formData.startDate).getDay() : undefined,
            timesPerWeek: formData.timesPerWeek
          }),
          ...(formData.reportingFrequency === 'monthly' && {
            dayOfMonth: formData.startDate ? new Date(formData.startDate).getDate() : undefined,
            timesPerMonth: formData.timesPerMonth
          })
        }
      };

      // Prepare commit data for Firestore
      // Filter out undefined values to prevent Firebase errors
      const baseCommitData = {
        commitment: stakeData.commitment,
        evidenceType: stakeData.evidenceType,
        reportingFrequency: stakeData.reportingFrequency,
        startDate: stakeData.startDate?.toISOString ? stakeData.startDate.toISOString() : stakeData.startDate,
        endDate: stakeData.endDate?.toISOString ? stakeData.endDate.toISOString() : stakeData.endDate,
        recipient: stakeData.recipient,
        amountPerReport: stakeData.amountPerReport,
        totalStake: stakeData.totalStake,
        referee: stakeData.referee,
        reportingSchedule: stakeData.reportingSchedule
      };

      // Add optional fields only if they have valid values (not undefined or null)
      const commitData: any = { ...baseCommitData };

      // Add calculatedEndDate if it exists
      if (stakeData.calculatedEndDate) commitData.calculatedEndDate = stakeData.calculatedEndDate;

      // Add weekLength only for frequencies that need it and when it has a value
      if (stakeData.reportingFrequency !== 'once' && stakeData.weekLength !== undefined && stakeData.weekLength !== null) {
        commitData.weekLength = stakeData.weekLength;
      }

      // Add timing fields only if they have values
      if (stakeData.timingType) commitData.timingType = stakeData.timingType;
      if (stakeData.beforeTime) commitData.beforeTime = stakeData.beforeTime;
      if (stakeData.afterTime) commitData.afterTime = stakeData.afterTime;
      if (stakeData.startTime) commitData.startTime = stakeData.startTime;
      if (stakeData.endTime) commitData.endTime = stakeData.endTime;

      // Add evidence-specific fields only if they have values
      if (stakeData.location) commitData.location = stakeData.location;
      if (stakeData.locationData) commitData.locationData = stakeData.locationData;
      if (stakeData.appName) commitData.appName = stakeData.appName;
      if (stakeData.stravaActivity) commitData.stravaActivity = stakeData.stravaActivity;

      // Add premium features only if they exist
      if (stakeData.premiumFeatures) commitData.premiumFeatures = stakeData.premiumFeatures;

      // Add frequency-specific fields only if they have values
      if (stakeData.timesPerWeek !== undefined && stakeData.timesPerWeek !== null) {
        commitData.timesPerWeek = stakeData.timesPerWeek;
      }
      if (stakeData.timesPerMonth !== undefined && stakeData.timesPerMonth !== null) {
        commitData.timesPerMonth = stakeData.timesPerMonth;
      }

      // Save commit to Firestore
      console.log('Saving commit to Firestore...');
      const result = await firestoreService.commits.createFullCommit(userId, commitData);

      if (result.success) {
        console.log('Commit saved successfully with ID:', result.data);

        const message = formData.recipient === 'no-money'
          ? 'Your custom commitment has been created successfully!\n\nNo money at stake - accountability only.'
          : `Your custom stake has been created successfully!\n\nTotal Stake: $${stakeData.totalStake.toFixed(2)}`;

        setSuccessMessage(message);
        setShowSuccessModal(true);
      } else {
        console.error('Failed to save commit:', result.error);
        setSuccessMessage('Failed to save your commitment. Please try again.');
        setShowSuccessModal(true);
      }
    } catch (error) {
      console.error('Error in handleFinalSubmit:', error);
      setSuccessMessage('An unexpected error occurred. Please try again.');
      setShowSuccessModal(true);
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, totalStake, router, isSubmitting]);

  const handleBackToStep1 = useCallback(() => {
    setCurrentStep(1);
  }, []);

  const handleBackToStep2 = useCallback(() => {
    setCurrentStep(2);
  }, []);



  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar
        barStyle={isDark ? 'light-content' : 'dark-content'}
        backgroundColor={colors.background}
        translucent={false}
      />
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => {
              if (currentStep > 1) {
                setCurrentStep(currentStep - 1);
              } else {
                console.log('Back button pressed');
                router.back();
              }
            }}
            activeOpacity={0.7}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <MaterialIcons name="arrow-back" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Custom Stake</Text>
          <View style={styles.headerSpacer} />
        </View>

        {/* Progress Bar */}
        <ProgressBar
          currentStep={currentStep}
          totalSteps={3}
        />

        {/* Scrollable Content */}
        <ScrollView
          style={styles.scrollContent}
          contentContainerStyle={styles.scrollContentContainer}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          {currentStep === 1 ? (
            <SentenceForm
              formData={formData}
              onUpdateCommitment={updateCommitment}
              onUpdateEvidenceType={updateEvidenceType}
              onUpdateReportingFrequency={updateReportingFrequency}
              onUpdateTimingType={updateTimingType}
              onUpdateRecipient={updateRecipient}
              onUpdateAmount={updateAmount}
              onUpdateStartDate={updateStartDate}
              onUpdateEndDate={updateEndDate}
              onUpdateWeekLength={updateWeekLength}
              onUpdateTimesPerWeek={updateTimesPerWeek}
              onUpdateTimesPerMonth={updateTimesPerMonth}
              onUpdateLocationData={updateLocationData}
              onUpdateAppName={updateAppName}
              onUpdateStravaActivity={updateStravaActivity}
              onUpdateTiming={updateTiming}
            />
          ) : currentStep === 2 ? (
            <PremiumFeaturesForm
              premiumFeatures={formData.premiumFeatures!}
              reportingFrequency={formData.reportingFrequency}
              onUpdateMotivationalNote={updateMotivationalNote}
              onUpdateReminderChannels={updateReminderChannels}
              onUpdateStrictnessLevel={updateStrictnessLevel}
              onUpdateNotificationTiming={updateNotificationTiming}
              onUpdatePremiumFeatures={updatePremiumFeatures}
              onSubmit={handleStep2Next}
              onCancel={handleBackToStep1}
            />
          ) : (
            <SummaryConfirmationForm
              formData={formData}
              totalStake={totalStake}
              onSubmit={handleFinalSubmit}
              onCancel={handleBackToStep2}
            />
          )}
        </ScrollView>

        {/* Submit Button - Only show on Step 1 */}
        {currentStep === 1 && (
          <View style={styles.submitContainer}>
            <TouchableOpacity
              style={[
                styles.submitButton,
                !validation.isValid && styles.submitButtonDisabled
              ]}
              onPress={handleNextClick}
              disabled={!validation.isValid}
            >
              <Text style={styles.submitButtonText}>Next</Text>
            </TouchableOpacity>
          </View>
        )}
      </KeyboardAvoidingView>

      {/* Success Modal */}
      <Modal
        visible={showSuccessModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowSuccessModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContainer, { backgroundColor: colors.surface }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                {successMessage.includes('Failed') || successMessage.includes('error') ? 'Error' : 'Success!'}
              </Text>
            </View>

            <View style={styles.modalContent}>
              <Text style={[styles.modalMessage, { color: colors.text }]}>
                {successMessage}
              </Text>
            </View>

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, { backgroundColor: colors.primary }]}
                onPress={() => {
                  setShowSuccessModal(false);
                  if (!successMessage.includes('Failed') && !successMessage.includes('error')) {
                    // Navigate to progress page to maintain flow after successful commit creation
                    router.replace('/(tabs)/progress');
                  }
                }}
                activeOpacity={0.7}
              >
                <Text style={styles.modalButtonText}>OK</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};



export default CustomStake;
