// Basic tests for the Event Management System
// Note: These are simplified tests. In a production environment,
// you would want more comprehensive testing with proper mocking.

import { EventManager } from '../EventManager';
import { EventType, EventPriority, EventHandler, EventMiddleware } from '../types';

// Mock handler for testing
class Mock<PERSON>andler implements EventHandler {
  public readonly name = 'MockHandler';
  public readonly priority = 1;
  public handledEvents: any[] = [];

  public shouldHandle(event: any): boolean {
    return true;
  }

  public async handle(event: any): Promise<void> {
    this.handledEvents.push(event);
  }

  public reset(): void {
    this.handledEvents = [];
  }
}

// Mock middleware for testing
class MockMiddleware implements EventMiddleware {
  public readonly name = 'MockMiddleware';
  public readonly order = 1;
  public processedEvents: any[] = [];

  public async process(event: any): Promise<any> {
    this.processedEvents.push(event);
    return event;
  }

  public reset(): void {
    this.processedEvents = [];
  }
}

describe('EventManager', () => {
  let eventManager: EventManager;
  let mockHandler: MockHandler;
  let mockMiddleware: MockMiddleware;

  beforeEach(() => {
    // Create a new instance for each test
    eventManager = EventManager.getInstance();
    mockHandler = new MockHandler();
    mockMiddleware = new MockMiddleware();
    
    // Reset handlers and middleware
    eventManager.unregisterHandler('MockHandler');
    eventManager.registerHandler(mockHandler);
    eventManager.addMiddleware(mockMiddleware);
  });

  afterEach(() => {
    mockHandler.reset();
    mockMiddleware.reset();
  });

  test('should emit and process events', async () => {
    const eventData = {
      userEmail: '<EMAIL>',
      timestamp: new Date().toISOString(),
      source: 'test',
    };

    const eventId = await eventManager.emit(
      EventType.USER_LOGIN,
      eventData,
      EventPriority.MEDIUM
    );

    expect(eventId).toBeDefined();
    expect(eventId).toMatch(/^evt_/);

    // Wait for processing
    await new Promise(resolve => setTimeout(resolve, 100));

    expect(mockMiddleware.processedEvents).toHaveLength(1);
    expect(mockHandler.handledEvents).toHaveLength(1);
    
    const processedEvent = mockHandler.handledEvents[0];
    expect(processedEvent.type).toBe(EventType.USER_LOGIN);
    expect(processedEvent.priority).toBe(EventPriority.MEDIUM);
    expect(processedEvent.data.userEmail).toBe('<EMAIL>');
  });

  test('should handle multiple events', async () => {
    const events = [
      {
        type: EventType.USER_LOGIN,
        data: { userEmail: '<EMAIL>', timestamp: new Date().toISOString(), source: 'test' },
      },
      {
        type: EventType.USER_SIGNUP,
        data: { userEmail: '<EMAIL>', timestamp: new Date().toISOString(), source: 'test' },
      },
    ];

    const eventIds = await Promise.all(
      events.map(event => 
        eventManager.emit(event.type, event.data, EventPriority.MEDIUM)
      )
    );

    expect(eventIds).toHaveLength(2);
    eventIds.forEach(id => expect(id).toMatch(/^evt_/));

    // Wait for processing
    await new Promise(resolve => setTimeout(resolve, 200));

    expect(mockHandler.handledEvents).toHaveLength(2);
  });

  test('should track statistics', async () => {
    const initialStats = eventManager.getStats();
    expect(initialStats.totalEvents).toBe(0);

    await eventManager.emit(
      EventType.USER_LOGIN,
      { userEmail: '<EMAIL>', timestamp: new Date().toISOString(), source: 'test' },
      EventPriority.HIGH
    );

    const updatedStats = eventManager.getStats();
    expect(updatedStats.totalEvents).toBe(1);
    expect(updatedStats.eventsByType[EventType.USER_LOGIN]).toBe(1);
    expect(updatedStats.eventsByPriority[EventPriority.HIGH]).toBe(1);
  });

  test('should register and unregister handlers', () => {
    const initialHandlers = eventManager.getHandlers();
    expect(initialHandlers).toContain('MockHandler');

    eventManager.unregisterHandler('MockHandler');
    const afterUnregister = eventManager.getHandlers();
    expect(afterUnregister).not.toContain('MockHandler');

    eventManager.registerHandler(mockHandler);
    const afterRegister = eventManager.getHandlers();
    expect(afterRegister).toContain('MockHandler');
  });

  test('should handle middleware that blocks events', async () => {
    // Create middleware that blocks events
    class BlockingMiddleware implements EventMiddleware {
      public readonly name = 'BlockingMiddleware';
      public readonly order = 0; // Higher priority than MockMiddleware

      public async process(event: any): Promise<any> {
        return null; // Block the event
      }
    }

    eventManager.addMiddleware(new BlockingMiddleware());

    await eventManager.emit(
      EventType.USER_LOGIN,
      { userEmail: '<EMAIL>', timestamp: new Date().toISOString(), source: 'test' },
      EventPriority.MEDIUM
    );

    // Wait for processing
    await new Promise(resolve => setTimeout(resolve, 100));

    // Event should be blocked, so handler shouldn't receive it
    expect(mockHandler.handledEvents).toHaveLength(0);
  });

  test('should create base event data', () => {
    const baseData = eventManager.createBaseEventData(
      'TestSource',
      'user123',
      '<EMAIL>'
    );

    expect(baseData.source).toBe('TestSource');
    expect(baseData.userId).toBe('user123');
    expect(baseData.userEmail).toBe('<EMAIL>');
    expect(baseData.timestamp).toBeDefined();
    expect(new Date(baseData.timestamp)).toBeInstanceOf(Date);
  });

  test('should handle configuration updates', () => {
    const newConfig = {
      maxRetries: 5,
      retryDelay: 2000,
      enableLogging: false,
    };

    eventManager.configure(newConfig);

    // Configuration is internal, so we can't directly test it,
    // but we can verify the method doesn't throw
    expect(() => eventManager.configure(newConfig)).not.toThrow();
  });

  test('should track queue size', async () => {
    const initialQueueSize = eventManager.getQueueSize();
    expect(initialQueueSize).toBe(0);

    // Emit multiple events quickly
    const promises = Array.from({ length: 5 }, (_, i) =>
      eventManager.emit(
        EventType.USER_LOGIN,
        { userEmail: `user${i}@example.com`, timestamp: new Date().toISOString(), source: 'test' },
        EventPriority.MEDIUM
      )
    );

    await Promise.all(promises);

    // Wait for processing to complete
    await new Promise(resolve => setTimeout(resolve, 300));

    const finalQueueSize = eventManager.getQueueSize();
    expect(finalQueueSize).toBe(0); // Should be processed
  });
});

// Integration test for the convenience functions
describe('Event System Integration', () => {
  test('should work with convenience functions', async () => {
    // This test would require mocking Firebase and other dependencies
    // For now, we'll just test that the functions exist and can be imported
    
    const { 
      emitUserEvent,
      emitProgramEvent,
      sendNotification,
      EventType,
      EventPriority,
      NotificationType 
    } = require('../index');

    expect(emitUserEvent).toBeDefined();
    expect(emitProgramEvent).toBeDefined();
    expect(sendNotification).toBeDefined();
    expect(EventType).toBeDefined();
    expect(EventPriority).toBeDefined();
    expect(NotificationType).toBeDefined();
  });
});

// Mock Firebase for testing
jest.mock('@/config/firebase', () => ({
  db: {},
  auth: {},
  storage: {},
}));

// Mock Expo Notifications for testing
jest.mock('expo-notifications', () => ({
  getPermissionsAsync: jest.fn().mockResolvedValue({ status: 'granted' }),
  scheduleNotificationAsync: jest.fn().mockResolvedValue({}),
}));
