import { useState, useEffect } from 'react';
import { Dimensions, Platform } from 'react-native';

interface CalendarDimensions {
  screenWidth: number;
  calendarPadding: number;
  calendarWidth: number;
  daySize: number;
  dayWidthPercentage: string;
  compactDayHeight: number;
  expandedDayHeight: number;
}

/**
 * Hook to provide responsive calendar dimensions for both mobile and web
 * Handles window resize events on web and provides consistent sizing
 */
export const useCalendarDimensions = (): CalendarDimensions => {
  const [dimensions, setDimensions] = useState<CalendarDimensions>(() => {
    return calculateDimensions();
  });

  useEffect(() => {
    if (Platform.OS === 'web') {
      // Handle window resize on web with debouncing for performance
      let timeoutId: NodeJS.Timeout;

      const handleResize = () => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          setDimensions(calculateDimensions());
        }, 100); // Debounce resize events
      };

      window.addEventListener('resize', handleResize);
      return () => {
        window.removeEventListener('resize', handleResize);
        clearTimeout(timeoutId);
      };
    } else {
      // Handle orientation changes on mobile
      const subscription = Dimensions.addEventListener('change', () => {
        setDimensions(calculateDimensions());
      });

      return () => subscription?.remove();
    }
  }, []);

  return dimensions;
};

/**
 * Calculate calendar dimensions based on platform and screen size
 */
function calculateDimensions(): CalendarDimensions {
  try {
    if (Platform.OS === 'web') {
      // Web-specific calculations
      const maxWidth = 480; // Match the web container max width from _layout.tsx
      const padding = 12;
      const calendarWidth = Math.max(maxWidth - padding * 2, 200); // Ensure minimum width
      const daySize = Math.max(Math.floor(calendarWidth / 7) * 0.85, 20); // Ensure minimum day size

      return {
        screenWidth: maxWidth,
        calendarPadding: padding,
        calendarWidth,
        daySize,
        dayWidthPercentage: '14.285714%', // 100% / 7 days
        compactDayHeight: 50, // Reduced from 60 to decrease vertical padding
        expandedDayHeight: 65, // Reduced from 80 to decrease vertical padding
      };
    } else {
      // Mobile calculations
      const { width: screenWidth } = Dimensions.get('window');
      const padding = 12;
      const calendarWidth = Math.max(screenWidth - padding * 2, 200);
      const daySize = Math.max(Math.floor(calendarWidth / 7) * 0.85, 20);

      return {
        screenWidth,
        calendarPadding: padding,
        calendarWidth,
        daySize,
        dayWidthPercentage: '14.285714%',
        compactDayHeight: 50, // Reduced from 60 to decrease vertical padding
        expandedDayHeight: 65, // Reduced from 80 to decrease vertical padding
      };
    }
  } catch (error) {
    console.warn('Error calculating calendar dimensions, using fallback:', error);
    // Fallback dimensions
    return {
      screenWidth: 320,
      calendarPadding: 12,
      calendarWidth: 296,
      daySize: 35,
      dayWidthPercentage: '14.285714%',
      compactDayHeight: 50, // Reduced from 60 to decrease vertical padding
      expandedDayHeight: 65, // Reduced from 80 to decrease vertical padding
    };
  }
}

/**
 * Get timeline-specific dimensions (with different padding)
 */
export const useTimelineCalendarDimensions = (): CalendarDimensions => {
  const [dimensions, setDimensions] = useState<CalendarDimensions>(() => {
    return calculateTimelineDimensions();
  });

  useEffect(() => {
    if (Platform.OS === 'web') {
      let timeoutId: NodeJS.Timeout;

      const handleResize = () => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          setDimensions(calculateTimelineDimensions());
        }, 100);
      };

      window.addEventListener('resize', handleResize);
      return () => {
        window.removeEventListener('resize', handleResize);
        clearTimeout(timeoutId);
      };
    } else {
      const subscription = Dimensions.addEventListener('change', () => {
        setDimensions(calculateTimelineDimensions());
      });

      return () => subscription?.remove();
    }
  }, []);

  return dimensions;
};

function calculateTimelineDimensions(): CalendarDimensions {
  try {
    if (Platform.OS === 'web') {
      const maxWidth = 480;
      const padding = 20; // Timeline uses more padding
      const calendarWidth = Math.max(maxWidth - padding * 2, 200);
      const daySize = Math.max(calendarWidth / 7, 25); // Timeline doesn't use the 0.85 multiplier

      return {
        screenWidth: maxWidth,
        calendarPadding: padding,
        calendarWidth,
        daySize,
        dayWidthPercentage: '14.285714%',
        compactDayHeight: 50, // Reduced from 60 to decrease vertical padding
        expandedDayHeight: 65, // Reduced from 80 to decrease vertical padding
      };
    } else {
      const { width: screenWidth } = Dimensions.get('window');
      const padding = 20;
      const calendarWidth = Math.max(screenWidth - padding * 2, 200);
      const daySize = Math.max(calendarWidth / 7, 25);

      return {
        screenWidth,
        calendarPadding: padding,
        calendarWidth,
        daySize,
        dayWidthPercentage: '14.285714%',
        compactDayHeight: 50, // Reduced from 60 to decrease vertical padding
        expandedDayHeight: 65, // Reduced from 80 to decrease vertical padding
      };
    }
  } catch (error) {
    console.warn('Error calculating timeline dimensions, using fallback:', error);
    // Fallback dimensions
    return {
      screenWidth: 320,
      calendarPadding: 20,
      calendarWidth: 280,
      daySize: 40,
      dayWidthPercentage: '14.285714%',
      compactDayHeight: 50, // Reduced from 60 to decrease vertical padding
      expandedDayHeight: 65, // Reduced from 80 to decrease vertical padding
    };
  }
}
