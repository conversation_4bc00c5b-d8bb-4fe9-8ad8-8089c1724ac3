import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';
import { CustomStakeData, LocationData } from '@/types/customStake';
import { EvidenceButton, EvidenceSelector } from './EvidenceSelector';
import { FrequencyButton, FrequencySelector } from './FrequencySelector';
import { TimingButton, TimingSelector } from './TimingSelector';
import { RecipientButton, RecipientSelector } from './RecipientSelector';
import { DateButton, DateSelector } from './DateSelector';
import { TimeButton, TimeSelector } from './TimeSelector';
import InlineLocationMap from '@/components/InlineLocationMap';
import LocationPicker from '@/components/LocationPicker';

interface SentenceFormProps {
  formData: CustomStakeData;
  onUpdateCommitment: (commitment: string) => void;
  onUpdateEvidenceType: (evidenceType: CustomStakeData['evidenceType']) => void;
  onUpdateReportingFrequency: (frequency: CustomStakeData['reportingFrequency']) => void;
  onUpdateTimingType: (timing: CustomStakeData['timingType']) => void;
  onUpdateRecipient: (recipient: CustomStakeData['recipient']) => void;
  onUpdateAmount: (amount: number) => void;
  onUpdateStartDate: (date: Date) => void;
  onUpdateEndDate: (date: Date) => void;
  onUpdateWeekLength: (length: number) => void;
  onUpdateTimesPerWeek: (times: number) => void;
  onUpdateTimesPerMonth: (times: number) => void;
  onUpdateLocationData: (location: LocationData) => void;
  onUpdateAppName: (appName: string) => void;
  onUpdateStravaActivity: (activity: string) => void;
  onUpdateTiming: (timeType: 'before' | 'after' | 'start' | 'end', time: string) => void;
}

const SentenceFormComponent: React.FC<SentenceFormProps> = ({
  formData,
  onUpdateCommitment,
  onUpdateEvidenceType,
  onUpdateReportingFrequency,
  onUpdateTimingType,
  onUpdateRecipient,
  onUpdateAmount,
  onUpdateStartDate,
  onUpdateEndDate,
  onUpdateWeekLength,
  onUpdateTimesPerWeek,
  onUpdateTimesPerMonth,
  onUpdateLocationData,
  onUpdateAppName,
  onUpdateStravaActivity,
  onUpdateTiming,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  // Modal states
  const [showEvidenceModal, setShowEvidenceModal] = useState(false);
  const [showFrequencyModal, setShowFrequencyModal] = useState(false);
  const [showRecipientModal, setShowRecipientModal] = useState(false);
  const [showTimingModal, setShowTimingModal] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState<'start' | 'end' | null>(null);
  const [showLocationPicker, setShowLocationPicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState<'before' | 'after' | 'start' | 'end' | null>(null);

  // Rotating placeholder state
  const [placeholderIndex, setPlaceholderIndex] = useState(0);
  const placeholderTexts = [
    "Meet myself at gym",
    "Finish my homework",
    "Clean my kitchen",
    "Read for 30 minutes daily",
    "Meditate every morning",
    "Exercise 3 times per week",
    "Cook healthy meals",
    "Study for 2 hours daily"
  ];

  // Rotating placeholder effect
  useEffect(() => {
    const interval = setInterval(() => {
      setPlaceholderIndex((prevIndex) =>
        (prevIndex + 1) % placeholderTexts.length
      );
    }, 3500);

    return () => clearInterval(interval);
  }, [placeholderTexts.length]);

  const formatDate = useCallback((date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }, []);

  const datePickerConstraints = useMemo(() => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (showDatePicker === 'start') {
      const maxDate = new Date(today);
      maxDate.setDate(today.getDate() + 14);
      return { minDate: today, maxDate };
    } else if (showDatePicker === 'end' && formData.startDate) {
      const minDate = new Date(formData.startDate);
      const maxDate = new Date(formData.startDate);
      maxDate.setDate(formData.startDate.getDate() + 50);
      return { minDate, maxDate };
    }
    return {};
  }, [showDatePicker, formData.startDate]);

  const getTimePickerTitle = () => {
    switch (showTimePicker) {
      case 'before':
        return 'Select Before Time';
      case 'after':
        return 'Select After Time';
      case 'start':
        return 'Select Start Time';
      case 'end':
        return 'Select End Time';
      default:
        return 'Select Time';
    }
  };

  const getCurrentTime = () => {
    switch (showTimePicker) {
      case 'before':
        return formData.beforeTime;
      case 'after':
        return formData.afterTime;
      case 'start':
        return formData.startTime;
      case 'end':
        return formData.endTime;
      default:
        return undefined;
    }
  };

  const handleTimeSelect = useCallback((time: string) => {
    if (showTimePicker) {
      onUpdateTiming(showTimePicker, time);
      setShowTimePicker(null);
    }
  }, [showTimePicker, onUpdateTiming]);

  return (
    <View style={styles.sentenceContainer}>
      <View style={styles.sentenceForm}>
        {/* Main sentence structure */}
        <View style={styles.sentenceRow}>
          <Text style={styles.sentenceText}>I will provide </Text>
          <EvidenceButton
            selectedEvidence={formData.evidenceType}
            onPress={() => setShowEvidenceModal(true)}
          />
          <Text style={styles.sentenceText}> as proof of my commitment to </Text>
        </View>

        {/* Commitment input */}
        <View style={styles.inputSection}>
          <TextInput
            style={styles.commitmentInput}
            placeholder={placeholderTexts[placeholderIndex]}
            placeholderTextColor={colors.textMuted}
            value={formData.commitment}
            onChangeText={onUpdateCommitment}
            multiline
            maxLength={500}
            accessibilityLabel="Commitment description"
            accessibilityHint="Enter your commitment description. Maximum 500 characters."
            accessibilityRole="text"
            returnKeyType="done"
            blurOnSubmit={true}
          />
          {formData.commitment.length > 450 && (
            <Text style={styles.characterCount} accessibilityLiveRegion="polite">
              {500 - formData.commitment.length} characters remaining
            </Text>
          )}
        </View>

        {/* Evidence-specific inputs */}
        {(formData.evidenceType === 'gps-checkin' || formData.evidenceType === 'gps-avoid') && (
          <View style={styles.locationSection}>
            <Text style={styles.sentenceText}> at </Text>
            <InlineLocationMap
              locationData={formData.locationData}
              onLocationSelect={onUpdateLocationData}
              onOpenFullPicker={() => setShowLocationPicker(true)}
            />
          </View>
        )}

        {formData.evidenceType === 'screen-time' && (
          <View style={styles.sentenceRow}>
            <Text style={styles.sentenceText}> for </Text>
            <View style={styles.inlineInputContainer}>
              <TextInput
                style={styles.inlineInput}
                placeholder="app name"
                placeholderTextColor={colors.textMuted}
                value={formData.appName || ''}
                onChangeText={onUpdateAppName}
                accessibilityLabel="App name for screen time tracking"
                accessibilityHint="Enter the name of the app you want to track screen time for"
                accessibilityRole="text"
                returnKeyType="done"
              />
            </View>
          </View>
        )}

        {formData.evidenceType === 'strava' && (
          <View style={styles.sentenceRow}>
            <Text style={styles.sentenceText}> of </Text>
            <View style={styles.inlineInputContainer}>
              <TextInput
                style={styles.inlineInput}
                placeholder="activity type (e.g., running, cycling)"
                placeholderTextColor={colors.textMuted}
                value={formData.stravaActivity || ''}
                onChangeText={onUpdateStravaActivity}
                accessibilityLabel="Strava activity type"
                accessibilityHint="Enter the type of activity for Strava tracking, such as running or cycling"
                accessibilityRole="text"
                returnKeyType="done"
              />
            </View>
          </View>
        )}

        {/* Frequency selection */}
        <View style={styles.sentenceRow}>
          <FrequencyButton
            selectedFrequency={formData.reportingFrequency}
            onPress={() => setShowFrequencyModal(true)}
          />
        </View>

        {/* Timing section - only for daily and once frequencies */}
        {(formData.reportingFrequency === 'daily' || formData.reportingFrequency === 'once') && (
          <>
            {formData.timingType === 'mid-night' && (
              <View style={styles.sentenceRow}>
                <Text style={styles.sentenceText}> by </Text>
                <TimingButton
                  selectedTiming={formData.timingType}
                  onPress={() => setShowTimingModal(true)}
                  displayText="midnight"
                />
                <Text style={styles.sentenceText}>
                  {formData.reportingFrequency === 'daily' ? ' each day' : ''}
                </Text>
              </View>
            )}

            {formData.timingType === 'before' && (
              <View style={styles.sentenceRow}>
                <Text style={styles.sentenceText}> </Text>
                <TimingButton
                  selectedTiming={formData.timingType}
                  onPress={() => setShowTimingModal(true)}
                  displayText="before"
                />
                <Text style={styles.sentenceText}> </Text>
                <TimeButton
                  selectedTime={formData.beforeTime}
                  onPress={() => setShowTimePicker('before')}
                  placeholder="Select Time"
                />
                <Text style={styles.sentenceText}>
                  {formData.reportingFrequency === 'daily' ? ' each day' : ''}
                </Text>
              </View>
            )}

            {formData.timingType === 'after' && (
              <View style={styles.sentenceRow}>
                <Text style={styles.sentenceText}> </Text>
                <TimingButton
                  selectedTiming={formData.timingType}
                  onPress={() => setShowTimingModal(true)}
                  displayText="after"
                />
                <Text style={styles.sentenceText}> </Text>
                <TimeButton
                  selectedTime={formData.afterTime}
                  onPress={() => setShowTimePicker('after')}
                  placeholder="Select Time"
                />
                <Text style={styles.sentenceText}>
                  {formData.reportingFrequency === 'daily' ? ' each day' : ''}
                </Text>
              </View>
            )}

            {formData.timingType === 'between' && (
              <View style={styles.sentenceRow}>
                <Text style={styles.sentenceText}> </Text>
                <TimingButton
                  selectedTiming={formData.timingType}
                  onPress={() => setShowTimingModal(true)}
                  displayText="between"
                />
                <Text style={styles.sentenceText}> </Text>
                <TimeButton
                  selectedTime={formData.startTime}
                  onPress={() => setShowTimePicker('start')}
                  placeholder="Start Time"
                />
                <Text style={styles.sentenceText}> and </Text>
                <TimeButton
                  selectedTime={formData.endTime}
                  onPress={() => setShowTimePicker('end')}
                  placeholder="End Time"
                />
                <Text style={styles.sentenceText}>
                  {formData.reportingFrequency === 'daily' ? ' each day' : ''}
                </Text>
              </View>
            )}
          </>
        )}

        {/* Date inputs based on frequency */}
        {formData.reportingFrequency === 'once' && (
          <View style={styles.sentenceRow}>
            <Text style={styles.sentenceText}> before </Text>
            <DateButton
              selectedDate={formData.endDate}
              onPress={() => setShowDatePicker('end')}
              placeholder="end date"
            />
          </View>
        )}

        {formData.reportingFrequency === 'daily' && (
          <View style={styles.sentenceRow}>
            <Text style={styles.sentenceText}> from </Text>
            <DateButton
              selectedDate={formData.startDate}
              onPress={() => setShowDatePicker('start')}
              placeholder="start date"
            />
            <Text style={styles.sentenceText}> to </Text>
            <DateButton
              selectedDate={formData.endDate}
              onPress={() => setShowDatePicker('end')}
              placeholder="end date"
            />
          </View>
        )}

        {(formData.reportingFrequency === 'weekly' || formData.reportingFrequency === 'monthly') && (
          <View style={styles.sentenceColumn}>
            <View style={styles.sentenceRow}>
              <Text style={styles.sentenceText}> starting </Text>
              <DateButton
                selectedDate={formData.startDate}
                onPress={() => setShowDatePicker('start')}
                placeholder="start date"
              />
            </View>

            <View style={styles.sentenceRow}>
              <Text style={styles.sentenceText}> for </Text>
              <View style={styles.numberInputContainer}>
                <TextInput
                  style={styles.numberBlankInput}
                  placeholder="4"
                  placeholderTextColor={colors.textMuted}
                  value={formData.weekLength?.toString() || ''}
                  onChangeText={(text) => {
                    const value = parseInt(text) || undefined;
                    if (value) onUpdateWeekLength(value);
                  }}
                  keyboardType="numeric"
                  accessibilityLabel={`Number of ${formData.reportingFrequency === 'weekly' ? 'weeks' : 'months'}`}
                />
              </View>
              <Text style={styles.sentenceText}>
                {formData.reportingFrequency === 'weekly' ? ' weeks' : ' months'}
              </Text>
            </View>

            {formData.reportingFrequency === 'weekly' && (
              <View style={styles.sentenceRow}>
                <Text style={styles.sentenceText}> reporting </Text>
                <View style={styles.numberInputContainer}>
                  <TextInput
                    style={styles.numberBlankInput}
                    placeholder="1"
                    placeholderTextColor={colors.textMuted}
                    value={formData.timesPerWeek?.toString() || ''}
                    onChangeText={(text) => {
                      const value = parseInt(text) || 1;
                      if (value >= 1 && value <= 7) {
                        onUpdateTimesPerWeek(value);
                      }
                    }}
                    keyboardType="numeric"
                    maxLength={1}
                    accessibilityLabel="Times per week"
                  />
                </View>
                <Text style={styles.sentenceText}> times per week</Text>
              </View>
            )}

            {formData.reportingFrequency === 'monthly' && (
              <View style={styles.sentenceRow}>
                <Text style={styles.sentenceText}> reporting </Text>
                <View style={styles.numberInputContainer}>
                  <TextInput
                    style={styles.numberBlankInput}
                    placeholder="1"
                    placeholderTextColor={colors.textMuted}
                    value={formData.timesPerMonth?.toString() || ''}
                    onChangeText={(text) => {
                      const value = parseInt(text) || 1;
                      if (value >= 1 && value <= 31) {
                        onUpdateTimesPerMonth(value);
                      }
                    }}
                    keyboardType="numeric"
                    maxLength={2}
                    accessibilityLabel="Times per month"
                  />
                </View>
                <Text style={styles.sentenceText}> times per month</Text>
              </View>
            )}
          </View>
        )}

        {/* Recipient selection */}
        <View style={styles.sentenceRow}>
          <Text style={styles.sentenceText}> If I fail my money goes to </Text>
          <RecipientButton
            selectedRecipient={formData.recipient}
            onPress={() => setShowRecipientModal(true)}
          />
        </View>

        {/* Amount section - only if money is at stake */}
        {formData.recipient !== 'no-money' && (
          <View style={styles.sentenceRow}>
            <Text style={styles.sentenceText}> and I stake </Text>
            <View style={styles.amountBlankContainer}>
              <Text style={styles.dollarSign}>$</Text>
              <TextInput
                style={styles.amountBlankInput}
                placeholder="5"
                placeholderTextColor={colors.textMuted}
                value={formData.amountPerReport > 0 ? formData.amountPerReport.toString() : ''}
                onChangeText={(text) => onUpdateAmount(parseFloat(text) || 0)}
                keyboardType="decimal-pad"
                accessibilityLabel="Amount per report"
              />
            </View>
            <Text style={styles.sentenceText}> per report</Text>
          </View>
        )}
      </View>

      {/* Modals */}
      <EvidenceSelector
        selectedEvidence={formData.evidenceType}
        onEvidenceSelect={onUpdateEvidenceType}
        visible={showEvidenceModal}
        onClose={() => setShowEvidenceModal(false)}
      />

      <FrequencySelector
        selectedFrequency={formData.reportingFrequency}
        onFrequencySelect={onUpdateReportingFrequency}
        visible={showFrequencyModal}
        onClose={() => setShowFrequencyModal(false)}
      />

      <RecipientSelector
        selectedRecipient={formData.recipient}
        onRecipientSelect={onUpdateRecipient}
        visible={showRecipientModal}
        onClose={() => setShowRecipientModal(false)}
      />

      <TimingSelector
        selectedTiming={formData.timingType}
        onTimingSelect={onUpdateTimingType}
        visible={showTimingModal}
        onClose={() => setShowTimingModal(false)}
      />

      {showDatePicker && (
        <DateSelector
          visible={true}
          onClose={() => setShowDatePicker(null)}
          onDateSelect={(date) => {
            if (showDatePicker === 'start') {
              onUpdateStartDate(date);
            } else {
              onUpdateEndDate(date);
            }
            setShowDatePicker(null);
          }}
          title={`Select ${showDatePicker === 'start' ? 'Start' : 'End'} Date`}
          selectedDate={showDatePicker === 'start' ? formData.startDate : formData.endDate}
          {...datePickerConstraints}
        />
      )}

      <TimeSelector
        visible={showTimePicker !== null}
        onClose={() => setShowTimePicker(null)}
        onTimeSelect={handleTimeSelect}
        initialTime={getCurrentTime()}
        title={getTimePickerTitle()}
      />

      <LocationPicker
        visible={showLocationPicker}
        onClose={() => setShowLocationPicker(false)}
        onLocationSelect={onUpdateLocationData}
        initialLocation={formData.locationData}
      />
    </View>
  );
};

export const SentenceForm = React.memo(SentenceFormComponent);

interface ThemeColors {
  surface: string;
  text: string;
  textMuted: string;
  primary: string;
  background: string;
  border: string;
}

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  sentenceContainer: {
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  sentenceForm: {
    paddingHorizontal: 0,
    paddingVertical: 0,
  },
  sentenceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    marginVertical: 8,
  },
  sentenceColumn: {
    marginVertical: 8,
  },
  inputSection: {
    marginVertical: 12,
  },
  sentenceText: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: colors.text,
    lineHeight: 32,
    marginVertical: 4,
  },
  commitmentInput: {
    backgroundColor: colors.background,
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    fontFamily: 'MontserratBold',
    color: colors.text,
    borderWidth: 1,
    borderColor: colors.border,
    minHeight: 60,
    textAlignVertical: 'top',
  },
  inlineInputContainer: {
    backgroundColor: colors.background,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.border,
    marginHorizontal: 4,
    minWidth: 120,
  },
  inlineInput: {
    padding: 12,
    fontSize: 16,
    fontFamily: 'MontserratRegular',
    color: colors.text,
    textAlign: 'center',
  },
  numberInputContainer: {
    backgroundColor: '#FFEB3B',
    borderRadius: 6,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderWidth: 1,
    borderColor: '#FFEB3B',
    marginHorizontal: 4,
    minWidth: 40,
    alignItems: 'center',
  },
  numberBlankInput: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    color: '#000000',
    textAlign: 'center',
    minWidth: 20,
  },
  amountBlankContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surface,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 2,
    borderWidth: 1,
    borderColor: colors.border,
    marginVertical: 4,
  },
  dollarSign: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: colors.primary,
    marginRight: 5,
  },
  amountBlankInput: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: colors.text,
    width: 60,
    textAlign: 'center',
  },
  locationSection: {
    marginVertical: 8,
  },
  characterCount: {
    fontSize: 12,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
    textAlign: 'right',
    marginTop: 4,
  },
});
