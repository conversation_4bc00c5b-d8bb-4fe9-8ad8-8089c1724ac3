/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * These colors work in conjunction with the ThemeContext for comprehensive theming support.
 */

const tintColorLight = '#FFEB3B'; // Brand yellow
const tintColorDark = '#FFEB3B'; // Brand yellow

export const Colors = {
  light: {
    // Basic colors for compatibility with existing useThemeColor hook
    text: '#11181C',
    background: '#FFFFFF',
    tint: tintColorLight,
    icon: '#687076',
    tabIconDefault: '#687076',
    tabIconSelected: tintColorLight,

    // Extended color palette
    surface: '#F5F5F5',
    card: '#FFFFFF',
    header: '#FFFFFF',
    textSecondary: '#11181C',
    textMuted: '#666666',
    primary: '#FFEB3B',
    border: '#E0E0E0',
    separator: '#EEEEEE',
    success: '#4CAF50',
    warning: '#FF9800',
    error: '#E53935',
    info: '#1E88E5',
  },
  dark: {
    // Basic colors for compatibility with existing useThemeColor hook
    text: '#ECEDEE',
    background: '#000000',
    tint: tintColorDark,
    icon: '#9BA1A6',
    tabIconDefault: '#9BA1A6',
    tabIconSelected: tintColorDark,

    // Extended color palette
    surface: '#1F1F1F',
    card: '#2A2A2A',
    header: '#1e1e1e',
    textSecondary: '#ECEDEE',
    textMuted: '#aaaaaa',
    primary: '#FFEB3B',
    border: '#333333',
    separator: '#444444',
    success: '#4CAF50',
    warning: '#FF9800',
    error: '#E53935',
    info: '#1E88E5',
  },
};

