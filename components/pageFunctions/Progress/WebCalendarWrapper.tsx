import React, { useEffect, useRef } from 'react';
import { View, Platform } from 'react-native';

interface WebCalendarWrapperProps {
  children: React.ReactNode;
  onKeyboardNavigation?: (direction: 'up' | 'down' | 'left' | 'right' | 'enter') => void;
}

/**
 * Web-specific wrapper for calendar components that adds keyboard navigation
 * and other web-specific enhancements
 */
export const WebCalendarWrapper: React.FC<WebCalendarWrapperProps> = ({
  children,
  onKeyboardNavigation,
}) => {
  const containerRef = useRef<View>(null);

  useEffect(() => {
    if (Platform.OS !== 'web' || !onKeyboardNavigation) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      // Only handle keyboard events when the calendar is focused
      if (!containerRef.current) return;

      switch (event.key) {
        case 'ArrowUp':
          event.preventDefault();
          onKeyboardNavigation('up');
          break;
        case 'ArrowDown':
          event.preventDefault();
          onKeyboardNavigation('down');
          break;
        case 'ArrowLeft':
          event.preventDefault();
          onKeyboardNavigation('left');
          break;
        case 'ArrowRight':
          event.preventDefault();
          onKeyboardNavigation('right');
          break;
        case 'Enter':
        case ' ':
          event.preventDefault();
          onKeyboardNavigation('enter');
          break;
      }
    };

    // Add event listener to document for keyboard navigation
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [onKeyboardNavigation]);

  if (Platform.OS !== 'web') {
    // On mobile, just return children without wrapper
    return <>{children}</>;
  }

  return (
    <View
      ref={containerRef}
      style={{
        outline: 'none',
      }}
      // Web-specific props
      {...(Platform.OS === 'web' && {
        tabIndex: 0,
        role: 'grid',
        'aria-label': 'Calendar',
      })}
    >
      {children}
    </View>
  );
};

/**
 * Hook to provide keyboard navigation state for calendar components
 */
export const useCalendarKeyboardNavigation = (
  selectedDate: string | null,
  onDateSelect: (date: string, programs: any[]) => void,
  calendarData: any[]
) => {
  const [focusedDate, setFocusedDate] = React.useState<string | null>(selectedDate);

  const handleKeyboardNavigation = React.useCallback((direction: 'up' | 'down' | 'left' | 'right' | 'enter') => {
    if (!calendarData || calendarData.length === 0) return;

    const currentIndex = focusedDate 
      ? calendarData.findIndex(day => day.date === focusedDate)
      : calendarData.findIndex(day => day.date === selectedDate);

    let newIndex = currentIndex;

    switch (direction) {
      case 'left':
        newIndex = Math.max(0, currentIndex - 1);
        break;
      case 'right':
        newIndex = Math.min(calendarData.length - 1, currentIndex + 1);
        break;
      case 'up':
        newIndex = Math.max(0, currentIndex - 7);
        break;
      case 'down':
        newIndex = Math.min(calendarData.length - 1, currentIndex + 7);
        break;
      case 'enter':
        if (currentIndex >= 0 && calendarData[currentIndex]) {
          const day = calendarData[currentIndex];
          if (day.isCurrentMonth) {
            onDateSelect(day.date, day.programs || []);
          }
        }
        return;
    }

    if (newIndex >= 0 && newIndex < calendarData.length) {
      const newDay = calendarData[newIndex];
      if (newDay && newDay.isCurrentMonth) {
        setFocusedDate(newDay.date);
      }
    }
  }, [focusedDate, selectedDate, calendarData, onDateSelect]);

  // Update focused date when selected date changes
  React.useEffect(() => {
    setFocusedDate(selectedDate);
  }, [selectedDate]);

  return {
    focusedDate,
    handleKeyboardNavigation,
  };
};
