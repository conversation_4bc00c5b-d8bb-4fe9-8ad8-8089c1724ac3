// Main exports for the centralized database service
export { firestoreService, FirestoreService } from './FirestoreService';

// Individual service exports for advanced usage
export { UserService } from './UserService';
export { ProgramService } from './ProgramService';
export { ParticipantService } from './ParticipantService';
export { SubmissionService } from './SubmissionService';
export { NotificationService } from './NotificationService';
export { ChatService } from './ChatService';
export { TransactionService } from './TransactionService';
export { CommitService } from './CommitService';
export { BaseService } from './BaseService';

// Type exports
export * from './types';

// Utility functions for common database operations
import { firestoreService } from './FirestoreService';
import { ServiceResponse } from './types';

/**
 * Utility functions for common database operations
 * These provide a simplified API for the most common use cases
 */

// User utilities
export const createUser = (userData: any) => firestoreService.users.createUser(userData);
export const getUserById = (userId: string) => firestoreService.users.getUserById(userId);
export const updateUserProfile = (userId: string, profileData: any) => 
  firestoreService.users.updateUserProfile(userId, profileData);
export const getUserPrograms = (userId: string) => firestoreService.users.getUserPrograms(userId);

// Program utilities
export const getAllPrograms = () => firestoreService.programs.getAllPrograms();
export const getProgramById = (programId: string) => firestoreService.programs.getProgramById(programId);
export const getActivePrograms = () => firestoreService.programs.getActivePrograms();
export const getProgramsByCategory = (category: string) => 
  firestoreService.programs.getProgramsByCategory(category);

// Participant utilities
export const enrollInProgram = (userId: string, programId: string, enrollmentData: any) =>
  firestoreService.enrollUserInProgram(userId, programId, enrollmentData);
export const getParticipant = (programId: string, userId: string) =>
  firestoreService.participants.getParticipant(programId, userId);
export const updateParticipantSetup = (userId: string, programId: string, setupVar: string, category: string) =>
  firestoreService.completeUserSetup(userId, programId, setupVar, category);

// Submission utilities
export const submitDayProgress = (userId: string, programId: string, dayId: string, attachment?: string) =>
  firestoreService.submitDayProgress(userId, programId, dayId, attachment);
export const bailOutForDay = (userId: string, programId: string, dayId: string) =>
  firestoreService.bailOutForDay(userId, programId, dayId);
export const getSubmissionStats = (programId: string, userId: string) =>
  firestoreService.submissions.getSubmissionStats(programId, userId);

// Commit utilities
export const createCommit = (userId: string, commitData: any) =>
  firestoreService.commits.createFullCommit(userId, commitData);
export const getUserCommits = (userId: string) =>
  firestoreService.commits.getCommitsByUser(userId);
export const getActiveUserCommits = (userId: string) =>
  firestoreService.commits.getActiveCommitsByUser(userId);
export const updateCommitStatus = (userId: string, commitId: string, status: 'active' | 'completed' | 'failed' | 'cancelled') =>
  firestoreService.commits.updateCommitStatus(userId, commitId, status);

// Commit submission utilities
export const getCommitSubmissions = (commitId: string) =>
  firestoreService.commits.getCommitSubmissions(commitId);
export const getCommitSubmission = (commitId: string, submissionId: string) =>
  firestoreService.commits.getCommitSubmission(commitId, submissionId);
export const updateCommitSubmission = (commitId: string, submissionId: string, submissionData: any) =>
  firestoreService.commits.updateCommitSubmission(commitId, submissionId, submissionData);

// New helper utilities for frequency-based submissions
export const getSubmissionDetails = (
  commitId: string,
  frequency: 'daily' | 'weekly' | 'monthly' | 'once',
  periodId: string,
  submissionId?: string
) =>
  firestoreService.commits.getSubmissionDetails(commitId, frequency, periodId, submissionId);

export const updateSubmissionDetails = (
  commitId: string,
  frequency: 'daily' | 'weekly' | 'monthly' | 'once',
  periodId: string,
  submissionData: any,
  submissionId?: string
) =>
  firestoreService.commits.updateSubmissionDetails(commitId, frequency, periodId, submissionData, submissionId);

// Weekly submission utilities
export const getWeekSubmissions = (commitId: string, weekId: string) =>
  firestoreService.commits.getWeekSubmissions(commitId, weekId);
export const getWeekSubmission = (commitId: string, weekId: string, submissionId: string) =>
  firestoreService.commits.getWeekSubmission(commitId, weekId, submissionId);
export const updateWeekSubmission = (commitId: string, weekId: string, submissionId: string, submissionData: any) =>
  firestoreService.commits.updateWeekSubmission(commitId, weekId, submissionId, submissionData);

// Monthly submission utilities
export const getMonthSubmissions = (commitId: string, monthId: string) =>
  firestoreService.commits.getMonthSubmissions(commitId, monthId);
export const getMonthSubmission = (commitId: string, monthId: string, submissionId: string) =>
  firestoreService.commits.getMonthSubmission(commitId, monthId, submissionId);
export const updateMonthSubmission = (commitId: string, monthId: string, submissionId: string, submissionData: any) =>
  firestoreService.commits.updateMonthSubmission(commitId, monthId, submissionId, submissionData);

// Period utilities
export const getCommitWeeks = (commitId: string) =>
  firestoreService.commits.getCommitWeeks(commitId);
export const getCommitMonths = (commitId: string) =>
  firestoreService.commits.getCommitMonths(commitId);

// Analytics utilities
export const createAnalyticsRecord = (userId: string, analyticsData: any) =>
  firestoreService.analytics.createAnalyticsRecord(userId, analyticsData);
export const getUserAnalytics = (userId: string, options?: any) =>
  firestoreService.analytics.getUserAnalytics(userId, options);
export const recordUserLogin = (userId: string, loginData: any) =>
  firestoreService.analytics.recordUserLogin(userId, loginData);
export const getAnalyticsStats = (userId: string, startDate?: string, endDate?: string) =>
  firestoreService.analytics.getAnalyticsStats(userId, startDate, endDate);
export const cleanupOldAnalytics = (userId: string, daysToKeep?: number) =>
  firestoreService.analytics.cleanupOldAnalytics(userId, daysToKeep);

// Notification utilities
export const createNotification = (userId: string, notificationData: any) =>
  firestoreService.notifications.createNotification(userId, notificationData);
export const getUnreadNotifications = (userId: string) =>
  firestoreService.notifications.getUnreadNotifications(userId);
export const markNotificationAsRead = (userId: string, notificationId: string) =>
  firestoreService.notifications.markAsRead(userId, notificationId);
export const subscribeToUnreadCount = (userId: string, callback: (count: number) => void) =>
  firestoreService.notifications.subscribeToUnreadCount(userId, callback);

// Chat utilities
export const sendChatMessage = (programId: string, messageData: any) =>
  firestoreService.chat.sendMessage(programId, messageData);
export const subscribeToChatMessages = (programId: string, callback: (messages: any[]) => void, limit?: number) =>
  firestoreService.chat.subscribeToMessages(programId, callback, limit);

// Transaction utilities
export const createEnrollmentTransaction = (userId: string, programId: string, programName: string, betAmount: number, paymentData?: any) =>
  firestoreService.transactions.createProgramEnrollmentTransaction(userId, programId, programName, betAmount, paymentData);
export const createCashOutTransaction = (userId: string, programId: string, programName: string, totalPayout: number, breakdown: any) =>
  firestoreService.transactions.createCashOutTransaction(userId, programId, programName, totalPayout, breakdown);
export const getCashOutTransaction = (userId: string, programId: string) =>
  firestoreService.transactions.getCashOutTransaction(userId, programId);
export const updateCashOutStatus = (userId: string, programId: string, status: 'pending' | 'payment_done' | 'failed') =>
  firestoreService.transactions.updateCashOutStatus(userId, programId, status);
export const getUserTransactions = (userId: string) =>
  firestoreService.transactions.getAllTransactions(userId);

// Dispute utilities
export const createDispute = (disputeData: any) => firestoreService.createDispute(disputeData);
export const getDisputesByUser = (userEmail: string) => firestoreService.getDisputesByUser(userEmail);

// Dashboard utilities
export const getUserDashboard = (userId: string) => firestoreService.getUserDashboardData(userId);
export const getProgramDashboard = (programId: string) => firestoreService.getProgramDashboardData(programId);

// Subscription management utilities
export const addSubscription = (key: string, unsubscribe: () => void) =>
  firestoreService.addSubscription(key, unsubscribe);
export const removeSubscription = (key: string) => firestoreService.removeSubscription(key);
export const cleanupSubscriptions = () => firestoreService.cleanup();

/**
 * Legacy compatibility functions
 * These maintain backward compatibility with existing code
 */

// Legacy fetchUserData function
export const fetchUserData = async (email: string): Promise<boolean> => {
  try {
    const result = await getUserById(email);
    if (result.success && result.data) {
      // Update local variables (you'll need to import these from your variables file)
      // updateToken(true);
      // updateFname(result.data.fname);
      // updateLname(result.data.lname);
      // updateId(result.data.email);
      return true;
    } else {
      return false;
    }
  } catch (error) {
    console.error("Error fetching user data from Firestore", error);
    return false;
  }
};

// Legacy notification creation (for backward compatibility)
export const createLegacyNotification = async (
  userEmail: string,
  notification: {
    title: string;
    message: string;
    type: "account" | "program" | "points" | "reminder";
    priority: "low" | "medium" | "high";
  }
): Promise<void> => {
  try {
    await createNotification(userEmail, notification);
  } catch (error) {
    console.error('Error creating notification:', error);
    throw error;
  }
};

/**
 * Error handling utilities
 */
export const isServiceError = (result: ServiceResponse<any>): boolean => {
  return !result.success;
};

export const getServiceError = (result: ServiceResponse<any>): string | undefined => {
  return result.error;
};

export const handleServiceError = (result: ServiceResponse<any>, defaultMessage: string = 'An error occurred'): string => {
  return result.error || defaultMessage;
};

/**
 * Type guards for service responses
 */
export const isSuccessResponse = <T>(result: ServiceResponse<T>): result is ServiceResponse<T> & { success: true; data: T } => {
  return result.success && result.data !== undefined;
};

export const isErrorResponse = <T>(result: ServiceResponse<T>): result is ServiceResponse<T> & { success: false; error: string } => {
  return !result.success;
};

/**
 * Validation utilities
 */
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validateUserData = (userData: any) => {
  return firestoreService.users.validateUserData(userData);
};

export const validateProgramData = (programData: any) => {
  return firestoreService.programs.validateProgramData(programData);
};

export const validateParticipantData = (participantData: any) => {
  return firestoreService.participants.validateParticipantData(participantData);
};

/**
 * Batch operation utilities
 */
export const batchUpdatePrograms = (updates: Array<{ programId: string; data: any }>) =>
  firestoreService.programs.updateMultiplePrograms(updates);

export const batchUpdateSubmissions = (updates: Array<{ programId: string; userId: string; dayId: string; data: any }>) =>
  firestoreService.submissions.updateMultipleSubmissions(updates);

/**
 * Real-time subscription helpers
 */
export const createManagedSubscription = (
  key: string,
  subscriptionFn: () => (() => void)
): void => {
  const unsubscribe = subscriptionFn();
  addSubscription(key, unsubscribe);
};

// Export the main service instance as default
export default firestoreService;
