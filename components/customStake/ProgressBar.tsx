import React from 'react';
import {
  View,
} from 'react-native';
import { useTheme } from '@/contexts/ThemeContext';

interface ProgressBarProps {
  currentStep: number;
  totalSteps: number;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  currentStep,
  totalSteps,
}) => {
  const { colors } = useTheme();

  const progress = (currentStep / totalSteps) * 100;

  const styles = {
    container: {
      backgroundColor: colors.background,
      paddingHorizontal: 20,
      paddingVertical: 8, // Reduced from 16 to reduce space above and below
      alignItems: 'center' as const,
    },
    stepContainer: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
    },
    stepCircle: {
      width: 12,
      height: 12,
      borderRadius: 6,
      backgroundColor: '#FFFFFF', // White for not yet done/selected
      marginHorizontal: 4,
      borderWidth: 1,
      borderColor: colors.border,
    },
    stepCircleActive: {
      backgroundColor: '#FFEB3B', // Yellow when selected and done
      borderColor: '#FFEB3B',
    },
    stepConnector: {
      width: 40,
      height: 2,
      backgroundColor: '#FFFFFF', // White for not yet done
      marginHorizontal: 4,
    },
    stepConnectorActive: {
      backgroundColor: '#FFEB3B', // Yellow when active
    },
  };

  return (
    <View style={styles.container}>
      <View style={styles.stepContainer}>
        {/* Step 1 Circle */}
        <View style={[
          styles.stepCircle,
          currentStep >= 1 && styles.stepCircleActive
        ]} />

        {/* Connector between step 1 and 2 */}
        <View style={[
          styles.stepConnector,
          currentStep >= 2 && styles.stepConnectorActive
        ]} />

        {/* Step 2 Circle */}
        <View style={[
          styles.stepCircle,
          currentStep >= 2 && styles.stepCircleActive
        ]} />

        {/* Connector between step 2 and 3 */}
        <View style={[
          styles.stepConnector,
          currentStep >= 3 && styles.stepConnectorActive
        ]} />

        {/* Step 3 Circle */}
        <View style={[
          styles.stepCircle,
          currentStep >= 3 && styles.stepCircleActive
        ]} />
      </View>
    </View>
  );
};
