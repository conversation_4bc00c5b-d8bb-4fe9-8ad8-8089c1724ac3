{"extension": "linecounter", "version": "0.2.7", "workspace": "c:\\Users\\<USER>\\Desktop\\hab.bets", "linecount": [{"version": "0.2.7", "counttime": "2025-04-25 00:36:36", "filesum": 121, "codesum": 34290, "commentsum": 6619, "blanksum": 792, "statistics": {"": {"code": 29, "comment": 1, "blank": 9}, ".ts": {"code": 238, "comment": 25, "blank": 38}, ".tsx": {"code": 8596, "comment": 276, "blank": 700}, ".json": {"code": 15039, "comment": 0, "blank": 24}, ".md": {"code": 8, "comment": 0, "blank": 1}, ".css": {"code": 19, "comment": 3, "blank": 3}, ".js": {"code": 1346, "comment": 6314, "blank": 17}, ".html": {"code": 9015, "comment": 0, "blank": 0}}, "filelist": [{"blank": 0, "code": 3, "comment": 0, "filename": ".expo\\devices.json"}, {"blank": 0, "code": 8, "comment": 0, "filename": ".expo\\README.md"}, {"blank": 0, "code": 3, "comment": 0, "filename": ".expo\\settings.json"}, {"blank": 2, "code": 11, "comment": 1, "filename": ".expo\\types\\router.d.ts"}, {"filename": ".expo\\web\\cache\\production\\images\\favicon\\favicon-24272cdaeff82cc5facdaccd982a6f05b60c4504704bbf94c19a6388659880bb-contain-transparent\\favicon-48.png", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"blank": 9, "code": 29, "comment": 1, "filename": ".giti<PERSON>re"}, {"blank": 1, "code": 55, "comment": 0, "filename": "app.json"}, {"blank": 5, "code": 70, "comment": 1, "filename": "app\\(tabs)\\_layout.tsx"}, {"blank": 23, "code": 324, "comment": 2, "filename": "app\\(tabs)\\index.tsx"}, {"blank": 15, "code": 222, "comment": 9, "filename": "app\\(tabs)\\points.tsx"}, {"blank": 28, "code": 253, "comment": 10, "filename": "app\\(tabs)\\profile.tsx"}, {"blank": 56, "code": 742, "comment": 22, "filename": "app\\(tabs)\\progress.tsx"}, {"blank": 2, "code": 11, "comment": 0, "filename": "app\\+not-found.tsx"}, {"blank": 8, "code": 84, "comment": 3, "filename": "app\\_layout.tsx"}, {"blank": 1, "code": 21, "comment": 1, "filename": "app\\CommonInterface.ts"}, {"blank": 15, "code": 177, "comment": 4, "filename": "app\\ConfirmDelete.tsx"}, {"blank": 14, "code": 145, "comment": 13, "filename": "app\\Disputes.tsx"}, {"blank": 8, "code": 115, "comment": 3, "filename": "app\\EmailSettings.tsx"}, {"blank": 3, "code": 19, "comment": 3, "filename": "app\\global.css"}, {"blank": 28, "code": 287, "comment": 10, "filename": "app\\Notifications.tsx"}, {"blank": 31, "code": 448, "comment": 5, "filename": "app\\ProgramDetails.tsx"}, {"blank": 14, "code": 192, "comment": 6, "filename": "app\\PushNotifications.tsx"}, {"blank": 80, "code": 876, "comment": 26, "filename": "app\\SignIn\\index.tsx"}, {"blank": 9, "code": 66, "comment": 2, "filename": "app\\Support.tsx"}, {"blank": 20, "code": 254, "comment": 0, "filename": "app\\UserProfile.tsx"}, {"blank": 0, "code": 1, "comment": 0, "filename": "assets\\animations\\loading_1.json"}, {"blank": 0, "code": 1, "comment": 0, "filename": "assets\\animations\\loading_2.json"}, {"blank": 0, "code": 1, "comment": 0, "filename": "assets\\animations\\loading_3.json"}, {"filename": "assets\\avatars\\0.png", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "assets\\avatars\\14.png", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "assets\\avatars\\21.png", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "assets\\avatars\\3.png", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "assets\\avatars\\30.png", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "assets\\avatars\\45.png", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "assets\\avatars\\7.png", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "assets\\avatars\\temp\\2.png", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "assets\\avatars\\temp\\4.png", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "assets\\fonts\\Montserrat-Bold.ttf", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "assets\\fonts\\Montserrat-MediumItalic.ttf", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "assets\\fonts\\Montserrat-Regular.ttf", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "assets\\fonts\\SpaceMono-Regular copy.ttf", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "assets\\fonts\\SpaceMono-Regular.ttf", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "assets\\images\\adaptive-icon.png", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "assets\\images\\favicon.png", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "assets\\images\\icon.png", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "assets\\images\\logoBig.png", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "assets\\images\\partial-react-logo.png", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "assets\\images\\react-logo.png", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "assets\\images\\<EMAIL>", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "assets\\images\\<EMAIL>", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "assets\\images\\splash-icon.png", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"blank": 1, "code": 16, "comment": 1, "filename": "components\\HapticTab.tsx"}, {"blank": 9, "code": 86, "comment": 1, "filename": "components\\Header.tsx"}, {"blank": 5, "code": 61, "comment": 1, "filename": "components\\pageFunctions\\Discover\\CategoryFilter.tsx"}, {"blank": 10, "code": 282, "comment": 2, "filename": "components\\pageFunctions\\Discover\\FAQSection.tsx"}, {"blank": 4, "code": 64, "comment": 2, "filename": "components\\pageFunctions\\Discover\\OctagonStamp.tsx"}, {"blank": 12, "code": 285, "comment": 2, "filename": "components\\pageFunctions\\Points\\CharacterUpgrade.tsx"}, {"blank": 12, "code": 204, "comment": 4, "filename": "components\\pageFunctions\\Points\\LeaderBoard.tsx"}, {"blank": 52, "code": 555, "comment": 45, "filename": "components\\pageFunctions\\Points\\Rewards.tsx"}, {"blank": 5, "code": 91, "comment": 0, "filename": "components\\pageFunctions\\Points\\StreakAndPercentile.tsx"}, {"blank": 6, "code": 101, "comment": 3, "filename": "components\\pageFunctions\\Progress\\BailoutModal.tsx"}, {"blank": 7, "code": 43, "comment": 1, "filename": "components\\pageFunctions\\Progress\\BreathingDot.tsx"}, {"blank": 26, "code": 162, "comment": 12, "filename": "components\\pageFunctions\\Progress\\CategoryInput.tsx"}, {"blank": 11, "code": 210, "comment": 4, "filename": "components\\pageFunctions\\Progress\\CategorySetup.tsx"}, {"blank": 13, "code": 99, "comment": 0, "filename": "components\\pageFunctions\\Progress\\DisqualifiedStats.tsx"}, {"blank": 26, "code": 231, "comment": 8, "filename": "components\\pageFunctions\\Progress\\DownloadReport.tsx"}, {"blank": 5, "code": 87, "comment": 1, "filename": "components\\pageFunctions\\Progress\\MoneyContainer.tsx"}, {"blank": 6, "code": 163, "comment": 1, "filename": "components\\pageFunctions\\Progress\\MoneyOverview.tsx"}, {"blank": 21, "code": 358, "comment": 10, "filename": "components\\pageFunctions\\Progress\\ParticipantsStatusDashboard.tsx"}, {"blank": 12, "code": 216, "comment": 0, "filename": "components\\pageFunctions\\Progress\\ProgramChat.tsx"}, {"blank": 8, "code": 149, "comment": 2, "filename": "components\\pageFunctions\\Progress\\ProgramDropDown.tsx"}, {"blank": 6, "code": 86, "comment": 0, "filename": "components\\pageFunctions\\Progress\\ProgramEnd.tsx"}, {"blank": 11, "code": 146, "comment": 19, "filename": "components\\pageFunctions\\Progress\\ProgressBar.tsx"}, {"blank": 34, "code": 235, "comment": 15, "filename": "components\\pageFunctions\\Progress\\SubmissionStatusBoard.tsx"}, {"blank": 41, "code": 392, "comment": 26, "filename": "components\\pageFunctions\\Progress\\WinnerShare.tsx"}, {"blank": 4, "code": 29, "comment": 0, "filename": "components\\pageFunctions\\SignUp\\fetchUserData.ts"}, {"blank": 1, "code": 8, "comment": 0, "filename": "components\\pageFunctions\\SignUp\\redirector.tsx"}, {"blank": 6, "code": 33, "comment": 6, "filename": "config\\firebase.ts"}, {"blank": 2, "code": 20, "comment": 4, "filename": "constants\\Colors.ts"}, {"blank": 2, "code": 1287, "comment": 6304, "filename": "dist\\client\\_expo\\static\\js\\web\\entry-39c2f1d4c321adbbf1d3279c1938d7cd.js"}, {"filename": "dist\\client\\assets\\assets\\avatars\\0.7fd65815c3236336cb339ba8ce688056.png", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "dist\\client\\assets\\assets\\avatars\\14.88d975134621b0b8ebd432d0326e7a8c.png", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "dist\\client\\assets\\assets\\avatars\\21.b643dba58c6f28e0122fe6d27ecdf8fc.png", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "dist\\client\\assets\\assets\\avatars\\3.7789e3cc38bd30b2ae94fb641d043ac0.png", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "dist\\client\\assets\\assets\\avatars\\30.d479114bb617a0de87d650ed3e421402.png", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "dist\\client\\assets\\assets\\avatars\\45.33ec9c9cabe130120a0dd29f923af71e.png", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "dist\\client\\assets\\assets\\avatars\\7.6824e6ec33a9b7cd05c500b8d24e1e1a.png", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "dist\\client\\assets\\assets\\fonts\\Montserrat-Bold.354dc625a35bef1b6ec00a79c6cfc0c8.ttf", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "dist\\client\\assets\\assets\\fonts\\Montserrat-MediumItalic.aaba9a9046de09a1ace6971fd7dd3b8a.ttf", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "dist\\client\\assets\\assets\\fonts\\Montserrat-Regular.38712903602f88435ddddec98862f8b8.ttf", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "dist\\client\\assets\\assets\\fonts\\SpaceMono-Regular.49a79d66bdea2debf1832bf4d7aca127.ttf", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "dist\\client\\assets\\assets\\images\\logoBig.fc5d83bc27a436a5706036f05b43e905.png", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"filename": "dist\\client\\favicon.ico", "isbinaryfile": true, "blank": 0, "code": 0, "comment": 0}, {"blank": 0, "code": 601, "comment": 0, "filename": "dist\\server\\(tabs)\\index.html"}, {"blank": 0, "code": 601, "comment": 0, "filename": "dist\\server\\(tabs)\\points.html"}, {"blank": 0, "code": 601, "comment": 0, "filename": "dist\\server\\(tabs)\\profile.html"}, {"blank": 0, "code": 601, "comment": 0, "filename": "dist\\server\\(tabs)\\progress.html"}, {"blank": 0, "code": 601, "comment": 0, "filename": "dist\\server\\+not-found.html"}, {"blank": 0, "code": 100, "comment": 0, "filename": "dist\\server\\_expo\\routes.json"}, {"blank": 0, "code": 601, "comment": 0, "filename": "dist\\server\\_sitemap.html"}, {"blank": 0, "code": 601, "comment": 0, "filename": "dist\\server\\ConfirmDelete.html"}, {"blank": 0, "code": 601, "comment": 0, "filename": "dist\\server\\Disputes.html"}, {"blank": 0, "code": 601, "comment": 0, "filename": "dist\\server\\EmailSettings.html"}, {"blank": 0, "code": 601, "comment": 0, "filename": "dist\\server\\Notifications.html"}, {"blank": 0, "code": 601, "comment": 0, "filename": "dist\\server\\ProgramDetails.html"}, {"blank": 0, "code": 601, "comment": 0, "filename": "dist\\server\\PushNotifications.html"}, {"blank": 0, "code": 601, "comment": 0, "filename": "dist\\server\\SignIn\\index.html"}, {"blank": 0, "code": 601, "comment": 0, "filename": "dist\\server\\Support.html"}, {"blank": 0, "code": 601, "comment": 0, "filename": "dist\\server\\UserProfile.html"}, {"blank": 0, "code": 23, "comment": 0, "filename": "eas.json"}, {"blank": 1, "code": 0, "comment": 2, "filename": "expo-env.d.ts"}, {"blank": 0, "code": 1, "comment": 0, "filename": "hooks\\useColorScheme.ts"}, {"blank": 5, "code": 13, "comment": 3, "filename": "hooks\\useColorScheme.web.ts"}, {"blank": 3, "code": 14, "comment": 4, "filename": "hooks\\useThemeColor.ts"}, {"blank": 21, "code": 14768, "comment": 0, "filename": "package-lock.json"}, {"blank": 0, "code": 68, "comment": 0, "filename": "package.json"}, {"blank": 1, "code": 0, "comment": 0, "filename": "README.md"}, {"blank": 15, "code": 59, "comment": 10, "filename": "scripts\\reset-project.js"}, {"blank": 2, "code": 16, "comment": 0, "filename": "tsconfig.json"}, {"blank": 1, "code": 23, "comment": 0, "filename": "utilis\\notifications.ts"}, {"blank": 13, "code": 73, "comment": 4, "filename": "utilis\\variables.ts"}]}]}