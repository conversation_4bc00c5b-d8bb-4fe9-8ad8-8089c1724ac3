import { StyleSheet } from "react-native";
import { Text, View } from "react-native";
import CategoryInput from "./CategoryInput";
import { DayTile, ParticipantList, Program } from "@/types/CommonInterface";
import { useCameraPermissions } from "expo-camera";
import { useTheme } from "@/contexts/ThemeContext";

interface ContentAreaProps {
    userId: string;
    selectedProgram: Program;
    selectedDay: number;
    currentDay: number;
    days: DayTile[];
    participantsList: ParticipantList[];
    submissionTriggered: boolean;
    loadingStatuses: boolean[];
    submissionTrigger: (submissionTriggered: boolean) => void;
}

export const ContentArea: React.FC<ContentAreaProps> = ({userId, selectedProgram, selectedDay, currentDay, days, participantsList, submissionTriggered, loadingStatuses, submissionTrigger}) => {
    const [permission, requestPermission] = useCameraPermissions();
    const { colors } = useTheme();
    const styles = createStyles(colors);
  
    if (selectedProgram.duration && selectedDay > selectedProgram.duration) {
    return (
      <View style={styles.messageContainer}>
        <Text style={styles.messageText}>Program ended!</Text>
      </View>
    );
  }

  const selectedDayTile = days.find((d) => d.day === selectedDay);
  const currentUserSubmissionStatus = participantsList.find(
    (p) => p.id === userId
  )?.status;

  if (selectedDay < currentDay) {
    // Past day: if the day's status is still upcoming, indicate the day was missed.
    if (selectedDayTile?.status === "upcoming") {
      return (
        <View style={styles.messageContainer}>
          <Text style={styles.messageText}>
            You missed the day, lock in next time.
          </Text>
        </View>
      );
    } else if (selectedDayTile?.status === "submitted") {
      return (
        <View style={styles.messageContainer}>
          <Text
            style={{
              fontFamily: "MontserratBold",
              color: colors.primary,
              fontSize: 19,
            }}
          >
            Great job! 🎉
          </Text>
          <Text
            style={{
              fontFamily: "MontserratBold",
              color: colors.text,
              fontSize: 17,
            }}
          >
            Submission made for today
          </Text>
          <Text
            style={{
              fontFamily: "MontserratRegular",
              color: colors.textSecondary,
              fontSize: 16,
              textAlign: "center",
              paddingVertical: 10,
            }}
          >
            Please wait for the moderator to evaluate your submission
          </Text>
        </View>
      );
    } else if (selectedDayTile?.status === "verified") {
      return (
        <View style={styles.messageContainer}>
          <Text style={styles.messageText}>
            Awesome! ✅ Your submission was verified by your moderator. Keep
            pushing!
          </Text>
        </View>
      );
    } else if (selectedDayTile?.status === "bailed") {
      return (
        <View style={styles.messageContainer}>
          <Text
            style={{
              fontFamily: "MontserratBold",
              color: colors.warning,
              fontSize: 19,
            }}
          >
            Bailed Out! ⚠️
          </Text>
          <Text
            style={{
              fontFamily: "MontserratBold",
              color: colors.text,
              fontSize: 15,
              textAlign: "center",
              paddingTop: 7,
            }}
          >
            Hope you had a good excuse.
          </Text>
          <Text
            style={{
              fontFamily: "MontserratBold",
              color: colors.text,
              fontSize: 15,
              textAlign: "center",
              paddingTop: 7,
            }}
          >
            We expect you to stay consistent. Stay hard!
          </Text>
        </View>
      );
    }
  } else if (selectedDay === currentDay) {
    // Current day: render the regular submission UI.
    if (currentUserSubmissionStatus === "submitted") {
      return (
        <View style={styles.messageContainer}>
          <Text
            style={{
              fontFamily: "MontserratBold",
              color: colors.primary,
              fontSize: 18,
            }}
          >
            Great job! 🎉
          </Text>
          <Text
            style={{
              fontFamily: "MontserratBold",
              color: colors.text,
              fontSize: 16,
            }}
          >
            Submission made for the day
          </Text>
          <Text
            style={{
              fontFamily: "MontserratRegular",
              color: colors.textSecondary,
              fontSize: 15,
              textAlign: "center",
              paddingVertical: 8,
            }}
          >
            Please wait for the moderator to evaluate your submission
          </Text>
        </View>
      );
    } else if (currentUserSubmissionStatus === "verified") {
      return (
        <View style={styles.messageContainer}>
          <Text style={styles.messageText}>
            Awesome! ✅ Your submission was verified by your moderator. Keep
            pushing!
          </Text>
        </View>
      );
    } else if (currentUserSubmissionStatus === "bailed") {
      return (
        <View style={styles.messageContainer}>
          <Text
            style={{
              fontFamily: "MontserratBold",
              color: colors.warning,
              fontSize: 19,
            }}
          >
            Bailed Out! ⚠️
          </Text>
          <Text
            style={{
              fontFamily: "MontserratBold",
              color: colors.text,
              fontSize: 15,
              textAlign: "center",
              paddingTop: 7,
            }}
          >
            Hope you had a good excuse.
          </Text>
          <Text
            style={{
              fontFamily: "MontserratBold",
              color: colors.text,
              fontSize: 15,
              textAlign: "center",
              paddingTop: 7,
            }}
          >
            We expect you to stay consistent. Stay hard!
          </Text>
        </View>
      );
    } else {
      return (
        <CategoryInput
          key={`${selectedProgram?.id}-${selectedDay}`}
          user_id={userId}
          category={selectedProgram?.category}
          program_id={String(selectedProgram?.id)}
          day={selectedDay}
          permissionGranted={permission?.granted}
          requestPermission={requestPermission}
          livesLeft={
            participantsList.find((p) => p.id === userId)?.livesLeft || 0
          }
          livesPurchaseLeft={
            participantsList.find((p) => p.id === userId)?.livesPurchaseLeft ||
            0
          }
          onSubmissionSuccess={() =>
            submissionTrigger(!submissionTriggered)
          }
          isLoading={loadingStatuses[0] || loadingStatuses[1] || loadingStatuses[2]}
        />
      );
    }
  } else {
    // Future day: prevent submissions
    return (
      <View style={styles.messageContainer}>
        <Text style={styles.messageText}>
          This day hasn't arrived yet. Come back on the day to submit!
        </Text>
      </View>
    );
  }
  return null;
};

const createStyles = (colors: any) => StyleSheet.create({
  messageContainer: {
    marginVertical: 6,
    paddingVertical: 16,
    paddingHorizontal: 16,
    backgroundColor: colors.surface,
    borderRadius: 10,
    alignItems: "center",
  },
  messageText: {
    color: colors.primary,
    fontSize: 17,
    textAlign: "center",
    marginBottom: 8,
  },
});
