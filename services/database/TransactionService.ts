import { BaseService } from './BaseService';
import { Transaction, ServiceResponse, COLLECTIONS, SUBCOLLECTIONS } from './types';

export class TransactionService extends BaseService {
  private getTransactionPath(userId: string): string {
    return `${COLLECTIONS.USERS}/${userId}/${SUBCOLLECTIONS.USER_TRANSACTIONS}`;
  }

  // Transaction CRUD operations
  async createTransaction(
    userId: string,
    transactionId: string,
    transactionData: Omit<Transaction, 'id'>
  ): Promise<ServiceResponse<string>> {
    const collectionPath = this.getTransactionPath(userId);
    return this.createDocument<Transaction>(collectionPath, transactionData, transactionId);
  }

  async getTransaction(
    userId: string,
    transactionId: string
  ): Promise<ServiceResponse<Transaction | null>> {
    const collectionPath = this.getTransactionPath(userId);
    return this.getDocument<Transaction>(collectionPath, transactionId);
  }

  async updateTransaction(
    userId: string,
    transactionId: string,
    transactionData: Partial<Transaction>
  ): Promise<ServiceResponse<void>> {
    const collectionPath = this.getTransactionPath(userId);
    return this.updateDocument<Transaction>(collectionPath, transactionId, transactionData);
  }

  async deleteTransaction(
    userId: string,
    transactionId: string
  ): Promise<ServiceResponse<void>> {
    const collectionPath = this.getTransactionPath(userId);
    return this.deleteDocument(collectionPath, transactionId);
  }

  async getAllTransactions(userId: string): Promise<ServiceResponse<Transaction[]>> {
    const collectionPath = this.getTransactionPath(userId);
    return this.getCollection<Transaction>(collectionPath, {
      orderBy: { field: 'timestamp', direction: 'desc' }
    });
  }

  // Specialized transaction operations
  async createProgramEnrollmentTransaction(
    userId: string,
    programId: string,
    programName: string,
    betAmount: number,
    paymentData?: {
      paymentMethod?: string;
      paymentIntentId?: string;
    }
  ): Promise<ServiceResponse<string>> {
    // Build metadata object, only including defined values
    const metadata: any = {
      betAmount,
    };

    // Only add optional fields if they are defined
    if (paymentData?.paymentMethod) {
      metadata.paymentMethod = paymentData.paymentMethod;
    }

    if (paymentData?.paymentIntentId) {
      metadata.paymentIntentId = paymentData.paymentIntentId;
    }

    const transactionData: Omit<Transaction, 'id'> = {
      programId,
      programName: '', // Remove program name as requested
      type: 'in',
      amount: betAmount,
      status: 'completed',
      description: 'Program enrollment transaction', // Simplified description
      timestamp: new Date().toISOString(),
      metadata,
    };

    return this.createTransaction(userId, 'in', transactionData); // Use simple "in" as document ID
  }

  async createCashOutTransaction(
    userId: string,
    programId: string,
    programName: string,
    totalPayout: number,
    breakdown: {
      betAmount: number;
      winnings: number;
      refund: number;
    }
  ): Promise<ServiceResponse<string>> {
    const transactionData: Omit<Transaction, 'id'> = {
      programId,
      programName: '', // Remove program name as requested
      type: 'out',
      amount: totalPayout,
      status: 'pending',
      description: 'Cash out transaction', // Simplified description
      timestamp: new Date().toISOString(),
      metadata: {
        betAmount: breakdown.betAmount,
        winnings: breakdown.winnings,
        refund: breakdown.refund,
        totalPayout,
      },
    };

    return this.createTransaction(userId, 'out', transactionData); // Use simple "out" as document ID
  }

  async updateCashOutStatus(
    userId: string,
    programId: string,
    status: 'pending' | 'payment_done' | 'failed'
  ): Promise<ServiceResponse<void>> {
    return this.updateTransaction(userId, 'out', { status }); // Use simple "out" document ID
  }

  async getCashOutTransaction(
    userId: string,
    programId: string
  ): Promise<ServiceResponse<Transaction | null>> {
    return this.getTransaction(userId, 'out'); // Use simple "out" document ID
  }

  async getEnrollmentTransaction(
    userId: string,
    programId: string
  ): Promise<ServiceResponse<Transaction | null>> {
    return this.getTransaction(userId, 'in'); // Use simple "in" document ID
  }

  // Transaction queries
  async getTransactionsByType(
    userId: string,
    type: 'in' | 'out'
  ): Promise<ServiceResponse<Transaction[]>> {
    const collectionPath = this.getTransactionPath(userId);
    return this.getCollection<Transaction>(collectionPath, {
      where: [{ field: 'type', operator: '==', value: type }],
      orderBy: { field: 'timestamp', direction: 'desc' }
    });
  }

  async getTransactionsByStatus(
    userId: string,
    status: 'completed' | 'pending' | 'payment_done' | 'failed'
  ): Promise<ServiceResponse<Transaction[]>> {
    const collectionPath = this.getTransactionPath(userId);
    return this.getCollection<Transaction>(collectionPath, {
      where: [{ field: 'status', operator: '==', value: status }],
      orderBy: { field: 'timestamp', direction: 'desc' }
    });
  }

  async getPendingCashOuts(userId: string): Promise<ServiceResponse<Transaction[]>> {
    const collectionPath = this.getTransactionPath(userId);
    return this.getCollection<Transaction>(collectionPath, {
      where: [
        { field: 'type', operator: '==', value: 'out' },
        { field: 'status', operator: '==', value: 'pending' }
      ],
      orderBy: { field: 'timestamp', direction: 'desc' }
    });
  }
}
