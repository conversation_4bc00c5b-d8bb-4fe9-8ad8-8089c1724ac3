// Event Manager - Centralized Notification and Event System
// This is the main entry point for the event management system

import { EventManager } from './EventManager';
import { FirebaseEventHandler } from './handlers/FirebaseEventHandler';
import { NotificationEventHandler } from './handlers/NotificationEventHandler';
import { AnalyticsEventHandler } from './handlers/AnalyticsEventHandler';
import { ValidationMiddleware } from './middleware/ValidationMiddleware';
import { FilterMiddleware } from './middleware/FilterMiddleware';
import {
  EventType,
  EventPriority,
  NotificationType,
  type UserEventData,
  type ProgramEventData,
  type PaymentEventData,
  type NotificationEventData,
  type AchievementEventData,
  type EventManagerConfig
} from './types';

// Singleton instance
let eventManagerInstance: EventManager | null = null;

/**
 * Initialize the event management system with default configuration
 */
export function initializeEventManager(config?: Partial<EventManagerConfig>): EventManager {
  if (eventManagerInstance) {
    return eventManagerInstance;
  }

  eventManagerInstance = EventManager.getInstance();

  // Configure the event manager
  if (config) {
    eventManagerInstance.configure(config);
  }

  // Register middleware (order matters)
  eventManagerInstance.addMiddleware(new ValidationMiddleware());
  eventManagerInstance.addMiddleware(new FilterMiddleware());

  // Register handlers (order matters for priority)
  eventManagerInstance.registerHandler(new FirebaseEventHandler());
  eventManagerInstance.registerHandler(new NotificationEventHandler());
  eventManagerInstance.registerHandler(new AnalyticsEventHandler());

  console.log('Event Manager initialized successfully');
  return eventManagerInstance;
}

/**
 * Get the initialized event manager instance
 */
export function getEventManager(): EventManager {
  if (!eventManagerInstance) {
    return initializeEventManager();
  }
  return eventManagerInstance;
}

// Convenience functions for common event types

/**
 * Emit a user-related event
 */
export async function emitUserEvent(
  type: EventType.USER_SIGNUP | EventType.USER_LOGIN | EventType.USER_LOGOUT | EventType.USER_PROFILE_UPDATE,
  data: Partial<UserEventData>,
  priority: EventPriority = EventPriority.MEDIUM
): Promise<string> {
  const eventManager = getEventManager();

  // Create base event data without undefined values
  const eventData: UserEventData = {
    timestamp: new Date().toISOString(),
    source: data.source || 'UserEvent',
  };

  // Only include defined values
  if (data.userId) eventData.userId = data.userId;
  if (data.userEmail) eventData.userEmail = data.userEmail;
  if (data.firstName) eventData.firstName = data.firstName;
  if (data.lastName) eventData.lastName = data.lastName;
  if (data.deviceInfo) eventData.deviceInfo = data.deviceInfo;
  if (data.metadata) eventData.metadata = data.metadata;

  return eventManager.emit(type, eventData, priority);
}

/**
 * Emit a program-related event
 */
export async function emitProgramEvent(
  type: EventType.PROGRAM_JOIN | EventType.PROGRAM_LEAVE | EventType.PROGRAM_START | EventType.PROGRAM_END | EventType.PROGRAM_SETUP_COMPLETE,
  data: Partial<ProgramEventData>,
  priority: EventPriority = EventPriority.MEDIUM
): Promise<string> {
  const eventManager = getEventManager();

  // Ensure required fields are present
  if (!data.programId || !data.programName) {
    throw new Error('programId and programName are required for program events');
  }

  const eventData: ProgramEventData = {
    timestamp: new Date().toISOString(),
    source: data.source || 'ProgramEvent',
    programId: data.programId,
    programName: data.programName,
  };

  // Only include defined values
  if (data.userId) eventData.userId = data.userId;
  if (data.userEmail) eventData.userEmail = data.userEmail;
  if (data.programCategory) eventData.programCategory = data.programCategory;
  if (data.dayNumber !== undefined) eventData.dayNumber = data.dayNumber;
  if (data.submissionStatus) eventData.submissionStatus = data.submissionStatus;
  if (data.metadata) eventData.metadata = data.metadata;

  return eventManager.emit(type, eventData, priority);
}

/**
 * Emit a payment-related event
 */
export async function emitPaymentEvent(
  type: EventType.PAYMENT_INITIATED | EventType.PAYMENT_SUCCESS | EventType.PAYMENT_FAILED,
  data: Partial<PaymentEventData>,
  priority: EventPriority = EventPriority.HIGH
): Promise<string> {
  const eventManager = getEventManager();

  // Ensure required fields are present
  if (!data.paymentIntentId || data.amount === undefined || !data.currency) {
    throw new Error('paymentIntentId, amount, and currency are required for payment events');
  }

  const eventData: PaymentEventData = {
    timestamp: new Date().toISOString(),
    source: data.source || 'PaymentEvent',
    paymentIntentId: data.paymentIntentId,
    amount: data.amount,
    currency: data.currency,
  };

  // Only include defined values
  if (data.userId) eventData.userId = data.userId;
  if (data.userEmail) eventData.userEmail = data.userEmail;
  if (data.paymentMethod) eventData.paymentMethod = data.paymentMethod;
  if (data.programId) eventData.programId = data.programId;
  if (data.metadata) eventData.metadata = data.metadata;

  return eventManager.emit(type, eventData, priority);
}

/**
 * Emit an achievement-related event
 */
export async function emitAchievementEvent(
  type: EventType.ACHIEVEMENT_UNLOCKED | EventType.STREAK_MILESTONE,
  data: Partial<AchievementEventData>,
  priority: EventPriority = EventPriority.MEDIUM
): Promise<string> {
  const eventManager = getEventManager();
  const eventData: AchievementEventData = {
    timestamp: new Date().toISOString(),
    source: 'AchievementEvent',
    achievementType: '',
    achievementName: '',
    ...data,
  };
  return eventManager.emit(type, eventData, priority);
}

/**
 * Send a custom notification
 */
export async function sendNotification(
  userEmail: string,
  title: string,
  message: string,
  type: NotificationType = NotificationType.SYSTEM,
  priority: EventPriority = EventPriority.MEDIUM,
  metadata?: Record<string, any>
): Promise<string> {
  const eventManager = getEventManager();

  const notificationData: NotificationEventData = {
    userEmail,
    timestamp: new Date().toISOString(),
    source: 'CustomNotification',
    title,
    message,
    type,
    priority,
  };

  // Only include metadata if it exists and has properties
  if (metadata && Object.keys(metadata).length > 0) {
    notificationData.metadata = metadata;
  }

  return eventManager.emit(EventType.NOTIFICATION_SENT, notificationData, priority);
}

// Legacy compatibility function for existing notification system
export async function createNotification(
  userEmail: string,
  notification: {
    title: string;
    message: string;
    type: "account" | "program" | "points" | "reminder";
    priority: "low" | "medium" | "high";
  }
): Promise<string> {
  const typeMapping: Record<string, NotificationType> = {
    account: NotificationType.ACCOUNT,
    program: NotificationType.PROGRAM,
    points: NotificationType.POINTS,
    reminder: NotificationType.REMINDER,
  };

  const priorityMapping: Record<string, EventPriority> = {
    low: EventPriority.LOW,
    medium: EventPriority.MEDIUM,
    high: EventPriority.HIGH,
  };

  return sendNotification(
    userEmail,
    notification.title,
    notification.message,
    typeMapping[notification.type] || NotificationType.SYSTEM,
    priorityMapping[notification.priority] || EventPriority.MEDIUM
  );
}

// Export types for external use
export {
  EventType,
  EventPriority,
  NotificationType,
} from './types';

// Export type aliases to avoid Babel scope tracking issues
export type {
  UserEventData,
  ProgramEventData,
  PaymentEventData,
  NotificationEventData,
  AchievementEventData,
  EventManagerConfig,
} from './types';

// Export the EventManager class for advanced usage
export { EventManager };

// Default export for easy importing
export default {
  initialize: initializeEventManager,
  getManager: getEventManager,
  emitUserEvent,
  emitProgramEvent,
  emitPaymentEvent,
  emitAchievementEvent,
  sendNotification,
  createNotification, // Legacy compatibility
  EventType,
  EventPriority,
  NotificationType,
};
