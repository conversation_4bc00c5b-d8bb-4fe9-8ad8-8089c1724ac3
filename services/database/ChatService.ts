import { BaseService } from './BaseService';
import {
  ChatMessage,
  ServiceResponse,
  QueryOptions,
  COLLECTIONS,
  SUBCOLLECTIONS,
  UnsubscribeFunction,
} from './types';

export class ChatService extends BaseService {
  private getChatPath(programId: string): string {
    return `${COLLECTIONS.PROGRAMS}/${programId}/${SUBCOLLECTIONS.PROGRAM_CHAT}`;
  }

  // Chat message CRUD operations
  async sendMessage(
    programId: string,
    messageData: Omit<ChatMessage, 'id' | 'timestamp'>
  ): Promise<ServiceResponse<string>> {
    const collectionPath = this.getChatPath(programId);
    const fullMessageData: Omit<ChatMessage, 'id'> = {
      ...messageData,
      timestamp: this.getServerTimestamp(),
    };
    return this.createDocument<ChatMessage>(collectionPath, fullMessageData);
  }

  async getMessage(
    programId: string,
    messageId: string
  ): Promise<ServiceResponse<ChatMessage | null>> {
    const collectionPath = this.getChatPath(programId);
    return this.getDocument<ChatMessage>(collectionPath, messageId);
  }

  async updateMessage(
    programId: string,
    messageId: string,
    messageData: Partial<ChatMessage>
  ): Promise<ServiceResponse<void>> {
    const collectionPath = this.getChatPath(programId);
    return this.updateDocument<ChatMessage>(collectionPath, messageId, messageData);
  }

  async deleteMessage(programId: string, messageId: string): Promise<ServiceResponse<void>> {
    const collectionPath = this.getChatPath(programId);
    return this.deleteDocument(collectionPath, messageId);
  }

  // Chat message querying
  async getAllMessages(programId: string): Promise<ServiceResponse<ChatMessage[]>> {
    const collectionPath = this.getChatPath(programId);
    const options: QueryOptions = {
      orderBy: { field: 'timestamp', direction: 'desc' }
    };
    return this.getCollection<ChatMessage>(collectionPath, options);
  }

  async getRecentMessages(
    programId: string,
    limitCount: number = 50
  ): Promise<ServiceResponse<ChatMessage[]>> {
    const collectionPath = this.getChatPath(programId);
    const options: QueryOptions = {
      orderBy: { field: 'timestamp', direction: 'desc' },
      limit: limitCount
    };
    return this.getCollection<ChatMessage>(collectionPath, options);
  }

  async getMessagesByUser(
    programId: string,
    userId: string
  ): Promise<ServiceResponse<ChatMessage[]>> {
    const collectionPath = this.getChatPath(programId);
    const options: QueryOptions = {
      where: [{ field: 'userId', operator: '==', value: userId }],
      orderBy: { field: 'timestamp', direction: 'desc' }
    };
    return this.getCollection<ChatMessage>(collectionPath, options);
  }

  // Real-time subscriptions
  subscribeToMessages(
    programId: string,
    callback: (messages: ChatMessage[]) => void,
    limitCount: number = 50
  ): UnsubscribeFunction {
    const collectionPath = this.getChatPath(programId);
    const options: QueryOptions = {
      orderBy: { field: 'timestamp', direction: 'desc' },
      limit: limitCount
    };
    return this.subscribeToCollection<ChatMessage>(collectionPath, callback, options);
  }

  subscribeToNewMessages(
    programId: string,
    callback: (messages: ChatMessage[]) => void,
    sinceTimestamp?: Date
  ): UnsubscribeFunction {
    const collectionPath = this.getChatPath(programId);
    const options: QueryOptions = {
      orderBy: { field: 'timestamp', direction: 'desc' }
    };

    if (sinceTimestamp) {
      options.where = [
        { field: 'timestamp', operator: '>', value: sinceTimestamp }
      ];
    }

    return this.subscribeToCollection<ChatMessage>(collectionPath, callback, options);
  }

  // Chat statistics
  async getMessageCount(programId: string): Promise<ServiceResponse<number>> {
    const result = await this.getAllMessages(programId);
    if (!result.success) {
      return this.createErrorResponse(result as any);
    }
    return this.createSuccessResponse(result.data?.length || 0);
  }

  async getUserMessageCount(programId: string, userId: string): Promise<ServiceResponse<number>> {
    const result = await this.getMessagesByUser(programId, userId);
    if (!result.success) {
      return this.createErrorResponse(result as any);
    }
    return this.createSuccessResponse(result.data?.length || 0);
  }

  async getChatStats(programId: string): Promise<ServiceResponse<{
    totalMessages: number;
    uniqueUsers: number;
    mostActiveUser: { userId: string; userName: string; messageCount: number } | null;
  }>> {
    try {
      const allResult = await this.getAllMessages(programId);
      if (!allResult.success) {
        return this.createErrorResponse(allResult as any);
      }

      const messages = allResult.data || [];
      const totalMessages = messages.length;
      
      // Count messages per user
      const userMessageCounts: Record<string, { count: number; userName: string }> = {};
      messages.forEach(message => {
        if (!userMessageCounts[message.userId]) {
          userMessageCounts[message.userId] = {
            count: 0,
            userName: message.userName
          };
        }
        userMessageCounts[message.userId].count++;
      });

      const uniqueUsers = Object.keys(userMessageCounts).length;
      
      // Find most active user
      let mostActiveUser: { userId: string; userName: string; messageCount: number } | null = null;
      let maxCount = 0;
      
      Object.entries(userMessageCounts).forEach(([userId, data]) => {
        if (data.count > maxCount) {
          maxCount = data.count;
          mostActiveUser = {
            userId,
            userName: data.userName,
            messageCount: data.count
          };
        }
      });

      const stats = {
        totalMessages,
        uniqueUsers,
        mostActiveUser,
      };

      return this.createSuccessResponse(stats);
    } catch (error) {
      const dbError = this.handleError('read', this.getChatPath(programId), error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  // Message validation
  validateMessageData(messageData: Partial<ChatMessage>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!messageData.text || messageData.text.trim().length === 0) {
      errors.push('Message text cannot be empty');
    }

    if (messageData.text && messageData.text.length > 1000) {
      errors.push('Message text cannot exceed 1000 characters');
    }

    if (!messageData.userId || messageData.userId.trim().length === 0) {
      errors.push('User ID is required');
    }

    if (!messageData.userName || messageData.userName.trim().length === 0) {
      errors.push('User name is required');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // Bulk operations
  async deleteAllMessages(programId: string): Promise<ServiceResponse<void>> {
    try {
      const allResult = await this.getAllMessages(programId);
      if (!allResult.success || !allResult.data) {
        return this.createErrorResponse(allResult as any);
      }

      const operations = allResult.data.map(message => ({
        type: 'delete' as const,
        collection: this.getChatPath(programId),
        documentId: message.id!,
      }));

      if (operations.length === 0) {
        return this.createSuccessResponse(undefined);
      }

      const result = await this.executeBatch(operations);
      if (result.success) {
        return this.createSuccessResponse(undefined);
      } else {
        return this.createErrorResponse({
          operation: 'delete',
          collection: this.getChatPath(programId),
          originalError: new Error(result.error || 'Failed to delete all messages'),
          timestamp: new Date(),
        });
      }
    } catch (error) {
      const dbError = this.handleError('delete', this.getChatPath(programId), error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  async deleteMessagesByUser(programId: string, userId: string): Promise<ServiceResponse<void>> {
    try {
      const userMessagesResult = await this.getMessagesByUser(programId, userId);
      if (!userMessagesResult.success || !userMessagesResult.data) {
        return this.createErrorResponse(userMessagesResult as any);
      }

      const operations = userMessagesResult.data.map(message => ({
        type: 'delete' as const,
        collection: this.getChatPath(programId),
        documentId: message.id!,
      }));

      if (operations.length === 0) {
        return this.createSuccessResponse(undefined);
      }

      const result = await this.executeBatch(operations);
      if (result.success) {
        return this.createSuccessResponse(undefined);
      } else {
        return this.createErrorResponse({
          operation: 'delete',
          collection: this.getChatPath(programId),
          originalError: new Error(result.error || 'Failed to delete user messages'),
          timestamp: new Date(),
        });
      }
    } catch (error) {
      const dbError = this.handleError('delete', this.getChatPath(programId), error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  // Helper methods
  async getLastMessage(programId: string): Promise<ServiceResponse<ChatMessage | null>> {
    const result = await this.getRecentMessages(programId, 1);
    if (!result.success) {
      return this.createErrorResponse(result as any);
    }
    
    const lastMessage = result.data && result.data.length > 0 ? result.data[0] : null;
    return this.createSuccessResponse(lastMessage);
  }

  async isUserActiveInChat(programId: string, userId: string): Promise<ServiceResponse<boolean>> {
    const result = await this.getUserMessageCount(programId, userId);
    if (!result.success) {
      return this.createErrorResponse(result as any);
    }
    
    const isActive = (result.data || 0) > 0;
    return this.createSuccessResponse(isActive);
  }
}
