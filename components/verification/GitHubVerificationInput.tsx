import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Alert,
  StyleSheet,
  TextInput,
  Modal,
  SafeAreaView,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { githubService, GitHubRepository, GitHubUser } from '@/services/githubService';

interface GitHubVerificationInputProps {
  onSubmit: (data: { repository: string; commits: any[] }) => void;
  onValidationChange: (isValid: boolean) => void;
  disabled?: boolean;
}

export const GitHubVerificationInput: React.FC<GitHubVerificationInputProps> = ({
  onSubmit,
  onValidationChange,
  disabled = false,
}) => {
  const { colors } = useTheme();
  const [repositories, setRepositories] = useState<GitHubRepository[]>([]);
  const [filteredRepositories, setFilteredRepositories] = useState<GitHubRepository[]>([]);
  const [selectedRepo, setSelectedRepo] = useState<GitHubRepository | null>(null);
  const [loading, setLoading] = useState(true);
  const [verifying, setVerifying] = useState(false);
  const [userData, setUserData] = useState<GitHubUser | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [repositoryCommits, setRepositoryCommits] = useState<Record<string, number>>({});
  const [loadingCommits, setLoadingCommits] = useState(false);

  useEffect(() => {
    loadGitHubData();
  }, []);

  useEffect(() => {
    onValidationChange(!!selectedRepo);
  }, [selectedRepo, onValidationChange]);

  // Filter repositories based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredRepositories(repositories);
    } else {
      const filtered = repositories.filter(repo =>
        repo.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        repo.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        repo.language?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredRepositories(filtered);
    }
  }, [repositories, searchQuery]);

  const loadGitHubData = async () => {
    try {
      console.log('GitHubVerificationInput: Starting to load GitHub data');
      setLoading(true);
      await githubService.initialize();

      const isAuth = githubService.isAuthenticated();
      console.log('GitHubVerificationInput: Is authenticated?', isAuth);

      if (!isAuth) {
        console.log('GitHubVerificationInput: Not authenticated, showing alert');
        Alert.alert(
          'GitHub Not Connected',
          'Please connect your GitHub account first in the setup step.'
        );
        return;
      }

      console.log('GitHubVerificationInput: Fetching user data and repositories');
      const [user, repos] = await Promise.all([
        githubService.getUserData(),
        githubService.getRepositories(),
      ]);

      console.log('GitHubVerificationInput: User data:', user?.login);
      console.log('GitHubVerificationInput: Repositories count:', repos.length);
      console.log('GitHubVerificationInput: Repository names:', repos.map(r => r.name).slice(0, 5));

      setUserData(user);
      setRepositories(repos);

      // Load today's commits for each repository
      await loadTodaysCommits(repos, user);
    } catch (error) {
      console.error('Failed to load GitHub data:', error);
      Alert.alert(
        'GitHub Error',
        'Failed to load your GitHub repositories. Please try again.'
      );
    } finally {
      setLoading(false);
    }
  };

  const loadTodaysCommits = async (repos: GitHubRepository[], user: GitHubUser | null) => {
    if (!user) return;

    setLoadingCommits(true);
    const commitsMap: Record<string, number> = {};
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    try {
      // Process repositories in batches to avoid rate limiting
      const batchSize = 5;
      for (let i = 0; i < repos.length; i += batchSize) {
        const batch = repos.slice(i, i + batchSize);

        await Promise.all(
          batch.map(async (repo) => {
            try {
              const commits = await githubService.getRecentCommits(
                repo.full_name,
                today,
                user.login
              );
              commitsMap[repo.full_name] = commits.length;
            } catch (error) {
              console.error(`Failed to load commits for ${repo.full_name}:`, error);
              commitsMap[repo.full_name] = 0;
            }
          })
        );

        // Update state after each batch
        setRepositoryCommits(prev => ({ ...prev, ...commitsMap }));

        // Small delay between batches to be respectful to GitHub API
        if (i + batchSize < repos.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }
    } finally {
      setLoadingCommits(false);
    }
  };

  const handleRepositorySelect = (repo: GitHubRepository) => {
    setSelectedRepo(repo);
    setShowModal(false);
  };

  const openRepositorySelector = () => {
    if (repositories.length === 0) {
      Alert.alert(
        'No Repositories',
        'No repositories found in your GitHub account.'
      );
      return;
    }
    setShowModal(true);
  };

  const handleSubmit = async () => {
    if (!selectedRepo) {
      Alert.alert('Selection Required', 'Please select a repository first.');
      return;
    }

    try {
      setVerifying(true);
      
      // Verify commits in the last 24 hours
      const verification = await githubService.verifyCommits(selectedRepo.full_name, 1, 24);
      
      if (verification.verified) {
        onSubmit({
          repository: selectedRepo.full_name,
          commits: verification.commits,
        });
        
        Alert.alert(
          'Verification Successful',
          `Found ${verification.commitCount} commit(s) in the last 24 hours.`
        );
      } else {
        Alert.alert(
          'No Recent Commits',
          'No commits found in the selected repository within the last 24 hours. Please make sure you have committed code recently.'
        );
      }
    } catch (error) {
      console.error('GitHub verification error:', error);
      Alert.alert(
        'Verification Failed',
        'Failed to verify your commits. Please try again.'
      );
    } finally {
      setVerifying(false);
    }
  };

  const renderRepository = ({ item }: { item: GitHubRepository }) => {
    const isSelected = selectedRepo?.id === item.id;
    const todaysCommits = repositoryCommits[item.full_name];
    const hasCommitsToday = todaysCommits !== undefined && todaysCommits > 0;
    const isLoadingCommits = loadingCommits && todaysCommits === undefined;

    return (
      <TouchableOpacity
        style={[
          styles.repositoryItem,
          {
            backgroundColor: isSelected ? colors.primary + '10' : colors.surface,
            borderColor: isSelected ? colors.primary : colors.border,
          },
          isSelected && styles.selectedRepository,
        ]}
        onPress={() => handleRepositorySelect(item)}
        disabled={disabled}
        activeOpacity={0.7}
      >
        <View style={styles.repositoryHeader}>
          <MaterialIcons
            name={item.private ? 'lock' : 'public'}
            size={16}
            color={item.private ? colors.warning : colors.success}
          />
          <Text style={[styles.repositoryName, { color: colors.text }]} numberOfLines={1}>
            {item.name}
          </Text>

          {/* Today's Commits Badge */}
          <View style={styles.repositoryHeaderRight}>
            {isLoadingCommits && (
              <ActivityIndicator size="small" color={colors.textMuted} />
            )}
            {hasCommitsToday && (
              <View style={[
                styles.todaysCommitsBadge,
                { backgroundColor: colors.success + '20', borderColor: colors.success }
              ]}>
                <MaterialIcons name="today" size={12} color={colors.success} />
                <Text style={[styles.todaysCommitsText, { color: colors.success }]}>
                  {todaysCommits}
                </Text>
              </View>
            )}
            {isSelected && (
              <MaterialIcons name="check-circle" size={20} color={colors.primary} style={styles.selectedIcon} />
            )}
          </View>
        </View>

        {item.description && (
          <Text style={[styles.repositoryDescription, { color: colors.textSecondary }]} numberOfLines={2}>
            {item.description}
          </Text>
        )}

        <View style={styles.repositoryFooter}>
          <Text style={[styles.repositoryUpdated, { color: colors.textMuted }]}>
            Updated: {new Date(item.updated_at).toLocaleDateString()}
          </Text>
          <View style={[styles.languageBadge, { backgroundColor: colors.primary + '20' }]}>
            <Text style={[styles.languageText, { color: colors.primary }]}>
              {item.language || 'Unknown'}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    console.log('GitHubVerificationInput: Rendering loading state');
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
          Loading your GitHub repositories...
        </Text>
      </View>
    );
  }

  console.log('GitHubVerificationInput: Rendering main UI with', repositories.length, 'repositories');
  console.log('GitHubVerificationInput: User data exists?', !!userData);
  console.log('GitHubVerificationInput: Colors:', {
    surface: colors.surface,
    text: colors.text,
    textSecondary: colors.textSecondary,
    border: colors.border,
    primary: colors.primary
  });

  return (
    <View style={styles.container}>
      {userData && (
        <View style={[styles.userInfo, { backgroundColor: colors.surface }]}>
          <View style={[styles.userAvatar, { backgroundColor: colors.primary + '20' }]}>
            <MaterialIcons name="account-circle" size={24} color={colors.primary} />
          </View>
          <View style={styles.userDetails}>
            <Text style={[styles.userText, { color: colors.text }]}>
              Connected as
            </Text>
            <Text style={[styles.userName, { color: colors.primary }]}>
              {userData.login}
            </Text>
          </View>
        </View>
      )}

      <Text style={[styles.instructionText, { color: colors.textSecondary }]}>
        Select a repository to verify your recent commits:
      </Text>

      {/* Repository Selection Button */}
      <TouchableOpacity
        style={[
          styles.repositorySelector,
          {
            backgroundColor: colors.surface,
            borderColor: selectedRepo ? colors.primary : colors.border
          }
        ]}
        onPress={openRepositorySelector}
        disabled={disabled || loading}
      >
        <View style={styles.repositorySelectorContent}>
          <MaterialIcons
            name="folder"
            size={20}
            color={selectedRepo ? colors.primary : colors.textMuted}
          />
          <View style={styles.repositorySelectorText}>
            <Text style={[
              styles.repositorySelectorTitle,
              { color: selectedRepo ? colors.text : colors.textMuted }
            ]}>
              {selectedRepo ? selectedRepo.name : 'Choose Repository'}
            </Text>
            {selectedRepo && (
              <Text style={[styles.repositorySelectorSubtitle, { color: colors.textSecondary }]}>
                {selectedRepo.description || 'No description'}
              </Text>
            )}
          </View>
          <MaterialIcons name="chevron-right" size={20} color={colors.textMuted} />
        </View>
      </TouchableOpacity>

      {/* Repository Count */}
      {repositories.length > 0 && (
        <Text style={[styles.repositoryCount, { color: colors.textMuted }]}>
          {repositories.length} repositories available
        </Text>
      )}

      {/* Submit Button */}
      {selectedRepo && (
        <TouchableOpacity
          style={[
            styles.submitButton,
            { backgroundColor: colors.primary },
            (disabled || verifying) && styles.disabledButton,
          ]}
          onPress={handleSubmit}
          disabled={disabled || verifying}
          activeOpacity={0.8}
        >
          {verifying ? (
            <ActivityIndicator size="small" color="#000" />
          ) : (
            <>
              <MaterialIcons name="verified" size={20} color="#000" />
              <Text style={styles.submitButtonText}>Verify Commits</Text>
            </>
          )}
        </TouchableOpacity>
      )}

      {/* Repository Selection Modal */}
      <Modal
        visible={showModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowModal(false)}
      >
        <SafeAreaView style={[styles.modalContainer, { backgroundColor: colors.background }]}>
          {/* Modal Header */}
          <View style={[styles.modalHeader, { borderBottomColor: colors.border }]}>
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setShowModal(false)}
            >
              <MaterialIcons name="close" size={24} color={colors.text} />
            </TouchableOpacity>
            <View style={styles.modalTitleContainer}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                Select Repository
              </Text>
              {!loadingCommits && (
                <Text style={[styles.modalSubtitle, { color: colors.textMuted }]}>
                  {Object.values(repositoryCommits).reduce((sum, count) => sum + count, 0)} commits today
                </Text>
              )}
            </View>
            <View style={styles.modalCloseButton}>
              {loadingCommits && (
                <ActivityIndicator size="small" color={colors.primary} />
              )}
            </View>
          </View>

          {/* Search Bar */}
          <View style={[styles.searchContainer, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <MaterialIcons name="search" size={20} color={colors.textMuted} />
            <TextInput
              style={[styles.searchInput, { color: colors.text }]}
              placeholder="Search repositories..."
              placeholderTextColor={colors.textMuted}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <MaterialIcons name="clear" size={20} color={colors.textMuted} />
              </TouchableOpacity>
            )}
          </View>

          {/* Repository List */}
          <FlatList
            data={filteredRepositories}
            renderItem={renderRepository}
            keyExtractor={(item) => item.id.toString()}
            style={styles.modalRepositoryList}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={filteredRepositories.length === 0 ? styles.emptyListContainer : styles.modalListContent}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <MaterialIcons
                  name={searchQuery ? "search-off" : "folder-open"}
                  size={48}
                  color={colors.textMuted}
                />
                <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
                  {searchQuery ? "No matching repositories" : "No repositories found"}
                </Text>
                <Text style={[styles.emptySubtext, { color: colors.textMuted }]}>
                  {searchQuery
                    ? "Try adjusting your search terms"
                    : "Make sure you have repositories in your GitHub account"
                  }
                </Text>
              </View>
            }
          />
        </SafeAreaView>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    minHeight: 200,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
    fontFamily: 'MontserratRegular',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 235, 59, 0.2)',
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  userDetails: {
    flex: 1,
  },
  userText: {
    fontSize: 12,
    fontFamily: 'MontserratRegular',
    opacity: 0.8,
  },
  userName: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    marginTop: 2,
  },
  instructionText: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    marginBottom: 16,
    textAlign: 'center',
    lineHeight: 20,
  },
  repositorySelector: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  repositorySelectorContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  repositorySelectorText: {
    flex: 1,
    marginLeft: 12,
  },
  repositorySelectorTitle: {
    fontSize: 16,
    fontFamily: 'MontserratSemiBold',
  },
  repositorySelectorSubtitle: {
    fontSize: 12,
    fontFamily: 'MontserratRegular',
    marginTop: 2,
  },
  repositoryCount: {
    fontSize: 12,
    fontFamily: 'MontserratRegular',
    textAlign: 'center',
    marginBottom: 16,
    opacity: 0.8,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 8,
    borderWidth: 1,
    margin: 16,
    marginBottom: 8,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
    fontFamily: 'MontserratRegular',
  },
  // Modal styles
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  modalTitleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    textAlign: 'center',
  },
  modalSubtitle: {
    fontSize: 12,
    fontFamily: 'MontserratRegular',
    textAlign: 'center',
    marginTop: 2,
  },
  modalCloseButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalRepositoryList: {
    flex: 1,
  },
  modalListContent: {
    padding: 16,
  },
  repositoryList: {
    flex: 1,
    marginBottom: 16,
  },
  emptyListContainer: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  repositoryItem: {
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    borderWidth: 1,
    minHeight: 100,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  selectedRepository: {
    borderWidth: 2,
    shadowOpacity: 0.2,
    elevation: 6,
  },
  repositoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  repositoryName: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'MontserratBold',
    marginLeft: 8,
  },
  repositoryHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  todaysCommitsBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderRadius: 10,
    borderWidth: 1,
    gap: 3,
  },
  todaysCommitsText: {
    fontSize: 10,
    fontFamily: 'MontserratBold',
  },
  selectedIcon: {
    marginLeft: 4,
  },
  repositoryDescription: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    marginTop: 4,
    marginBottom: 8,
    lineHeight: 20,
  },
  repositoryFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  repositoryUpdated: {
    fontSize: 12,
    fontFamily: 'MontserratRegular',
    flex: 1,
  },
  languageBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 8,
  },
  languageText: {
    fontSize: 10,
    fontFamily: 'MontserratSemiBold',
    textTransform: 'uppercase',
  },
  emptyContainer: {
    alignItems: 'center',
    padding: 40,
    flex: 1,
    justifyContent: 'center',
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    fontFamily: 'MontserratSemiBold',
    textAlign: 'center',
  },
  emptySubtext: {
    marginTop: 8,
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    textAlign: 'center',
    lineHeight: 20,
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    marginTop: 16,
    shadowColor: '#FFEB3B',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  disabledButton: {
    opacity: 0.6,
    shadowOpacity: 0.1,
  },
  submitButtonText: {
    color: '#000',
    fontSize: 16,
    fontFamily: 'MontserratBold',
    marginLeft: 8,
  },
});
