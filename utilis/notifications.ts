import { firestoreService } from '../services/database';

// Legacy notification function - maintained for backward compatibility
// New code should use the centralized database service directly
export const createNotification = async (
  userEmail: string,
  notification: {
    title: string;
    message: string;
    type: "account" | "program" | "points" | "reminder";
    priority: "low" | "medium" | "high";
  }
) => {
  try {
    const result = await firestoreService.notifications.createNotification(userEmail, notification);
    if (!result.success) {
      throw new Error(result.error || 'Failed to create notification');
    }
  } catch (error) {
    console.error('Error creating notification:', error);
    throw error;
  }
};