import { useState, useEffect, useCallback } from 'react';
import { CustomStakeData, LocationData, PremiumFeatures, ReminderChannel, StrictnessLevel, NotificationTiming } from '@/types/customStake';

const initialFormData: CustomStakeData = {
  commitment: '',
  evidenceType: 'photo',
  reportingFrequency: 'daily',
  timingType: 'mid-night',
  recipient: 'habit-royale',
  amountPerReport: 5,
  referee: 'honor',
  premiumFeatures: {
    motivationalNote: '',
    reminderChannels: ['app'],
    strictnessLevel: 'reasonable',
    notificationTiming: {
      type: 'hours-before',
      hoursBeforeDeadline: 2,
    },
    prioritySupport: false,
    advancedAnalytics: false,
    addToCalendar: false,
  },
};

export const useCustomStakeForm = () => {
  const [formData, setFormData] = useState<CustomStakeData>(initialFormData);

  // Reset amount when no-money is selected and set default when money is at stake
  useEffect(() => {
    if (formData.recipient === 'no-money') {
      setFormData(prev => ({ ...prev, amountPerReport: 0 }));
    } else if (formData.amountPerReport === 0) {
      setFormData(prev => ({ ...prev, amountPerReport: 5 })); // Reset to default
    }
  }, [formData.recipient]);

  // Reset timing when switching to weekly/monthly frequencies
  useEffect(() => {
    if (formData.reportingFrequency === 'weekly' || formData.reportingFrequency === 'monthly') {
      setFormData(prev => ({
        ...prev,
        timingType: 'mid-night', // Reset to default
        beforeTime: undefined,
        afterTime: undefined,
        startTime: undefined,
        endTime: undefined
      }));
    }
  }, [formData.reportingFrequency]);

  const updateFormData = useCallback((updates: Partial<CustomStakeData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  }, []);

  const updateCommitment = useCallback((commitment: string) => {
    setFormData(prev => ({ ...prev, commitment }));
  }, []);

  const updateEvidenceType = useCallback((evidenceType: CustomStakeData['evidenceType']) => {
    setFormData(prev => ({ ...prev, evidenceType }));
  }, []);

  const updateReportingFrequency = useCallback((reportingFrequency: CustomStakeData['reportingFrequency']) => {
    setFormData(prev => ({ ...prev, reportingFrequency }));
  }, []);

  const updateRecipient = useCallback((recipient: CustomStakeData['recipient']) => {
    setFormData(prev => ({ ...prev, recipient }));
  }, []);

  const updateTimingType = useCallback((timingType: CustomStakeData['timingType']) => {
    setFormData(prev => ({ ...prev, timingType }));
  }, []);

  const updateAmount = useCallback((amountPerReport: number) => {
    setFormData(prev => ({ ...prev, amountPerReport }));
  }, []);

  const updateStartDate = useCallback((startDate: Date) => {
    setFormData(prev => ({ ...prev, startDate }));
  }, []);

  const updateEndDate = useCallback((endDate: Date) => {
    setFormData(prev => ({ ...prev, endDate }));
  }, []);

  const updateWeekLength = useCallback((weekLength: number) => {
    setFormData(prev => ({ ...prev, weekLength }));
  }, []);

  const updateTimesPerWeek = useCallback((timesPerWeek: number) => {
    setFormData(prev => ({ ...prev, timesPerWeek }));
  }, []);

  const updateTimesPerMonth = useCallback((timesPerMonth: number) => {
    setFormData(prev => ({ ...prev, timesPerMonth }));
  }, []);

  const updateLocationData = useCallback((locationData: LocationData) => {
    setFormData(prev => ({
      ...prev,
      locationData,
      location: locationData.title // Keep legacy field for backward compatibility
    }));
  }, []);

  const updateAppName = useCallback((appName: string) => {
    setFormData(prev => ({ ...prev, appName }));
  }, []);

  const updateStravaActivity = useCallback((stravaActivity: string) => {
    setFormData(prev => ({ ...prev, stravaActivity }));
  }, []);

  const updateTiming = useCallback((timeType: 'before' | 'after' | 'start' | 'end', time: string) => {
    const updates: Partial<CustomStakeData> = {};
    switch (timeType) {
      case 'before':
        updates.beforeTime = time;
        break;
      case 'after':
        updates.afterTime = time;
        break;
      case 'start':
        updates.startTime = time;
        break;
      case 'end':
        updates.endTime = time;
        break;
    }
    setFormData(prev => ({ ...prev, ...updates }));
  }, []);

  const updatePremiumFeatures = useCallback((updates: Partial<PremiumFeatures>) => {
    setFormData(prev => ({
      ...prev,
      premiumFeatures: {
        ...prev.premiumFeatures!,
        ...updates,
      }
    }));
  }, []);

  const updateMotivationalNote = useCallback((motivationalNote: string) => {
    updatePremiumFeatures({ motivationalNote });
  }, [updatePremiumFeatures]);

  const updateReminderChannels = useCallback((reminderChannels: ReminderChannel[]) => {
    updatePremiumFeatures({ reminderChannels });
  }, [updatePremiumFeatures]);

  const updateStrictnessLevel = useCallback((strictnessLevel: StrictnessLevel) => {
    updatePremiumFeatures({ strictnessLevel });
  }, [updatePremiumFeatures]);

  const updateNotificationTiming = useCallback((notificationTiming: NotificationTiming) => {
    updatePremiumFeatures({ notificationTiming });
  }, [updatePremiumFeatures]);

  const resetForm = useCallback(() => {
    setFormData(initialFormData);
  }, []);

  return {
    formData,
    updateFormData,
    updateCommitment,
    updateEvidenceType,
    updateReportingFrequency,
    updateRecipient,
    updateTimingType,
    updateAmount,
    updateStartDate,
    updateEndDate,
    updateWeekLength,
    updateTimesPerWeek,
    updateTimesPerMonth,
    updateLocationData,
    updateAppName,
    updateStravaActivity,
    updateTiming,
    updatePremiumFeatures,
    updateMotivationalNote,
    updateReminderChannels,
    updateStrictnessLevel,
    updateNotificationTiming,
    resetForm,
  };
};
