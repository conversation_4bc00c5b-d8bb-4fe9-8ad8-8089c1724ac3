import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';

interface WeeklyProgressIndicatorProps {
  submittedCount: number;
  totalRequired: number;
  todaySubmissionExists: boolean;
  canSubmitToday: boolean;
  style?: any;
}

export const WeeklyProgressIndicator: React.FC<WeeklyProgressIndicatorProps> = ({
  submittedCount,
  totalRequired,
  todaySubmissionExists,
  canSubmitToday,
  style
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  const renderProgressDots = () => {
    const dots = [];
    
    for (let i = 0; i < totalRequired; i++) {
      let dotStyle = styles.dotPending;
      let icon = 'radio-button-unchecked';
      let iconColor = colors.textMuted;
      
      if (i < submittedCount) {
        // Completed submissions
        dotStyle = styles.dotCompleted;
        icon = 'check-circle';
        iconColor = colors.success || '#4CAF50';
      } else if (i === submittedCount && canSubmitToday) {
        // Next available submission (can submit today)
        dotStyle = styles.dotNext;
        icon = 'radio-button-unchecked';
        iconColor = colors.primary;
      }
      
      dots.push(
        <View key={i} style={[styles.dotContainer, dotStyle]}>
          <MaterialIcons 
            name={icon} 
            size={16} 
            color={iconColor}
          />
        </View>
      );
    }
    
    return dots;
  };

  const getProgressText = () => {
    const remaining = totalRequired - submittedCount;

    if (submittedCount === totalRequired) {
      return 'Week Complete! 🏆';
    } else {
      // Calculate days remaining in the week (excluding today)
      const today = new Date();
      const dayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
      const daysUntilSunday = dayOfWeek === 0 ? 0 : 7 - dayOfWeek;
      const daysRemaining = daysUntilSunday; // Exclude today

      const submissionText = remaining === 1 ? 'submission' : 'submissions';
      const dayText = daysRemaining === 1 ? 'day' : 'days';

      if (todaySubmissionExists) {
        if (remaining === 1) {
          return `1 submission to go within upcoming ${daysRemaining} ${dayText}! 🎯`;
        } else {
          return `${remaining} ${submissionText} to go within upcoming ${daysRemaining} ${dayText} 💪`;
        }
      } else if (canSubmitToday) {
        if (remaining === 1) {
          return 'Final submission! 🎯';
        } else {
          return `${remaining} ${submissionText} to go within upcoming ${daysRemaining} ${dayText} 💪`;
        }
      } else {
        return `${remaining} ${submissionText} to go within upcoming ${daysRemaining} ${dayText}`;
      }
    }
  };

  return (
    <View style={[styles.container, style]}>
      <View style={styles.dotsContainer}>
        {renderProgressDots()}
      </View>
      <Text style={styles.progressText}>
        {getProgressText()}
      </Text>
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    alignItems: 'center',
    paddingVertical: 8,
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
    gap: 8,
  },
  dotContainer: {
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  dotCompleted: {
    backgroundColor: (colors.success || '#4CAF50') + '20',
  },
  dotNext: {
    backgroundColor: colors.primary + '10',
    borderWidth: 1,
    borderColor: colors.primary,
  },
  dotPending: {
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
  },
  progressText: {
    fontSize: 12,
    fontFamily: 'MontserratMedium',
    color: colors.textSecondary,
    textAlign: 'center',
  },
});
