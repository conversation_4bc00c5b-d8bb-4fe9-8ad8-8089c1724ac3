// Verification system types and interfaces

export type VerificationType = 'camera' | 'gps' | 'camera+gps' | 'text' | 'photo' | 'github';

export interface LocationData {
  latitude: number;
  longitude: number;
}

export interface VerificationConfig {
  type: VerificationType;
  camera?: {
    allowMultiple?: boolean;
    quality?: number;
    allowsEditing?: boolean;
  };
  photo?: {
    allowMultiple?: boolean;
    quality?: number;
    allowsEditing?: boolean;
    allowGallery?: boolean;
    allowCamera?: boolean;
  };
  gps?: {
    accuracy?: 'low' | 'balanced' | 'high' | 'best';
    timeout?: number;
  };
  text?: {
    placeholder?: string;
    validation?: (value: string) => boolean;
    validationMessage?: string;
  };
  github?: {
    scopes?: string[];
    minCommits?: number;
    verificationWindow?: number; // hours
  };
}

export interface VerificationData {
  type: VerificationType;
  images?: string[];
  location?: LocationData;
  textValue?: string;
  githubData?: {
    repository?: string;
    commits?: any[];
    accessToken?: string;
  };
  timestamp: string;
}

export interface VerificationResult {
  success: boolean;
  data?: VerificationData;
  error?: string;
}

// Predefined verification configurations for common use cases
export const VERIFICATION_CONFIGS: Record<string, VerificationConfig> = {
  // Camera only - for affirmations, journaling (single photo)
  CAMERA_ONLY: {
    type: 'camera',
    camera: {
      allowMultiple: false,
      quality: 0.5,
      allowsEditing: true,
    },
  },

  // Camera only with multiple photos - for writing submissions
  CAMERA_MULTIPLE: {
    type: 'camera',
    camera: {
      allowMultiple: true,
      quality: 0.5,
      allowsEditing: true,
    },
  },
  
  // GPS only - for location-based programs
  GPS_ONLY: {
    type: 'gps',
    gps: {
      accuracy: 'high',
      timeout: 15000,
    },
  },
  
  // Camera + GPS - for gym programs
  CAMERA_GPS: {
    type: 'camera+gps',
    camera: {
      allowMultiple: true,
      quality: 0.5,
      allowsEditing: true,
    },
    gps: {
      accuracy: 'high',
      timeout: 15000,
    },
  },
  
  // Text input - for cardio/coding links
  TEXT_INPUT: {
    type: 'text',
    text: {
      placeholder: 'Enter link...',
      validation: (value: string) => value.trim().length > 0,
      validationMessage: 'Please enter a valid link',
    },
  },

  // Photo with gallery and camera - for flexible photo submissions
  PHOTO_ONLY: {
    type: 'photo',
    photo: {
      allowMultiple: false,
      quality: 0.5,
      allowsEditing: true,
      allowGallery: true,
      allowCamera: true,
    },
  },

  // Photo with gallery and camera (multiple) - for flexible photo submissions
  PHOTO_MULTIPLE: {
    type: 'photo',
    photo: {
      allowMultiple: true,
      quality: 0.5,
      allowsEditing: true,
      allowGallery: true,
      allowCamera: true,
    },
  },

  // GitHub OAuth - for coding programs with repository verification
  GITHUB_OAUTH: {
    type: 'github',
    github: {
      scopes: ['repo', 'user:email'],
      minCommits: 1,
      verificationWindow: 24, // hours
    },
  },
};

// Category to verification config mapping
export const CATEGORY_VERIFICATION_MAP: Record<string, VerificationConfig> = {
  'affirmations': VERIFICATION_CONFIGS.CAMERA_ONLY,
  'journals': VERIFICATION_CONFIGS.CAMERA_ONLY,
  'journaling': VERIFICATION_CONFIGS.CAMERA_ONLY,
  'writing': VERIFICATION_CONFIGS.CAMERA_MULTIPLE,
  'gym': VERIFICATION_CONFIGS.CAMERA_GPS,
  'cardio': VERIFICATION_CONFIGS.TEXT_INPUT,
  'coding': VERIFICATION_CONFIGS.GITHUB_OAUTH,
  'photo': VERIFICATION_CONFIGS.PHOTO_ONLY,
};

// Commit evidence type to verification config mapping
export const COMMIT_EVIDENCE_VERIFICATION_MAP: Record<string, VerificationConfig> = {
  'photo': VERIFICATION_CONFIGS.PHOTO_MULTIPLE, // Allow multiple photos for photo evidence
  'video': VERIFICATION_CONFIGS.CAMERA_ONLY, // Video recording as camera
  'video-timelapse': VERIFICATION_CONFIGS.CAMERA_ONLY, // Video timelapse recording
  'camera-only': VERIFICATION_CONFIGS.CAMERA_ONLY, // Camera without saving
  'gps-checkin': VERIFICATION_CONFIGS.GPS_ONLY,
  'gps-avoid': VERIFICATION_CONFIGS.GPS_ONLY,
  'strava': VERIFICATION_CONFIGS.TEXT_INPUT, // Strava link input
  'screen-time': VERIFICATION_CONFIGS.TEXT_INPUT, // Screen time data input
  'honor': VERIFICATION_CONFIGS.CAMERA_ONLY, // Honor system with photo proof
  'github': VERIFICATION_CONFIGS.GITHUB_OAUTH, // GitHub OAuth with repository verification
};

// Helper function to get verification config for a category
export function getVerificationConfig(category: string): VerificationConfig {
  return CATEGORY_VERIFICATION_MAP[category] || VERIFICATION_CONFIGS.CAMERA_ONLY;
}

// Helper function to get verification config for commit evidence type
export function getCommitVerificationConfig(evidenceType: string): VerificationConfig {
  return COMMIT_EVIDENCE_VERIFICATION_MAP[evidenceType] || VERIFICATION_CONFIGS.CAMERA_ONLY;
}

// Helper function to create verification data
export function createVerificationData(
  type: VerificationType,
  options: {
    images?: string[];
    location?: LocationData;
    textValue?: string;
    githubData?: {
      repository?: string;
      commits?: any[];
      accessToken?: string;
    };
  }
): VerificationData {
  return {
    type,
    images: options.images,
    location: options.location,
    textValue: options.textValue,
    githubData: options.githubData,
    timestamp: new Date().toISOString(),
  };
}

// Helper function to serialize verification data for storage
export function serializeVerificationData(data: VerificationData): string {
  return JSON.stringify(data);
}

// Helper function to deserialize verification data from storage
export function deserializeVerificationData(serialized: string): VerificationData | null {
  try {
    return JSON.parse(serialized);
  } catch (error) {
    console.error('Error deserializing verification data:', error);
    return null;
  }
}
