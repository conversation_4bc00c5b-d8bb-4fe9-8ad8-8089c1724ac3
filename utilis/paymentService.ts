import { Platform } from 'react-native';
import { Alert } from 'react-native';
import { STRIPE_CONFIG, API_CONFIG, validateStripeConfig, validateAPIConfig } from '@/config/payments';
import { mockCreatePaymentIntent, mockConfirmPayment, logPaymentEvent } from './mockPaymentAPI';

// Payment service interface
export interface PaymentResult {
  success: boolean;
  paymentIntentId?: string;
  error?: string;
}

export interface PaymentData {
  amount: number; // Amount in cents
  currency: string;
  description: string;
  programId: string;
  userId: string;
}

// Platform-specific Stripe implementations
let stripePromise: any = null;

const initializeStripeWeb = async () => {
  // Web implementation using @stripe/stripe-js
  const { loadStripe } = await import('@stripe/stripe-js');
  if (!stripePromise) {
    stripePromise = loadStripe(STRIPE_CONFIG.PUBLISHABLE_KEY);
  }
  return stripePromise;
};

const initializeStripeMobile = async () => {
  // Mobile implementation using @stripe/stripe-react-native
  const { initStripe } = await import('@stripe/stripe-react-native');
  await initStripe({
    publishableKey: STRIPE_CONFIG.PUBLISHABLE_KEY,
    merchantIdentifier: STRIPE_CONFIG.MERCHANT_IDENTIFIER,
  });
  return true;
};

const initializeStripe = async () => {
  // Validate configuration before initializing
  if (!validateStripeConfig()) {
    throw new Error('Stripe configuration is invalid. Please check your publishable key.');
  }

  if (Platform.OS === 'web') {
    return initializeStripeWeb();
  } else {
    return initializeStripeMobile();
  }
};

// Create payment intent on backend
const createPaymentIntent = async (paymentData: PaymentData): Promise<{ clientSecret: string; paymentIntentId: string }> => {
  logPaymentEvent('Creating payment intent', paymentData);

  // Use mock API in development or when backend is not configured
  if (__DEV__ || !validateAPIConfig()) {
    if (__DEV__) {
      console.warn('⚠️ Using mock payment intent for development');
    }

    try {
      const mockResponse = await mockCreatePaymentIntent(
        paymentData.amount,
        paymentData.currency,
        paymentData.description,
        {
          programId: paymentData.programId,
          userId: paymentData.userId,
        }
      );

      return {
        clientSecret: mockResponse.client_secret,
        paymentIntentId: mockResponse.id,
      };
    } catch (error) {
      logPaymentEvent('Mock payment intent creation failed', error);
      throw error;
    }
  }

  // Production API call
  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.CREATE_PAYMENT_INTENT}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        amount: paymentData.amount,
        currency: paymentData.currency,
        description: paymentData.description,
        metadata: {
          programId: paymentData.programId,
          userId: paymentData.userId,
        },
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    logPaymentEvent('Payment intent created successfully', { paymentIntentId: data.id });

    return {
      clientSecret: data.client_secret,
      paymentIntentId: data.id,
    };
  } catch (error) {
    logPaymentEvent('Payment intent creation failed', error);
    throw new Error(error instanceof Error ? error.message : 'Failed to create payment intent');
  }
};

// Process Stripe payment for mobile
const processStripePaymentMobile = async (paymentData: PaymentData): Promise<PaymentResult> => {
  try {
    logPaymentEvent('Starting mobile Stripe payment', { amount: paymentData.amount });

    await initializeStripeMobile();

    // Create payment intent
    const { clientSecret, paymentIntentId } = await createPaymentIntent(paymentData);

    // In development mode with mock API, simulate payment success
    if (__DEV__ && clientSecret.includes('mock')) {
      logPaymentEvent('Using mock payment confirmation for mobile', { paymentIntentId });

      const mockResult = await mockConfirmPayment(paymentIntentId, 'card');

      if (mockResult.status === 'succeeded') {
        return {
          success: true,
          paymentIntentId: mockResult.id,
        };
      } else {
        return {
          success: false,
          error: 'Mock payment failed for testing',
        };
      }
    }

    // Real Stripe payment processing - only import on mobile
    const { confirmPayment } = await import('@stripe/stripe-react-native');

    // Confirm payment
    const { error, paymentIntent } = await confirmPayment(clientSecret, {
      paymentMethodType: 'Card',
    });

    if (error) {
      logPaymentEvent('Mobile payment failed', error);
      return {
        success: false,
        error: error.message,
      };
    }

    if (paymentIntent?.status === 'Succeeded') {
      logPaymentEvent('Mobile payment succeeded', { paymentIntentId: paymentIntent.id });
      return {
        success: true,
        paymentIntentId: paymentIntent.id,
      };
    }

    return {
      success: false,
      error: 'Payment was not completed',
    };
  } catch (error) {
    logPaymentEvent('Mobile payment error', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
};

// Process Stripe payment for web
const processStripePaymentWeb = async (paymentData: PaymentData): Promise<PaymentResult> => {
  try {
    logPaymentEvent('Starting web Stripe payment', { amount: paymentData.amount });

    const stripe = await initializeStripeWeb();

    if (!stripe) {
      throw new Error('Stripe failed to initialize');
    }

    // Create payment intent
    const { clientSecret, paymentIntentId } = await createPaymentIntent(paymentData);

    // In development mode with mock API, simulate payment success
    if (__DEV__ && clientSecret.includes('mock')) {
      logPaymentEvent('Using mock payment confirmation for web', { paymentIntentId });

      const mockResult = await mockConfirmPayment(paymentIntentId, 'card');

      if (mockResult.status === 'succeeded') {
        return {
          success: true,
          paymentIntentId: mockResult.id,
        };
      } else {
        return {
          success: false,
          error: 'Mock payment failed for testing',
        };
      }
    }

    // For real implementation, you would need to collect card details from a Stripe Elements form
    // This is a simplified version for demonstration
    Alert.alert(
      'Payment Integration Required',
      'Web payment requires Stripe Elements integration. Please implement card collection form.',
      [{ text: 'OK' }]
    );

    return {
      success: false,
      error: 'Web payment integration not fully implemented. Please use mobile app for payments.',
    };
  } catch (error) {
    logPaymentEvent('Web payment error', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
};

// Main payment processing function
export const processStripePayment = async (paymentData: PaymentData): Promise<PaymentResult> => {
  if (Platform.OS === 'web') {
    return processStripePaymentWeb(paymentData);
  } else {
    return processStripePaymentMobile(paymentData);
  }
};

// Google Pay implementation (mobile only)
export const processGooglePayPayment = async (paymentData: PaymentData): Promise<PaymentResult> => {
  if (Platform.OS === 'web') {
    return {
      success: false,
      error: 'Google Pay is not supported on web platform',
    };
  }

  try {
    await initializeStripeMobile();

    // Import Google Pay functions - only on mobile
    const { isGooglePaySupported, initGooglePay, presentGooglePay } = await import('@stripe/stripe-react-native');

    // Check if Google Pay is supported
    const isSupported = await isGooglePaySupported({ testEnv: true });
    if (!isSupported) {
      return {
        success: false,
        error: 'Google Pay is not supported on this device',
      };
    }

    // Initialize Google Pay
    const { error: initError } = await initGooglePay({
      testEnv: STRIPE_CONFIG.TEST_ENV,
      merchantName: 'Habit Royale',
      countryCode: 'US',
    });

    if (initError) {
      return {
        success: false,
        error: initError.message,
      };
    }

    // Create payment intent
    const { clientSecret, paymentIntentId } = await createPaymentIntent(paymentData);

    // Present Google Pay
    const { error } = await presentGooglePay({
      clientSecret,
      forSetupIntent: false,
    });

    if (error) {
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
      paymentIntentId,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
};

// Apple Pay implementation (iOS only)
export const processApplePayPayment = async (paymentData: PaymentData): Promise<PaymentResult> => {
  if (Platform.OS !== 'ios') {
    return {
      success: false,
      error: 'Apple Pay is only supported on iOS devices',
    };
  }

  try {
    await initializeStripeMobile();

    // Import Apple Pay functions - only on mobile
    const { isApplePaySupported, presentApplePay } = await import('@stripe/stripe-react-native');

    // Check if Apple Pay is supported
    const isSupported = await isApplePaySupported();
    if (!isSupported) {
      return {
        success: false,
        error: 'Apple Pay is not supported on this device',
      };
    }

    // Create payment intent
    const { clientSecret, paymentIntentId } = await createPaymentIntent(paymentData);

    // Present Apple Pay
    const { error } = await presentApplePay({
      clientSecret,
      applePay: {
        merchantCountryCode: 'US',
        currencyCode: paymentData.currency.toUpperCase(),
        cartItems: [
          {
            label: paymentData.description,
            amount: (paymentData.amount / 100).toString(),
            paymentType: 'Immediate',
          },
        ],
        merchantDisplayName: 'Habit Royale',
      },
    });

    if (error) {
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
      paymentIntentId,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
};
