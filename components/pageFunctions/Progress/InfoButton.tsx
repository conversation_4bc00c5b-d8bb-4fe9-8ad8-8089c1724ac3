import { MaterialIcons } from "@expo/vector-icons";
import { router } from "expo-router";
import { TouchableOpacity } from "react-native";
import { useTheme } from "@/contexts/ThemeContext";

export function InfoButton (selectedProgramId: any) {
  const { colors } = useTheme();

  return (
    <TouchableOpacity
      style={{
        position: "absolute",
        top: 8,
        right: 8,
      }}
      onPress={() =>
        router.push(`/ProgramDetails?programId=${selectedProgramId}`)
      }
    >
      <MaterialIcons name="info-outline" size={20} color={colors.primary} />
    </TouchableOpacity>
  );
};