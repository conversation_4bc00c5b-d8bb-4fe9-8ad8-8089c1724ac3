import { BaseService } from './BaseService';
import {
  Program,
  ServiceResponse,
  QueryOptions,
  COLLECTIONS,
  UnsubscribeFunction,
  DocumentSubscriptionCallback,
  ProgramStats,
} from './types';

export class ProgramService extends BaseService {
  private readonly COLLECTION_PATH = COLLECTIONS.PROGRAMS;

  // Program CRUD operations
  async createProgram(programData: Omit<Program, 'id'>): Promise<ServiceResponse<string>> {
    return this.createDocument<Program>(this.COLLECTION_PATH, programData);
  }

  async getProgramById(programId: string): Promise<ServiceResponse<Program | null>> {
    try {
      const result = await this.getDocument<Program>(this.COLLECTION_PATH, programId);

      if (!result.success || !result.data) {
        return result;
      }

      // Add registrationsOpen field
      const programWithRegistrationStatus = {
        ...result.data,
        registrationsOpen: result.data.status === 'upcoming'
      };

      return this.createSuccessResponse(programWithRegistrationStatus);
    } catch (error) {
      const dbError = this.handleError('read', this.COLLECTION_PATH, error as Error, programId);
      return this.createErrorResponse(dbError);
    }
  }

  async updateProgram(programId: string, programData: Partial<Program>): Promise<ServiceResponse<void>> {
    return this.updateDocument<Program>(this.COLLECTION_PATH, programId, programData);
  }

  async deleteProgram(programId: string): Promise<ServiceResponse<void>> {
    return this.deleteDocument(this.COLLECTION_PATH, programId);
  }

  // Program querying and filtering
  async getAllPrograms(): Promise<ServiceResponse<Program[]>> {
    try {
      // Get all programs sorted by start date first
      const options: QueryOptions = {
        orderBy: { field: 'startDate', direction: 'asc' }
      };
      const result = await this.getCollection<Program>(this.COLLECTION_PATH, options);

      if (!result.success || !result.data) {
        return result;
      }

      // Add registrationsOpen field and implement custom sorting
      const programsWithRegistrationStatus = result.data.map(program => ({
        ...program,
        registrationsOpen: program.status === 'upcoming'
      }));

      // Sort by registration status first (open first), then by start date
      const sortedPrograms = programsWithRegistrationStatus.sort((a, b) => {
        // First priority: registration status (open first)
        if (a.registrationsOpen !== b.registrationsOpen) {
          return a.registrationsOpen ? -1 : 1; // Open registration comes first
        }

        // Second priority: start date (earliest first)
        const dateA = new Date(a.startDate);
        const dateB = new Date(b.startDate);
        return dateA.getTime() - dateB.getTime();
      });

      return this.createSuccessResponse(sortedPrograms);
    } catch (error) {
      const dbError = this.handleError('read', this.COLLECTION_PATH, error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  async getProgramsByStatus(status: Program['status']): Promise<ServiceResponse<Program[]>> {
    try {
      const options: QueryOptions = {
        where: [{ field: 'status', operator: '==', value: status }],
        orderBy: { field: 'startDate', direction: 'asc' }
      };
      const result = await this.getCollection<Program>(this.COLLECTION_PATH, options);

      if (!result.success || !result.data) {
        return result;
      }

      // Add registrationsOpen field
      const programsWithRegistrationStatus = result.data.map(program => ({
        ...program,
        registrationsOpen: program.status === 'upcoming'
      }));

      return this.createSuccessResponse(programsWithRegistrationStatus);
    } catch (error) {
      const dbError = this.handleError('read', this.COLLECTION_PATH, error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  async getProgramsByCategory(category: string): Promise<ServiceResponse<Program[]>> {
    try {
      const options: QueryOptions = {
        where: [{ field: 'category', operator: '==', value: category }],
        orderBy: { field: 'startDate', direction: 'asc' }
      };
      const result = await this.getCollection<Program>(this.COLLECTION_PATH, options);

      if (!result.success || !result.data) {
        return result;
      }

      // Add registrationsOpen field and sort by registration status first
      const programsWithRegistrationStatus = result.data.map(program => ({
        ...program,
        registrationsOpen: program.status === 'upcoming'
      }));

      // Sort by registration status first, then by start date
      const sortedPrograms = programsWithRegistrationStatus.sort((a, b) => {
        if (a.registrationsOpen !== b.registrationsOpen) {
          return a.registrationsOpen ? -1 : 1;
        }
        const dateA = new Date(a.startDate);
        const dateB = new Date(b.startDate);
        return dateA.getTime() - dateB.getTime();
      });

      return this.createSuccessResponse(sortedPrograms);
    } catch (error) {
      const dbError = this.handleError('read', this.COLLECTION_PATH, error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  async getActivePrograms(): Promise<ServiceResponse<Program[]>> {
    return this.getProgramsByStatus('active');
  }

  async getUpcomingPrograms(): Promise<ServiceResponse<Program[]>> {
    return this.getProgramsByStatus('upcoming');
  }

  async getEndedPrograms(): Promise<ServiceResponse<Program[]>> {
    return this.getProgramsByStatus('ended');
  }

  async searchProgramsByName(searchTerm: string, limitCount: number = 20): Promise<ServiceResponse<Program[]>> {
    const options: QueryOptions = {
      where: [
        { field: 'name', operator: '>=', value: searchTerm },
        { field: 'name', operator: '<=', value: searchTerm + '\uf8ff' }
      ],
      limit: limitCount,
      orderBy: { field: 'name', direction: 'asc' }
    };
    return this.getCollection<Program>(this.COLLECTION_PATH, options);
  }

  // Program participant count management
  async incrementParticipantCount(programId: string, incrementBy: number = 1): Promise<ServiceResponse<void>> {
    try {
      await this.updateDocument(this.COLLECTION_PATH, programId, {
        participantsCount: this.incrementField(incrementBy),
      });
      return this.createSuccessResponse(undefined);
    } catch (error) {
      const dbError = this.handleError('update', this.COLLECTION_PATH, error as Error, programId);
      return this.createErrorResponse(dbError);
    }
  }

  async decrementParticipantCount(programId: string, decrementBy: number = 1): Promise<ServiceResponse<void>> {
    return this.incrementParticipantCount(programId, -decrementBy);
  }

  async updateParticipantCount(programId: string, newCount: number): Promise<ServiceResponse<void>> {
    return this.updateProgram(programId, { participantsCount: newCount });
  }

  // Program status management
  async startProgram(programId: string): Promise<ServiceResponse<void>> {
    return this.updateProgram(programId, { status: 'active' });
  }

  async endProgram(programId: string): Promise<ServiceResponse<void>> {
    return this.updateProgram(programId, { status: 'ended' });
  }

  async updateProgramStatus(programId: string, status: Program['status']): Promise<ServiceResponse<void>> {
    return this.updateProgram(programId, { status });
  }

  // Program statistics
  async getProgramStats(programId: string): Promise<ServiceResponse<ProgramStats>> {
    try {
      // Get program details
      const programResult = await this.getProgramById(programId);
      if (!programResult.success || !programResult.data) {
        return this.createErrorResponse({
          operation: 'read',
          collection: this.COLLECTION_PATH,
          originalError: new Error('Program not found'),
          timestamp: new Date(),
        });
      }

      const program = programResult.data;
      
      // Get participants count from program document
      const totalParticipants = program.participantsCount || 0;
      
      // Calculate basic stats (more detailed stats would require participant data)
      const totalPool = totalParticipants * (program.betAmount || 0);
      
      const stats: ProgramStats = {
        totalParticipants,
        activeParticipants: totalParticipants, // This would need to be calculated from participant data
        disqualifiedParticipants: 0, // This would need to be calculated from participant data
        totalSubmissions: 0, // This would need to be calculated from submission data
        completionRate: 0, // This would need to be calculated from submission data
        totalPool,
        distributedAmount: 0, // This would be calculated based on program completion
      };

      return this.createSuccessResponse(stats);
    } catch (error) {
      const dbError = this.handleError('read', this.COLLECTION_PATH, error as Error, programId);
      return this.createErrorResponse(dbError);
    }
  }

  // Program validation
  validateProgramData(programData: Partial<Program>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (programData.name && programData.name.trim().length < 3) {
      errors.push('Program name must be at least 3 characters');
    }

    if (programData.description && programData.description.trim().length < 10) {
      errors.push('Program description must be at least 10 characters');
    }

    if (programData.betAmount !== undefined && programData.betAmount < 0) {
      errors.push('Bet amount cannot be negative');
    }

    if (programData.defaultLives !== undefined && programData.defaultLives < 0) {
      errors.push('Default lives cannot be negative');
    }

    if (programData.maxLivePurchase !== undefined && programData.maxLivePurchase < 0) {
      errors.push('Max live purchase cannot be negative');
    }

    if (programData.duration !== undefined && programData.duration <= 0) {
      errors.push('Duration must be greater than 0');
    }

    if (programData.startDate && programData.endDate) {
      const startDate = new Date(programData.startDate);
      const endDate = new Date(programData.endDate);
      if (startDate >= endDate) {
        errors.push('End date must be after start date');
      }
    }

    if (programData.status && !['upcoming', 'active', 'ended'].includes(programData.status)) {
      errors.push('Invalid program status');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // Real-time subscriptions
  subscribeToProgram(
    programId: string,
    callback: DocumentSubscriptionCallback<Program>
  ): UnsubscribeFunction {
    return this.subscribeToDocument<Program>(this.COLLECTION_PATH, programId, callback);
  }

  subscribeToAllPrograms(
    callback: (programs: Program[]) => void,
    options?: QueryOptions
  ): UnsubscribeFunction {
    const defaultOptions: QueryOptions = {
      orderBy: { field: 'createdAt', direction: 'desc' }
    };
    return this.subscribeToCollection<Program>(
      this.COLLECTION_PATH, 
      callback, 
      options || defaultOptions
    );
  }

  subscribeToProgramsByStatus(
    status: Program['status'],
    callback: (programs: Program[]) => void
  ): UnsubscribeFunction {
    const options: QueryOptions = {
      where: [{ field: 'status', operator: '==', value: status }],
      orderBy: { field: 'startDate', direction: 'asc' }
    };
    return this.subscribeToCollection<Program>(this.COLLECTION_PATH, callback, options);
  }

  // Batch operations
  async updateMultiplePrograms(
    updates: Array<{ programId: string; data: Partial<Program> }>
  ): Promise<ServiceResponse<void>> {
    try {
      const operations = updates.map(({ programId, data }) => ({
        type: 'update' as const,
        collection: this.COLLECTION_PATH,
        documentId: programId,
        data,
      }));

      const result = await this.executeBatch(operations);
      if (result.success) {
        return this.createSuccessResponse(undefined);
      } else {
        return this.createErrorResponse({
          operation: 'update',
          collection: this.COLLECTION_PATH,
          originalError: new Error(result.error || 'Batch update failed'),
          timestamp: new Date(),
        });
      }
    } catch (error) {
      const dbError = this.handleError('update', this.COLLECTION_PATH, error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  // Helper methods
  async isProgramActive(programId: string): Promise<ServiceResponse<boolean>> {
    const result = await this.getProgramById(programId);
    if (!result.success || !result.data) {
      return this.createErrorResponse(result as any);
    }
    return this.createSuccessResponse(result.data.status === 'active');
  }

  async isProgramEnded(programId: string): Promise<ServiceResponse<boolean>> {
    const result = await this.getProgramById(programId);
    if (!result.success || !result.data) {
      return this.createErrorResponse(result as any);
    }
    return this.createSuccessResponse(result.data.status === 'ended');
  }
}
