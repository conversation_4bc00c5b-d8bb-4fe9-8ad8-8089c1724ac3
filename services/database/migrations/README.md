# Analytics Migration to Subcollections

This migration restructures the analytics data from root-level collections to user subcollections for better organization and data isolation.

## Overview

### Before Migration
```
/analytics/{documentId}
/analytics_daily/{date}
/userLogIns/{documentId}
```

### After Migration
```
/users/{userId}/analytics/{documentId}
/users/{userId}/analytics_daily/{date}
/users/{userId}/logins/{documentId}
```

## Benefits

1. **Better Data Organization**: Analytics data is now organized under each user
2. **Improved Security**: User-specific data is naturally isolated
3. **Easier Querying**: No need to filter by user ID in queries
4. **Scalability**: Better performance for user-specific analytics queries
5. **Data Privacy**: Each user's analytics are contained within their document

## Migration Process

### 1. Dry Run (Recommended First Step)
```bash
npm run migrate:analytics:dry-run
```

This will show you:
- How many records will be migrated
- Number of users in the system
- Estimated migration scope

### 2. Run Migration
```bash
npm run migrate:analytics
```

This will:
- Migrate all analytics records to user subcollections
- Migrate daily analytics (replicated for each user)
- Migrate user login records
- Preserve all original data and timestamps

### 3. Cleanup (Optional)
```bash
npm run migrate:analytics:cleanup
```

This will:
- Remove the old root-level collections
- Only run this after verifying the migration was successful

## What Gets Migrated

### Analytics Records
- **Source**: `/analytics` collection
- **Target**: `/users/{userEmail}/analytics` subcollection
- **Key Changes**: 
  - Removed `userId` and `userEmail` fields (now implicit from parent document)
  - Preserved all metadata and timestamps

### Daily Analytics
- **Source**: `/analytics_daily` collection  
- **Target**: `/users/{userId}/analytics_daily` subcollection
- **Key Changes**:
  - Replicated for each user (since original was global)
  - Preserved all aggregation data

### User Logins
- **Source**: `/userLogIns` collection
- **Target**: `/users/{userEmail}/logins` subcollection
- **Key Changes**:
  - Removed `userEmail` field (now implicit)
  - Preserved all login metadata

## New Analytics Service

The migration includes a new `AnalyticsService` with methods:

### Analytics Records
```typescript
// Create analytics record
await firestoreService.analytics.createAnalyticsRecord(userId, analyticsData);

// Get user analytics
await firestoreService.analytics.getUserAnalytics(userId, options);

// Get analytics stats
await firestoreService.analytics.getAnalyticsStats(userId, startDate, endDate);
```

### Daily Analytics
```typescript
// Create/update daily analytics
await firestoreService.analytics.createDailyAnalytics(userId, date, dailyData);

// Get daily analytics
await firestoreService.analytics.getDailyAnalytics(userId, date);
```

### Login Tracking
```typescript
// Record login
await firestoreService.analytics.recordUserLogin(userId, loginData);

// Get user logins
await firestoreService.analytics.getUserLogins(userId, options);
```

### Subscriptions
```typescript
// Subscribe to user analytics
const unsubscribe = firestoreService.analytics.subscribeToUserAnalytics(
  userId, 
  (analytics) => console.log(analytics)
);
```

## Updated Event Handler

The `AnalyticsEventHandler` has been updated to:
- Use the new subcollection structure
- Group analytics by user before storing
- Handle user-specific daily analytics updates
- Maintain backward compatibility during transition

## Verification Steps

After migration, verify:

1. **Data Integrity**: Check that all records were migrated correctly
2. **User Analytics**: Verify each user has their analytics in subcollections
3. **Application Functionality**: Test that analytics are still being recorded
4. **Performance**: Monitor query performance with new structure

## Rollback Plan

If issues occur:
1. The old collections remain until cleanup is run
2. You can temporarily revert the `AnalyticsEventHandler` changes
3. New analytics will continue to be written to old collections
4. Re-run migration after fixing issues

## Security Rules Update

Update your Firestore security rules to allow access to the new subcollections:

```javascript
// Allow users to read/write their own analytics
match /users/{userId}/analytics/{document} {
  allow read, write: if request.auth != null && request.auth.uid == userId;
}

match /users/{userId}/analytics_daily/{document} {
  allow read, write: if request.auth != null && request.auth.uid == userId;
}

match /users/{userId}/logins/{document} {
  allow read, write: if request.auth != null && request.auth.uid == userId;
}
```

## Monitoring

Monitor the migration with:
- Console logs during migration process
- Error tracking for failed records
- Performance metrics before/after migration
- User analytics functionality testing

## Support

If you encounter issues:
1. Check the migration logs for specific errors
2. Verify Firebase permissions and security rules
3. Run dry-run mode to identify potential issues
4. Contact the development team for assistance
