import { 
  Event, 
  EventType, 
  EventPriority, 
  EventData, 
  EventHandler, 
  EventMiddleware, 
  EventManagerConfig, 
  EventStats,
  BaseEventData 
} from './types';

export class EventManager {
  private static instance: EventManager;
  private handlers: Map<string, EventHandler> = new Map();
  private middleware: EventMiddleware[] = [];
  private eventQueue: Event[] = [];
  private processing = false;
  private stats: EventStats = {
    totalEvents: 0,
    eventsByType: {} as Record<EventType, number>,
    eventsByPriority: {} as Record<EventPriority, number>,
    successRate: 0,
    averageProcessingTime: 0,
    failedEvents: 0,
    retryEvents: 0,
  };

  private config: EventManagerConfig = {
    maxRetries: 3,
    retryDelay: 1000,
    batchSize: 10,
    enableAnalytics: true,
    enableLogging: true,
    firebaseConfig: {
      collection: 'events',
      enablePersistence: true,
      enableRealtime: false,
    },
    notificationConfig: {
      enabled: true,
      types: ['account', 'program', 'points', 'reminder', 'system', 'achievement'],
      priorities: ['low', 'medium', 'high', 'critical'],
      deliveryMethods: ['firebase', 'push'],
    },
  };

  private constructor() {
    this.initializeStats();
  }

  public static getInstance(): EventManager {
    if (!EventManager.instance) {
      EventManager.instance = new EventManager();
    }
    return EventManager.instance;
  }

  public configure(config: Partial<EventManagerConfig>): void {
    this.config = { ...this.config, ...config };
  }

  public registerHandler(handler: EventHandler): void {
    this.handlers.set(handler.name, handler);
    this.log(`Handler registered: ${handler.name}`);
  }

  public unregisterHandler(handlerName: string): void {
    this.handlers.delete(handlerName);
    this.log(`Handler unregistered: ${handlerName}`);
  }

  public addMiddleware(middleware: EventMiddleware): void {
    this.middleware.push(middleware);
    this.middleware.sort((a, b) => a.order - b.order);
    this.log(`Middleware added: ${middleware.name}`);
  }

  public async emit(
    type: EventType, 
    data: EventData, 
    priority: EventPriority = EventPriority.MEDIUM
  ): Promise<string> {
    const event: Event = {
      id: this.generateEventId(),
      type,
      priority,
      data: {
        ...data,
        timestamp: new Date().toISOString(),
        source: data.source || 'unknown',
      },
      createdAt: new Date().toISOString(),
      retryCount: 0,
      maxRetries: this.config.maxRetries,
    };

    this.updateStats(event);
    this.eventQueue.push(event);
    this.log(`Event emitted: ${type} (${event.id})`);

    // Start processing if not already running
    if (!this.processing) {
      this.processQueue();
    }

    return event.id;
  }

  private async processQueue(): Promise<void> {
    if (this.processing || this.eventQueue.length === 0) {
      return;
    }

    this.processing = true;
    this.log('Starting event queue processing');

    try {
      while (this.eventQueue.length > 0) {
        const batch = this.eventQueue.splice(0, this.config.batchSize);
        await Promise.all(batch.map(event => this.processEvent(event)));
      }
    } catch (error) {
      this.log(`Error processing event queue: ${error}`, 'error');
    } finally {
      this.processing = false;
      this.log('Event queue processing completed');
    }
  }

  private async processEvent(event: Event): Promise<void> {
    const startTime = Date.now();
    
    try {
      // Apply middleware
      let processedEvent: Event | null = event;
      for (const middleware of this.middleware) {
        if (processedEvent) {
          processedEvent = await middleware.process(processedEvent);
        }
      }

      if (!processedEvent) {
        this.log(`Event ${event.id} stopped by middleware`);
        return;
      }

      // Get applicable handlers
      const applicableHandlers = Array.from(this.handlers.values())
        .filter(handler => handler.shouldHandle(processedEvent!))
        .sort((a, b) => a.priority - b.priority);

      // Process with handlers
      const handlerPromises = applicableHandlers.map(async (handler) => {
        try {
          await handler.handle(processedEvent!);
          this.log(`Handler ${handler.name} processed event ${event.id}`);
        } catch (error) {
          this.log(`Handler ${handler.name} failed for event ${event.id}: ${error}`, 'error');
          throw error;
        }
      });

      await Promise.all(handlerPromises);
      
      processedEvent.processedAt = new Date().toISOString();
      this.updateProcessingStats(startTime, true);
      this.log(`Event ${event.id} processed successfully`);

    } catch (error) {
      this.updateProcessingStats(startTime, false);
      await this.handleEventError(event, error);
    }
  }

  private async handleEventError(event: Event, error: any): Promise<void> {
    event.retryCount = (event.retryCount || 0) + 1;
    this.stats.failedEvents++;

    this.log(`Event ${event.id} failed (attempt ${event.retryCount}): ${error}`, 'error');

    if (event.retryCount < (event.maxRetries || this.config.maxRetries)) {
      this.stats.retryEvents++;
      // Add back to queue for retry with delay
      setTimeout(() => {
        this.eventQueue.push(event);
        if (!this.processing) {
          this.processQueue();
        }
      }, this.config.retryDelay * event.retryCount);
    } else {
      this.log(`Event ${event.id} exceeded max retries, dropping`, 'error');
    }
  }

  private generateEventId(): string {
    return `evt_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
  }

  private initializeStats(): void {
    // Initialize event type counters
    Object.values(EventType).forEach(type => {
      this.stats.eventsByType[type] = 0;
    });

    // Initialize priority counters
    Object.values(EventPriority).forEach(priority => {
      this.stats.eventsByPriority[priority] = 0;
    });
  }

  private updateStats(event: Event): void {
    this.stats.totalEvents++;
    this.stats.eventsByType[event.type] = (this.stats.eventsByType[event.type] || 0) + 1;
    this.stats.eventsByPriority[event.priority] = (this.stats.eventsByPriority[event.priority] || 0) + 1;
  }

  private updateProcessingStats(startTime: number, success: boolean): void {
    const processingTime = Date.now() - startTime;
    
    // Update average processing time
    const totalProcessed = this.stats.totalEvents - this.eventQueue.length;
    this.stats.averageProcessingTime = 
      (this.stats.averageProcessingTime * (totalProcessed - 1) + processingTime) / totalProcessed;

    // Update success rate
    if (success) {
      const successfulEvents = totalProcessed - this.stats.failedEvents;
      this.stats.successRate = (successfulEvents / totalProcessed) * 100;
    }
  }

  public getStats(): EventStats {
    return { ...this.stats };
  }

  public getQueueSize(): number {
    return this.eventQueue.length;
  }

  public getHandlers(): string[] {
    return Array.from(this.handlers.keys());
  }

  public getMiddleware(): string[] {
    return this.middleware.map(m => m.name);
  }

  private log(message: string, level: 'info' | 'error' = 'info'): void {
    if (this.config.enableLogging) {
      const timestamp = new Date().toISOString();
      const logMessage = `[EventManager] ${timestamp}: ${message}`;
      
      if (level === 'error') {
        console.error(logMessage);
      } else {
        console.log(logMessage);
      }
    }
  }

  // Utility method for creating events with common data
  public createBaseEventData(source: string, userId?: string, userEmail?: string): BaseEventData {
    const baseData: BaseEventData = {
      timestamp: new Date().toISOString(),
      source,
    };

    // Only include userId and userEmail if they have values
    if (userId) {
      baseData.userId = userId;
    }
    if (userEmail) {
      baseData.userEmail = userEmail;
    }

    return baseData;
  }
}
