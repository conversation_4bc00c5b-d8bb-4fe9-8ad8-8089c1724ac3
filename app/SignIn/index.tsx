import { Redirect } from "expo-router";
import React, { useState, memo } from "react";
import {
  TextInput,
  View,
  Text,
  TouchableOpacity,
  Alert,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Modal,
  StyleSheet,
  SafeAreaView,
} from "react-native";
import { getToken, updateId, updateToken } from "../../utilis/variables";
import { auth } from "../../config/firebase";
import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  sendPasswordResetEmail,
  sendEmailVerification,
} from "firebase/auth";
import fetchUserData from "@/components/pageFunctions/SignUp/fetchUserData";
import { firestoreService } from "../../services/database";
import { useAuth } from "@/contexts/AuthContext";
import { useTheme } from "@/contexts/ThemeContext";
import { emitUserEvent, EventType, EventPriority } from "@/utilis/eventManager";
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialCommunityIcons } from '@expo/vector-icons';



const Login = () => {
  const { isAuthenticated } = useAuth();
  const { colors } = useTheme();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [fname, setFname] = useState("");
  const [lname, setLname] = useState("");
  const [dateOfBirth, setDateOfBirth] = useState("");
  const [gender, setGender] = useState("");

  const [SignUpInPwd, setSignUpInPwd] = useState("login");
  const [disLoginBtn, setLoginBtn] = useState(false);
  const [disSignupBtn, setSignupBtn] = useState(false);

  const [modalVisible, setModalVisible] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  // Date picker states
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectedYear, setSelectedYear] = useState(2012); // Set initial year to 2012
  const [selectedMonth, setSelectedMonth] = useState(0);
  const [selectedDay, setSelectedDay] = useState(1);

  // Constants for date picker
  const maxYear = 2012; // Maximum year (users must be at least ~12 years old)
  const years = Array.from(
    { length: 100 }, 
    (_, i) => maxYear - i
  );
  const months = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  // Helper functions
  const formatDate = (year: number, month: number, day: number) => {
    return `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
  };

  const getDaysInMonth = (year: number, month: number) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const handleDateConfirm = () => {
    const formattedDate = formatDate(selectedYear, selectedMonth, selectedDay);
    setDateOfBirth(formattedDate);
    setShowDatePicker(false);
  };



  const handleSignup = async () => {
    if (!validateInputs()) return;

    try {
      setSignupBtn(true);

      const result = await createUserWithEmailAndPassword(auth, email, password);
      const user = result.user;

      await sendEmailVerification(user);

      if (user.email) {
        const result = await firestoreService.users.createUser({
          fname,
          lname,
          email: user.email,
          dateOfBirth,
          gender,
          streak: 0,
        });

        if (result.success) {
          // Create welcome notification
          await firestoreService.notifications.createWelcomeNotification(user.email, fname);
        } else {
          throw new Error(result.error || "Failed to create user document");
        }
      } else {
        throw new Error("User email is null");
      }

      Alert.alert(
        "User Sign Up Successful!",
        "Please verify your email before logging in.",
        [{ text: "OK", onPress: () => showContent("login") }]
      );

      setSignupBtn(false);
    } catch (err) {
      console.error("Error signing up user:", err);
      setSignupBtn(false);
      Alert.alert(
        "Sign Up Failed",
        "An error occurred during sign up. Please try again."
      );
    }
  };

  const handleLogin = async () => {
    try {
      setLoginBtn(true);
  
      const result = await signInWithEmailAndPassword(auth, email, password);
      const user = result.user;
  
      if (!user.emailVerified) {
        Alert.alert(
          "Email not verified",
          "Please verify your email before logging in.",
          [
            {
              text: "Resend Verification Email",
              onPress: handleResendVerificationEmail,
            },
          ]
        );
        setLoginBtn(false);
        return;
      }
  
      if (user.email) {
        const success = await fetchUserData(user.email);
  
        if (success) {
          // Emit user login event - this will handle notifications, analytics, and login tracking automatically
          await emitUserEvent(EventType.USER_LOGIN, {
            userEmail: user.email,
            source: 'LoginScreen',
            deviceInfo: {
              platform: Platform.OS,
              version: '1.0.0',
            },
            metadata: {
              loginMethod: 'email_password',
              loginTime: new Date().toISOString(),
            },
          }, EventPriority.LOW);

          // The AuthContext will handle the authentication state automatically
          updateToken(true);
          updateId(user.email);
        } else {
          throw new Error("User Sign-In failed. User data not fetched.");
        }
      } else {
        throw new Error("User email is null.");
      }
  
      setLoginBtn(false);
    }catch (error: any) {
      setLoginBtn(false);
      
      if (error.code?.includes("auth/invalid-credential")) {
        setErrorMessage("Invalid email or password. Please try again.");
        setModalVisible(true);
      } else if (error.code?.includes("auth/user-not-found")) {
        setErrorMessage("User not found. Please sign up first.");
        setModalVisible(true);
      } else if (error.code?.includes("auth/missing-password")) {
        setErrorMessage("Enter your password first");
        setModalVisible(true);
      } else if (error.code?.includes("auth/too-many-requests")) {
        setErrorMessage(
          "Too many failed attempts. Please try again later or reset your password."
        );
        setModalVisible(true);
      } else {
        setErrorMessage("An error occurred. Please try again.");
        setModalVisible(true);
      }
      
      // console.error("Error signing in:", error.message);
    }
  };

  const handleForgotPwd = async () => {
    try {
      if (!email) {
        Alert.alert("Please enter your email for password reset.");
        return;
      }

      await sendPasswordResetEmail(auth, email);
      setEmail("");
      Alert.alert("Password reset email sent successfully!");
    } catch (error: any) {
      console.error("Error sending password reset email:", error.message);
    }
  };

  const handleResendVerificationEmail = async () => {
    try {
      const user = auth.currentUser;
      if (user) {
        await sendEmailVerification(user);
        Alert.alert("Verification email sent successfully!");
      }
    } catch (error: any) {
      console.error("Error resending verification email:", error.message);
    }
  };

  const showContent = (val: any) => {
    setSignUpInPwd(val);
  };

  const validateInputs = () => {
    // Regular expressions
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{6,}$/;
    const nameRegex = /^[A-Za-z]+$/;

    // Check email
    if (!email || !emailRegex.test(email)) {
      setErrorMessage("Please enter a valid email address.");
      setModalVisible(true);
      return false;
    }

    // Check first name
    if (!fname || !nameRegex.test(fname)) {
      setErrorMessage("First name should contain only letters.");
      setModalVisible(true);
      return false;
    }

    // Check last name
    if (!lname || !nameRegex.test(lname)) {
      setErrorMessage("Last name should contain only letters.");
      setModalVisible(true);
      return false;
    }

    // Check password
    if (!password || !passwordRegex.test(password)) {
      setErrorMessage(
        "Password must be at least 6 characters long, contain one letter, one number, and one special character."
      );
      setModalVisible(true);
      return false;
    }

    // Check date of birth
    if (!dateOfBirth) {
      setErrorMessage("Please select your date of birth.");
      setModalVisible(true);
      return false;
    }

    // Check gender
    if (!gender) {
      setErrorMessage("Please select your gender.");
      setModalVisible(true);
      return false;
    }

    return true;
  };
  

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Royal Background Gradient */}
      <LinearGradient
        colors={[
          colors.background,
          colors.surface,
          colors.background
        ]}
        style={StyleSheet.absoluteFillObject}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />



      <SafeAreaView style={styles.safeArea}>
        <KeyboardAvoidingView
          style={styles.keyboardView}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 0}
        >
          {isAuthenticated && <Redirect href="/(tabs)" />}

          {/* Royal Entrance Header */}
          <View style={styles.headerSection}>
            {/* Royal Crown with Glow */}
            <View style={styles.crownContainer}>
              <LinearGradient
                colors={['#FFD700', '#FFC107', '#FF8F00']}
                style={styles.crownGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <View style={styles.crownGlow}>
                  <MaterialCommunityIcons
                    name="crown"
                    size={56}
                    color="#000"
                  />
                </View>
              </LinearGradient>

              {/* Crown Sparkles */}
              <View style={styles.crownSparkles}>
                <MaterialCommunityIcons name="star-four-points" size={12} color={colors.primary} style={styles.sparkleTopLeft} />
                <MaterialCommunityIcons name="star-four-points" size={8} color={colors.primary} style={styles.sparkleTopRight} />
                <MaterialCommunityIcons name="star-four-points" size={10} color={colors.primary} style={styles.sparkleBottomLeft} />
              </View>
            </View>

            {/* Royal Title with Gradient Text Effect */}
            <LinearGradient
              colors={['#FFD700', '#FFEB3B', '#FFC107']}
              style={styles.titleGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              <Text style={styles.royalTitle}>
                HABIT ROYALE
              </Text>
            </LinearGradient>

            {/* Welcome Message */}
            <Text style={[styles.welcomeText, { color: colors.primary }]}>
              Where Self-Improvement pays you!
            </Text>

            {/* Entrance Subtitle */}
            <Text style={[styles.entranceSubtitle, { color: colors.textMuted }]}>
              Transform Habits into Victory
            </Text>
          </View>

        {/* Form Section */}
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <View style={[styles.formContainer, { backgroundColor: colors.card }]}>
            {SignUpInPwd === "login" && (
              <View style={styles.formContent}>
                <Text style={[styles.formTitle, { color: colors.text }]}>
                  Welcome Back, Champion
                </Text>

                <View style={styles.inputContainer}>
                  <TextInput
                    placeholder="Email"
                    placeholderTextColor={colors.textMuted}
                    style={[styles.input, {
                      color: colors.text,
                      backgroundColor: colors.surface,
                      borderColor: colors.border,
                    }]}
                    onChangeText={setEmail}
                    autoCapitalize="none"
                    keyboardType="email-address"
                  />
                  <TextInput
                    placeholder="Password"
                    placeholderTextColor={colors.textMuted}
                    style={[styles.input, {
                      color: colors.text,
                      backgroundColor: colors.surface,
                      borderColor: colors.border,
                    }]}
                    secureTextEntry={true}
                    onChangeText={setPassword}
                  />
                </View>

                <TouchableOpacity
                  style={[
                    styles.primaryButton,
                    disLoginBtn && styles.primaryButtonDisabled
                  ]}
                  onPress={handleLogin}
                  disabled={disLoginBtn}
                  activeOpacity={0.8}
                >
                  <LinearGradient
                    colors={['#FFD700', '#FFEB3B', '#FFC107']}
                    style={styles.buttonGradient}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                  >
                    <View style={styles.buttonContent}>
                      {disLoginBtn ? (
                        <ActivityIndicator size="small" color="#000000" />
                      ) : (
                        <Text style={styles.primaryButtonText}>
                          Start Your Journey
                        </Text>
                      )}
                    </View>
                  </LinearGradient>
                </TouchableOpacity>
              </View>
            )}

          {SignUpInPwd === "signup" && (
            <View style={styles.formContent}>
              <Text style={[styles.formTitle, { color: colors.text }]}>
                Begin Your Transformation
              </Text>

              <View style={styles.inputContainer}>
                <View style={styles.nameRow}>
                  <TextInput
                    placeholder="First Name"
                    placeholderTextColor={colors.textMuted}
                    style={[styles.input, styles.nameInput, {
                      color: colors.text,
                      backgroundColor: colors.surface,
                      borderColor: colors.border,
                    }]}
                    onChangeText={setFname}
                  />
                  <TextInput
                    placeholder="Last Name"
                    placeholderTextColor={colors.textMuted}
                    style={[styles.input, styles.nameInput, {
                      color: colors.text,
                      backgroundColor: colors.surface,
                      borderColor: colors.border,
                    }]}
                    onChangeText={setLname}
                  />
                </View>

                <TextInput
                  placeholder="Email"
                  placeholderTextColor={colors.textMuted}
                  style={[styles.input, {
                    color: colors.text,
                    backgroundColor: colors.surface,
                    borderColor: colors.border,
                  }]}
                  onChangeText={setEmail}
                  autoCapitalize="none"
                  keyboardType="email-address"
                />
                <TextInput
                  placeholder="Password"
                  placeholderTextColor={colors.textMuted}
                  style={[styles.input, {
                    color: colors.text,
                    backgroundColor: colors.surface,
                    borderColor: colors.border,
                  }]}
                  secureTextEntry={true}
                  onChangeText={setPassword}
                />
                {/* Date of Birth Picker */}
                <TouchableOpacity
                  style={[styles.datePickerButton, {
                    backgroundColor: colors.surface,
                    borderColor: colors.border,
                  }]}
                  onPress={() => setShowDatePicker(true)}
                >
                  <Text style={[styles.datePickerButtonText, {
                    color: dateOfBirth ? colors.text : colors.textMuted
                  }]}>
                    {dateOfBirth || "Select Date of Birth"}
                  </Text>
                </TouchableOpacity>

                {/* Date Picker Modal */}
                <Modal
                  visible={showDatePicker}
                  transparent={true}
                  animationType="fade"
                  onRequestClose={() => setShowDatePicker(false)}
                >
                  <View style={styles.datePickerModal}>
                    <View style={styles.datePickerContainer}>
                      <View style={styles.pickerRow}>
                        {/* Year Picker */}
                        <View style={styles.pickerColumn}>
                          <Text style={styles.pickerLabel}>Year</Text>
                          <ScrollView style={styles.pickerScroll}>
                            {years.map(year => (
                              <TouchableOpacity
                                key={year}
                                style={[
                                  styles.pickerItem,
                                  selectedYear === year && styles.pickerItemSelected
                                ]}
                                onPress={() => setSelectedYear(year)}
                              >
                                <Text 
                                  style={[
                                    styles.pickerItemText,
                                    selectedYear === year && styles.pickerItemTextSelected
                                  ]}
                                >
                                  {year}
                                </Text>
                              </TouchableOpacity>
                            ))}
                          </ScrollView>
                        </View>

                        {/* Month Picker */}
                        <View style={styles.pickerColumn}>
                          <Text style={styles.pickerLabel}>Month</Text>
                          <ScrollView style={styles.pickerScroll}>
                            {months.map((month, index) => (
                              <TouchableOpacity
                                key={month}
                                style={[
                                  styles.pickerItem,
                                  selectedMonth === index && styles.pickerItemSelected
                                ]}
                                onPress={() => setSelectedMonth(index)}
                              >
                                <Text 
                                  style={[
                                    styles.pickerItemText,
                                    selectedMonth === index && styles.pickerItemTextSelected
                                  ]}
                                >
                                  {month}
                                </Text>
                              </TouchableOpacity>
                            ))}
                          </ScrollView>
                        </View>

                        {/* Day Picker */}
                        <View style={styles.pickerColumn}>
                          <Text style={styles.pickerLabel}>Day</Text>
                          <ScrollView style={styles.pickerScroll}>
                            {Array.from(
                              {length: getDaysInMonth(selectedYear, selectedMonth)},
                              (_, i) => i + 1
                            ).map(day => (
                              <TouchableOpacity
                                key={day}
                                style={[
                                  styles.pickerItem,
                                  selectedDay === day && styles.pickerItemSelected
                                ]}
                                onPress={() => setSelectedDay(day)}
                              >
                                <Text 
                                  style={[
                                    styles.pickerItemText,
                                    selectedDay === day && styles.pickerItemTextSelected
                                  ]}
                                >
                                  {day}
                                </Text>
                              </TouchableOpacity>
                            ))}
                          </ScrollView>
                        </View>
                      </View>

                      <View style={styles.modalButtons}>
                        <TouchableOpacity
                          style={[styles.modalButton, styles.cancelButton]}
                          onPress={() => setShowDatePicker(false)}
                        >
                          <Text style={[styles.modalButtonText, styles.cancelButtonText]}>
                            Cancel
                          </Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                          style={[styles.modalButton, styles.confirmButton]}
                          onPress={handleDateConfirm}
                        >
                          <Text style={[styles.modalButtonText, styles.confirmButtonText]}>
                            Confirm
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                </Modal>
                <View style={[styles.genderContainer, { marginTop: 12 }]}>
                  <TouchableOpacity
                    style={[
                      styles.genderButton,
                      {
                        backgroundColor: gender === 'male' ? colors.primary : colors.surface,
                        borderColor: colors.border,
                      }
                    ]}
                    onPress={() => setGender('male')}
                  >
                    <Text style={[
                      styles.genderButtonText,
                      { color: gender === 'male' ? '#000' : colors.text }
                    ]}>Male</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      styles.genderButton,
                      {
                        backgroundColor: gender === 'female' ? colors.primary : colors.surface,
                        borderColor: colors.border,
                      }
                    ]}
                    onPress={() => setGender('female')}
                  >
                    <Text style={[
                      styles.genderButtonText,
                      { color: gender === 'female' ? '#000' : colors.text }
                    ]}>Female</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      styles.genderButton,
                      {
                        backgroundColor: gender === 'other' ? colors.primary : colors.surface,
                        borderColor: colors.border,
                      }
                    ]}
                    onPress={() => setGender('other')}
                  >
                    <Text style={[
                      styles.genderButtonText,
                      { color: gender === 'other' ? '#000' : colors.text }
                    ]}>Other</Text>
                  </TouchableOpacity>
                </View>
              </View>

              <TouchableOpacity
                style={[
                  styles.primaryButton,
                  disSignupBtn && styles.primaryButtonDisabled
                ]}
                onPress={handleSignup}
                disabled={disSignupBtn}
                activeOpacity={0.8}
              >
                <LinearGradient
                  colors={['#FFD700', '#FFEB3B', '#FFC107']}
                  style={styles.buttonGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <View style={styles.buttonContent}>
                    {disSignupBtn ? (
                      <ActivityIndicator size="small" color="#000000" />
                    ) : (
                      <Text style={styles.primaryButtonText}>
                        Create Account
                      </Text>
                    )}
                  </View>
                </LinearGradient>
              </TouchableOpacity>
            </View>
          )}

          {SignUpInPwd === "forgotPwd" && (
            <View style={styles.formContent}>
              <Text style={[styles.formTitle, { color: colors.text }]}>
                Recover Access
              </Text>

              <View style={styles.inputContainer}>
                <TextInput
                  placeholder="Email"
                  placeholderTextColor={colors.textMuted}
                  style={[styles.input, {
                    color: colors.text,
                    backgroundColor: colors.surface,
                    borderColor: colors.border,
                  }]}
                  onChangeText={setEmail}
                  autoCapitalize="none"
                  keyboardType="email-address"
                />
              </View>

              <TouchableOpacity
                style={[styles.primaryButton]}
                onPress={handleForgotPwd}
                activeOpacity={0.8}
              >
                <LinearGradient
                  colors={['#FFD700', '#FFEB3B', '#FFC107']}
                  style={styles.buttonGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <View style={styles.buttonContent}>
                    <Text style={styles.primaryButtonText}>
                      Send Reset Link
                    </Text>
                  </View>
                </LinearGradient>
              </TouchableOpacity>
            </View>
          )}

          {/* Navigation Links */}
          <View style={styles.navigationContainer}>
            <TouchableOpacity
              onPress={() =>
                showContent(SignUpInPwd === "login" ? "signup" : "login")
              }
              style={styles.linkButton}
            >
              <Text style={[styles.linkText, { color: colors.primary }]}>
                {SignUpInPwd === "login"
                  ? "New user? Sign Up"
                  : "Existing user? Sign In"}
              </Text>
            </TouchableOpacity>
            {!(SignUpInPwd === "forgotPwd" || SignUpInPwd === "signup") && (
              <TouchableOpacity
                onPress={() => showContent("forgotPwd")}
                style={styles.linkButton}
              >
                <Text style={[styles.linkText, { color: colors.textMuted }]}>
                  Forgot Password?
                </Text>
              </TouchableOpacity>
            )}
          </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Error Modal */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContainer, { backgroundColor: colors.card }]}>
            <Text style={[styles.modalTitle, { color: colors.error }]}>
              Authentication Error
            </Text>
            <Text style={[styles.modalMessage, { color: colors.text }]}>
              {errorMessage}
            </Text>
            <TouchableOpacity
              style={[styles.modalButton, { backgroundColor: colors.primary }]}
              onPress={() => setModalVisible(false)}
            >
              <Text style={styles.modalButtonText}>OK</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  // Main container styles
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  keyboardView: {
    flex: 1,
  },


  // Royal header styles
  headerSection: {
    alignItems: 'center',
    paddingTop: Platform.OS === 'ios' ? 80 : 60,
    paddingBottom: 50,
    paddingHorizontal: 20,
  },
  crownContainer: {
    marginBottom: 24,
    position: 'relative',
  },
  crownGradient: {
    width: 100,
    height: 100,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#FFD700',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.6,
    shadowRadius: 16,
    elevation: 12,
  },
  crownGlow: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  crownSparkles: {
    position: 'absolute',
    width: 120,
    height: 120,
    top: -10,
    left: -10,
  },
  sparkleTopLeft: {
    position: 'absolute',
    top: 5,
    left: 15,
  },
  sparkleTopRight: {
    position: 'absolute',
    top: 10,
    right: 20,
  },
  sparkleBottomLeft: {
    position: 'absolute',
    bottom: 15,
    left: 10,
  },
  titleGradient: {
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginBottom: 16,
  },
  royalTitle: {
    fontSize: 36,
    fontFamily: 'CasinoFlatShadowItalic',
    textAlign: 'center',
    color: '#000',
    letterSpacing: 2,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 4,
  },
  welcomeText: {
    fontSize: 20,
    fontFamily: 'MontserratBold',
    textAlign: 'center',
    marginBottom: 8,
    letterSpacing: 0.5,
  },
  entranceSubtitle: {
    fontSize: 16,
    fontFamily: 'MontserratMediumItalic',
    textAlign: 'center',
    marginBottom: 8,
  },

  // Royal form styles
  scrollView: {
    flex: 1,
  },
  formContainer: {
    marginHorizontal: 20,
    borderRadius: 20,
    padding: 28,
    shadowColor: '#FFD700',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 215, 0, 0.2)',
  },
  formContent: {
    gap: 24,
  },
  formTitle: {
    fontSize: 28,
    fontFamily: 'CasinoFlatShadowItalic',
    textAlign: 'center',
    marginBottom: 12,
    letterSpacing: 1,
  },

  // Input styles
  inputContainer: {
    gap: 16,
  },
  input: {
    borderWidth: 1,
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 16,
    fontSize: 16,
    fontFamily: 'MontserratRegular',
  },
  nameRow: {
    flexDirection: 'row',
    gap: 12,
  },
  nameInput: {
    flex: 1,
  },

  // Royal button styles
  primaryButton: {
    borderRadius: 14,
    marginTop: 16,
    shadowColor: '#FFD700',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
    overflow: 'hidden',
  },
  primaryButtonDisabled: {
    opacity: 0.6,
    shadowOpacity: 0.1,
    elevation: 2,
  },
  primaryButtonText: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    color: '#000000',
    letterSpacing: 1,
    textAlign: 'center',
  },
  buttonGradient: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 14,
  },
  buttonContent: {
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 24,
  },

  // Gender selection styles
  genderContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  genderButton: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 12,
    paddingVertical: 12,
    alignItems: 'center',
  },
  genderButtonText: {
    fontSize: 14,
    fontFamily: 'MontserratBold',
  },

  // Date picker styles
  datePickerButton: {
    borderWidth: 1,
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 16,
  },
  datePickerButtonText: {
    fontSize: 16,
    fontFamily: 'MontserratRegular',
  },
  datePickerModal: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  datePickerContainer: {
    backgroundColor: '#2A2A2A',
    padding: 20,
    borderRadius: 16,
    width: '90%',
    maxHeight: '80%',
  },
  pickerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  pickerColumn: {
    flex: 1,
    marginHorizontal: 5,
  },
  pickerLabel: {
    color: '#fcfaec',
    marginBottom: 8,
    textAlign: 'center',
    fontFamily: 'MontserratBold',
    fontSize: 14,
  },
  pickerScroll: {
    maxHeight: 150,
    backgroundColor: '#1e1e1e',
    borderRadius: 8,
  },
  pickerItem: {
    padding: 12,
    alignItems: 'center',
  },
  pickerItemText: {
    color: '#fcfaec',
    fontFamily: 'MontserratRegular',
    fontSize: 16,
  },
  pickerItemSelected: {
    backgroundColor: '#FFEB3B',
  },
  pickerItemTextSelected: {
    color: '#000000',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 20,
  },
  confirmButton: {
    backgroundColor: '#FFEB3B',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  cancelButton: {
    backgroundColor: '#666',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  confirmButtonText: {
    color: '#000',
    fontFamily: 'MontserratBold',
    fontSize: 16,
  },
  cancelButtonText: {
    color: '#fcfaec',
    fontFamily: 'MontserratBold',
    fontSize: 16,
  },

  // Navigation styles
  navigationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 24,
    paddingHorizontal: 4,
  },
  linkButton: {
    paddingVertical: 8,
    paddingHorizontal: 4,
  },
  linkText: {
    fontSize: 14,
    fontFamily: 'MontserratBold',
  },

  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    borderRadius: 16,
    padding: 24,
    maxWidth: 400,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 10,
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    textAlign: 'center',
    marginBottom: 12,
  },
  modalMessage: {
    fontSize: 16,
    fontFamily: 'MontserratRegular',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 20,
  },
  modalButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    minWidth: 100,
    alignItems: 'center',
  },
  modalButtonText: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    color: '#000',
  },
});

export default memo(Login);