import { DarkTheme, DefaultTheme, ThemeProvider as NavigationThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { StatusBar } from 'expo-status-bar';
import { useEffect } from 'react';
import { View, Platform, StyleSheet } from 'react-native';
import 'react-native-reanimated';

import { useColorScheme } from '@/hooks/useColorScheme';
import { AuthProvider } from '@/contexts/AuthContext';
import { ProtectedRoute } from '@/components/ProtectedRoute';
import { ThemeProvider, useTheme } from '@/contexts/ThemeContext';
import { initializeEventSystem } from '@/utilis/eventManager/init';

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

// Create a wrapper component to use theme context inside the provider
function AppContent() {
  const { isDark, colors } = useTheme();

  // Create navigation theme based on our theme context
  const navigationTheme = {
    ...(isDark ? DarkTheme : DefaultTheme),
    colors: {
      ...(isDark ? DarkTheme.colors : DefaultTheme.colors),
      background: colors.background,
      card: colors.surface,
      text: colors.text,
      border: colors.border,
      primary: colors.primary,
    },
  };

  return (
    <NavigationThemeProvider value={navigationTheme}>
      {Platform.OS === 'web' ? (
        <View style={[styles.webContainer, { backgroundColor: colors.background }]}>
          <View style={[styles.webContent, { backgroundColor: colors.background }]}>
            <Stack>
              <Stack.Screen name="SignIn/index" options={{ headerShown: false }} />
              <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
              <Stack.Screen name="ProgramDetails" options={{ headerShown: false }} />
              <Stack.Screen name="PaymentConfirmation" options={{ headerShown: false }} />
              <Stack.Screen name="UserProfile" options={{ headerShown: false }} />
              <Stack.Screen name="MyPrograms" options={{ headerShown: false }} />
              <Stack.Screen name="ConfirmDelete" options={{ headerShown: false }} />
              <Stack.Screen name="PushNotifications" options={{ headerShown: false }} />
              <Stack.Screen name="EmailSettings" options={{ headerShown: false }} />
              <Stack.Screen name="Disputes" options={{ headerShown: false }} />
              <Stack.Screen name="Support" options={{ headerShown: false }} />
              <Stack.Screen name="FAQ" options={{ headerShown: false }} />
              <Stack.Screen name="Notifications" options={{ headerShown: false }} />
              <Stack.Screen name="CustomStake" options={{ headerShown: false }} />
              <Stack.Screen name="+not-found" />
            </Stack>
          </View>
        </View>
      ) : (
        <Stack>
          <Stack.Screen name="SignIn/index" options={{ headerShown: false }} />
          <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
          <Stack.Screen name="ProgramDetails" options={{ headerShown: false }} />
          <Stack.Screen name="PaymentConfirmation" options={{ headerShown: false }} />
          <Stack.Screen name="UserProfile" options={{ headerShown: false }} />
          <Stack.Screen name="MyPrograms" options={{ headerShown: false }} />
          <Stack.Screen name="ConfirmDelete" options={{ headerShown: false }} />
          <Stack.Screen name="PushNotifications" options={{ headerShown: false }} />
          <Stack.Screen name="EmailSettings" options={{ headerShown: false }} />
          <Stack.Screen name="Disputes" options={{ headerShown: false }} />
          <Stack.Screen name="Support" options={{ headerShown: false }} />
          <Stack.Screen name="FAQ" options={{ headerShown: false }} />
          <Stack.Screen name="Notifications" options={{ headerShown: false }} />
          <Stack.Screen name="CustomStake" options={{ headerShown: false }} />
          <Stack.Screen name="+not-found" />
        </Stack>
      )}
      <StatusBar style={isDark ? "light" : "dark"} />
    </NavigationThemeProvider>
  );
}

export default function RootLayout() {
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
    MontserratBold: require("../assets/fonts/Montserrat-Bold.ttf"),
    MontserratMediumItalic: require("../assets/fonts/Montserrat-MediumItalic.ttf"),
    MontserratRegular: require("../assets/fonts/Montserrat-Regular.ttf"),
    CasinoFlatShadowItalic: require("../assets/fonts/CasinoFlatShadow-Italic.ttf")
  });

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  // Initialize event management system
  useEffect(() => {
    try {
      initializeEventSystem();
      console.log('Event management system initialized successfully');
    } catch (error) {
      console.error('Failed to initialize event management system:', error);
    }
  }, []);

  if (!loaded) {
    return null;
  }

  return (
    <AuthProvider>
      <ThemeProvider>
        <AppContent />
      </ThemeProvider>
    </AuthProvider>
  );
}

const styles = StyleSheet.create({
  webContainer: {
    flex: 1,
    // backgroundColor will be set dynamically based on theme
  },
  webContent: {
    maxWidth: 480, // Maximum width similar to mobile devices
    width: '100%',
    height: '100%',
    alignSelf: 'center',
    // backgroundColor will be set dynamically based on theme
    overflow: 'hidden',
    // Add subtle shadow to make it look like a device
    ...Platform.select({
      web: {
        boxShadow: '0px 0px 20px rgba(0, 0, 0, 0.15)',
      },
    }),
  },
});

