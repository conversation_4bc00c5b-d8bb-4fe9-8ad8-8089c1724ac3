import { firestoreService } from "../../../services/database";
import {
  updateLname,
  updateToken,
  updateFname,
  updateId } from "../../../utilis/variables"
import { Alert } from "react-native";

const fetchUserData = async (email: string) => {
  const result = await firestoreService.users.getUserById(email);

  if (result.success && result.data) {
    const userData = result.data;
    updateToken(true);
    updateFname(userData.fname);
    updateLname(userData.lname);
    updateId(userData.email);

    return true;
  } else {
    if (result.error) {
      console.error("Error fetching user data from Firestore:", result.error);
    }
    Alert.alert("User document not found in Firestore. Create a new user document?");
    return false;
  }
};

export default fetchUserData;
