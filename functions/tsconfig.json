{
  "compilerOptions": {
    // build settings
    "rootDir": "src",
    "outDir": "lib",
    "sourceMap": true,

    // target & module
    "target": "es2022",
    "module": "commonjs",
    "moduleResolution": "node",

    // only load <PERSON><PERSON>’s typings, not DOM/react-native
    "lib": ["es2022"],
    "types": ["node"],

    // TS “strict” goodness
    "strict": true,
    "noImplicitReturns": true,
    "noUnusedLocals": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,

    // skip errors in .d.ts from your dependencies
    "skipLibCheck": true
  },

  "include": ["src"]
}
