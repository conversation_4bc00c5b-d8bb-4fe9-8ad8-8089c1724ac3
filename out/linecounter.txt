===============================================================================
EXTENSION NAME : linecounter
EXTENSION VERSION : 0.2.7
-------------------------------------------------------------------------------
count time : 2025-04-25 00:36:36
count workspace : c:\Users\<USER>\Desktop\hab.bets
total files : 121
total code lines : 34290
total comment lines : 6619
total blank lines : 792

    statistics
   |      extension|     total code|  total comment|    total blank|percent|
   -------------------------------------------------------------------------
   |               |             29|              1|              9|  0.085|
   |            .ts|            238|             25|             38|   0.69|
   |           .tsx|           8596|            276|            700|     25|
   |          .json|          15039|              0|             24|     44|
   |            .md|              8|              0|              1|  0.023|
   |           .css|             19|              3|              3|  0.055|
   |            .js|           1346|           6314|             17|    3.9|
   |          .html|           9015|              0|              0|     26|
   -------------------------------------------------------------------------
.expo\devices.json, code is 3, comment is 0, blank is 0.
.expo\README.md, code is 8, comment is 0, blank is 0.
.expo\settings.json, code is 3, comment is 0, blank is 0.
.expo\types\router.d.ts, code is 11, comment is 1, blank is 2.
.expo\web\cache\production\images\favicon\favicon-24272cdaeff82cc5facdaccd982a6f05b60c4504704bbf94c19a6388659880bb-contain-transparent\favicon-48.png, it is a binary file.
.gitignore, code is 29, comment is 1, blank is 9.
app.json, code is 55, comment is 0, blank is 1.
app\(tabs)\_layout.tsx, code is 70, comment is 1, blank is 5.
app\(tabs)\index.tsx, code is 324, comment is 2, blank is 23.
app\(tabs)\points.tsx, code is 222, comment is 9, blank is 15.
app\(tabs)\profile.tsx, code is 253, comment is 10, blank is 28.
app\(tabs)\progress.tsx, code is 742, comment is 22, blank is 56.
app\+not-found.tsx, code is 11, comment is 0, blank is 2.
app\_layout.tsx, code is 84, comment is 3, blank is 8.
app\CommonInterface.ts, code is 21, comment is 1, blank is 1.
app\ConfirmDelete.tsx, code is 177, comment is 4, blank is 15.
app\Disputes.tsx, code is 145, comment is 13, blank is 14.
app\EmailSettings.tsx, code is 115, comment is 3, blank is 8.
app\global.css, code is 19, comment is 3, blank is 3.
app\Notifications.tsx, code is 287, comment is 10, blank is 28.
app\ProgramDetails.tsx, code is 448, comment is 5, blank is 31.
app\PushNotifications.tsx, code is 192, comment is 6, blank is 14.
app\SignIn\index.tsx, code is 876, comment is 26, blank is 80.
app\Support.tsx, code is 66, comment is 2, blank is 9.
app\UserProfile.tsx, code is 254, comment is 0, blank is 20.
assets\animations\loading_1.json, code is 1, comment is 0, blank is 0.
assets\animations\loading_2.json, code is 1, comment is 0, blank is 0.
assets\animations\loading_3.json, code is 1, comment is 0, blank is 0.
assets\avatars\0.png, it is a binary file.
assets\avatars\14.png, it is a binary file.
assets\avatars\21.png, it is a binary file.
assets\avatars\3.png, it is a binary file.
assets\avatars\30.png, it is a binary file.
assets\avatars\45.png, it is a binary file.
assets\avatars\7.png, it is a binary file.
assets\avatars\temp\2.png, it is a binary file.
assets\avatars\temp\4.png, it is a binary file.
assets\fonts\Montserrat-Bold.ttf, it is a binary file.
assets\fonts\Montserrat-MediumItalic.ttf, it is a binary file.
assets\fonts\Montserrat-Regular.ttf, it is a binary file.
assets\fonts\SpaceMono-Regular copy.ttf, it is a binary file.
assets\fonts\SpaceMono-Regular.ttf, it is a binary file.
assets\images\adaptive-icon.png, it is a binary file.
assets\images\favicon.png, it is a binary file.
assets\images\icon.png, it is a binary file.
assets\images\logoBig.png, it is a binary file.
assets\images\partial-react-logo.png, it is a binary file.
assets\images\react-logo.png, it is a binary file.
assets\images\<EMAIL>, it is a binary file.
assets\images\<EMAIL>, it is a binary file.
assets\images\splash-icon.png, it is a binary file.
components\HapticTab.tsx, code is 16, comment is 1, blank is 1.
components\Header.tsx, code is 86, comment is 1, blank is 9.
components\pageFunctions\Discover\CategoryFilter.tsx, code is 61, comment is 1, blank is 5.
components\pageFunctions\Discover\FAQSection.tsx, code is 282, comment is 2, blank is 10.
components\pageFunctions\Discover\OctagonStamp.tsx, code is 64, comment is 2, blank is 4.
components\pageFunctions\Points\CharacterUpgrade.tsx, code is 285, comment is 2, blank is 12.
components\pageFunctions\Points\LeaderBoard.tsx, code is 204, comment is 4, blank is 12.
components\pageFunctions\Points\Rewards.tsx, code is 555, comment is 45, blank is 52.
components\pageFunctions\Points\StreakAndPercentile.tsx, code is 91, comment is 0, blank is 5.
components\pageFunctions\Progress\BailoutModal.tsx, code is 101, comment is 3, blank is 6.
components\pageFunctions\Progress\BreathingDot.tsx, code is 43, comment is 1, blank is 7.
components\pageFunctions\Progress\CategoryInput.tsx, code is 162, comment is 12, blank is 26.
components\pageFunctions\Progress\CategorySetup.tsx, code is 210, comment is 4, blank is 11.
components\pageFunctions\Progress\DisqualifiedStats.tsx, code is 99, comment is 0, blank is 13.
components\pageFunctions\Progress\DownloadReport.tsx, code is 231, comment is 8, blank is 26.
components\pageFunctions\Progress\MoneyContainer.tsx, code is 87, comment is 1, blank is 5.
components\pageFunctions\Progress\MoneyOverview.tsx, code is 163, comment is 1, blank is 6.
components\pageFunctions\Progress\ParticipantsStatusDashboard.tsx, code is 358, comment is 10, blank is 21.
components\pageFunctions\Progress\ProgramChat.tsx, code is 216, comment is 0, blank is 12.
components\pageFunctions\Progress\ProgramDropDown.tsx, code is 149, comment is 2, blank is 8.
components\pageFunctions\Progress\ProgramEnd.tsx, code is 86, comment is 0, blank is 6.
components\pageFunctions\Progress\ProgressBar.tsx, code is 146, comment is 19, blank is 11.
components\pageFunctions\Progress\SubmissionStatusBoard.tsx, code is 235, comment is 15, blank is 34.
components\pageFunctions\Progress\WinnerShare.tsx, code is 392, comment is 26, blank is 41.
components\pageFunctions\SignUp\fetchUserData.ts, code is 29, comment is 0, blank is 4.
components\pageFunctions\SignUp\redirector.tsx, code is 8, comment is 0, blank is 1.
config\firebase.ts, code is 33, comment is 6, blank is 6.
constants\Colors.ts, code is 20, comment is 4, blank is 2.
dist\client\_expo\static\js\web\entry-39c2f1d4c321adbbf1d3279c1938d7cd.js, code is 1287, comment is 6304, blank is 2.
dist\client\assets\assets\avatars\0.7fd65815c3236336cb339ba8ce688056.png, it is a binary file.
dist\client\assets\assets\avatars\14.88d975134621b0b8ebd432d0326e7a8c.png, it is a binary file.
dist\client\assets\assets\avatars\21.b643dba58c6f28e0122fe6d27ecdf8fc.png, it is a binary file.
dist\client\assets\assets\avatars\3.7789e3cc38bd30b2ae94fb641d043ac0.png, it is a binary file.
dist\client\assets\assets\avatars\30.d479114bb617a0de87d650ed3e421402.png, it is a binary file.
dist\client\assets\assets\avatars\45.33ec9c9cabe130120a0dd29f923af71e.png, it is a binary file.
dist\client\assets\assets\avatars\7.6824e6ec33a9b7cd05c500b8d24e1e1a.png, it is a binary file.
dist\client\assets\assets\fonts\Montserrat-Bold.354dc625a35bef1b6ec00a79c6cfc0c8.ttf, it is a binary file.
dist\client\assets\assets\fonts\Montserrat-MediumItalic.aaba9a9046de09a1ace6971fd7dd3b8a.ttf, it is a binary file.
dist\client\assets\assets\fonts\Montserrat-Regular.38712903602f88435ddddec98862f8b8.ttf, it is a binary file.
dist\client\assets\assets\fonts\SpaceMono-Regular.49a79d66bdea2debf1832bf4d7aca127.ttf, it is a binary file.
dist\client\assets\assets\images\logoBig.fc5d83bc27a436a5706036f05b43e905.png, it is a binary file.
dist\client\favicon.ico, it is a binary file.
dist\server\(tabs)\index.html, code is 601, comment is 0, blank is 0.
dist\server\(tabs)\points.html, code is 601, comment is 0, blank is 0.
dist\server\(tabs)\profile.html, code is 601, comment is 0, blank is 0.
dist\server\(tabs)\progress.html, code is 601, comment is 0, blank is 0.
dist\server\+not-found.html, code is 601, comment is 0, blank is 0.
dist\server\_expo\routes.json, code is 100, comment is 0, blank is 0.
dist\server\_sitemap.html, code is 601, comment is 0, blank is 0.
dist\server\ConfirmDelete.html, code is 601, comment is 0, blank is 0.
dist\server\Disputes.html, code is 601, comment is 0, blank is 0.
dist\server\EmailSettings.html, code is 601, comment is 0, blank is 0.
dist\server\Notifications.html, code is 601, comment is 0, blank is 0.
dist\server\ProgramDetails.html, code is 601, comment is 0, blank is 0.
dist\server\PushNotifications.html, code is 601, comment is 0, blank is 0.
dist\server\SignIn\index.html, code is 601, comment is 0, blank is 0.
dist\server\Support.html, code is 601, comment is 0, blank is 0.
dist\server\UserProfile.html, code is 601, comment is 0, blank is 0.
eas.json, code is 23, comment is 0, blank is 0.
expo-env.d.ts, code is 0, comment is 2, blank is 1.
hooks\useColorScheme.ts, code is 1, comment is 0, blank is 0.
hooks\useColorScheme.web.ts, code is 13, comment is 3, blank is 5.
hooks\useThemeColor.ts, code is 14, comment is 4, blank is 3.
package-lock.json, code is 14768, comment is 0, blank is 21.
package.json, code is 68, comment is 0, blank is 0.
README.md, code is 0, comment is 0, blank is 1.
scripts\reset-project.js, code is 59, comment is 10, blank is 15.
tsconfig.json, code is 16, comment is 0, blank is 2.
utilis\notifications.ts, code is 23, comment is 0, blank is 1.
utilis\variables.ts, code is 73, comment is 4, blank is 13.
===============================================================================
