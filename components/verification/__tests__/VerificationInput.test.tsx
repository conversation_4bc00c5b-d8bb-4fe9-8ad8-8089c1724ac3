import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { VerificationInput } from '../VerificationInput';
import { VERIFICATION_CONFIGS, VerificationData, VerificationResult, getVerificationConfig } from '../../../types/verification';

// Mock the theme context
jest.mock('@/contexts/ThemeContext', () => ({
  useTheme: () => ({
    colors: {
      primary: '#007AFF',
      background: '#FFFFFF',
      card: '#F2F2F7',
      surface: '#FFFFFF',
      text: '#000000',
      textMuted: '#8E8E93',
      warning: '#FF9500',
    },
  }),
}));

// Mock Firebase Storage
jest.mock('firebase/storage', () => ({
  getStorage: jest.fn(),
  ref: jest.fn(),
  uploadBytes: jest.fn(),
  getDownloadURL: jest.fn(() => Promise.resolve('https://example.com/image.jpg')),
}));

// Mock ImagePicker
jest.mock('expo-image-picker', () => ({
  requestCameraPermissionsAsync: jest.fn(() => Promise.resolve({ granted: true })),
  launchCameraAsync: jest.fn(() => Promise.resolve({
    canceled: false,
    assets: [{ uri: 'file://test-image.jpg' }],
  })),
}));

// Mock Location
jest.mock('expo-location', () => ({
  requestForegroundPermissionsAsync: jest.fn(() => Promise.resolve({ status: 'granted' })),
  getCurrentPositionAsync: jest.fn(() => Promise.resolve({
    coords: { latitude: 37.7749, longitude: -122.4194 },
  })),
}));

// Mock Clipboard
jest.mock('expo-clipboard', () => ({
  getStringAsync: jest.fn(() => Promise.resolve('https://example.com/link')),
}));

describe('VerificationInput', () => {
  const mockOnSubmit = jest.fn<Promise<VerificationResult>, [VerificationData]>();
  const mockOnValidationChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockOnSubmit.mockResolvedValue({ success: true });
  });

  const defaultProps = {
    programId: 'test-program',
    userId: 'test-user',
    day: 1,
    onSubmit: mockOnSubmit,
    onValidationChange: mockOnValidationChange,
  };

  describe('Camera Only Verification', () => {
    it('should render camera capture button for camera-only config', () => {
      const { getByText } = render(
        <VerificationInput
          {...defaultProps}
          config={VERIFICATION_CONFIGS.CAMERA_ONLY}
        />
      );

      expect(getByText('Capture Photo')).toBeTruthy();
    });

    it('should work correctly for writing category', () => {
      const { getByText } = render(
        <VerificationInput
          {...defaultProps}
          config={getVerificationConfig('writing')}
        />
      );

      expect(getByText('Capture Photo')).toBeTruthy();
    });
  });

  describe('Photo Verification', () => {
    it('should render camera and gallery buttons for photo config', () => {
      const { getByText } = render(
        <VerificationInput
          {...defaultProps}
          config={VERIFICATION_CONFIGS.PHOTO_ONLY}
        />
      );

      expect(getByText('Camera')).toBeTruthy();
      expect(getByText('Gallery')).toBeTruthy();
    });

    it('should work correctly for photo category', () => {
      const { getByText } = render(
        <VerificationInput
          {...defaultProps}
          config={getVerificationConfig('photo')}
        />
      );

      expect(getByText('Camera')).toBeTruthy();
      expect(getByText('Gallery')).toBeTruthy();
    });

    it('should handle image capture and submission', async () => {
      const { getByText } = render(
        <VerificationInput
          {...defaultProps}
          config={VERIFICATION_CONFIGS.CAMERA_ONLY}
        />
      );

      // Capture image
      fireEvent.press(getByText('Capture Photo'));

      await waitFor(() => {
        expect(getByText('Retake')).toBeTruthy();
      });

      // Submit
      fireEvent.press(getByText('Submit'));

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith(
          expect.objectContaining({
            type: 'camera',
            images: ['https://example.com/image.jpg'],
          })
        );
      });
    });
  });

  describe('GPS Only Verification', () => {
    it('should render location capture button for GPS-only config', () => {
      const { getByText } = render(
        <VerificationInput
          {...defaultProps}
          config={VERIFICATION_CONFIGS.GPS_ONLY}
        />
      );

      expect(getByText('Get Location')).toBeTruthy();
    });

    it('should handle location capture and submission', async () => {
      const { getByText } = render(
        <VerificationInput
          {...defaultProps}
          config={VERIFICATION_CONFIGS.GPS_ONLY}
        />
      );

      // Capture location
      fireEvent.press(getByText('Get Location'));

      await waitFor(() => {
        expect(getByText('Recapture')).toBeTruthy();
      });

      // Submit
      fireEvent.press(getByText('Submit'));

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith(
          expect.objectContaining({
            type: 'gps',
            location: { latitude: 37.7749, longitude: -122.4194 },
          })
        );
      });
    });
  });

  describe('Camera + GPS Verification', () => {
    it('should render step indicator for camera+GPS config', () => {
      const { getByText } = render(
        <VerificationInput
          {...defaultProps}
          config={VERIFICATION_CONFIGS.CAMERA_GPS}
        />
      );

      expect(getByText('Photo')).toBeTruthy();
      expect(getByText('Location')).toBeTruthy();
      expect(getByText('Review')).toBeTruthy();
    });

    it('should progress through steps correctly', async () => {
      const { getByText } = render(
        <VerificationInput
          {...defaultProps}
          config={VERIFICATION_CONFIGS.CAMERA_GPS}
        />
      );

      // Step 1: Capture photo
      fireEvent.press(getByText('Take First Photo'));

      await waitFor(() => {
        expect(getByText('Next')).toBeTruthy();
      });

      fireEvent.press(getByText('Next'));

      // Step 2: Capture location
      await waitFor(() => {
        expect(getByText('Get Location')).toBeTruthy();
      });

      fireEvent.press(getByText('Get Location'));

      await waitFor(() => {
        expect(getByText('Review')).toBeTruthy();
      });

      fireEvent.press(getByText('Review'));

      // Step 3: Review and submit
      await waitFor(() => {
        expect(getByText('✅ Review & Submit')).toBeTruthy();
      });
    });
  });

  describe('Text Input Verification', () => {
    it('should render text input for text config', () => {
      const { getByPlaceholderText } = render(
        <VerificationInput
          {...defaultProps}
          config={VERIFICATION_CONFIGS.TEXT_INPUT}
        />
      );

      expect(getByPlaceholderText('Enter link...')).toBeTruthy();
    });

    it('should handle text input and submission', async () => {
      const { getByPlaceholderText, getByText } = render(
        <VerificationInput
          {...defaultProps}
          config={VERIFICATION_CONFIGS.TEXT_INPUT}
        />
      );

      // Enter text
      const textInput = getByPlaceholderText('Enter link...');
      fireEvent.changeText(textInput, 'https://example.com/test');

      // Submit
      fireEvent.press(getByText('Submit'));

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith(
          expect.objectContaining({
            type: 'text',
            textValue: 'https://example.com/test',
          })
        );
      });
    });
  });

  describe('Validation', () => {
    it('should call onValidationChange when validation state changes', async () => {
      const { getByPlaceholderText } = render(
        <VerificationInput
          {...defaultProps}
          config={VERIFICATION_CONFIGS.TEXT_INPUT}
        />
      );

      // Initially invalid
      expect(mockOnValidationChange).toHaveBeenCalledWith(false);

      // Enter text to make it valid
      const textInput = getByPlaceholderText('Enter link...');
      fireEvent.changeText(textInput, 'https://example.com/test');

      await waitFor(() => {
        expect(mockOnValidationChange).toHaveBeenCalledWith(true);
      });
    });

    it('should disable submit button when validation fails', () => {
      const { getByText } = render(
        <VerificationInput
          {...defaultProps}
          config={VERIFICATION_CONFIGS.TEXT_INPUT}
        />
      );

      const submitButton = getByText('Submit');
      expect(submitButton.props.accessibilityState?.disabled).toBe(true);
    });
  });

  describe('Initial Data', () => {
    it('should populate with initial verification data', () => {
      const initialData: VerificationData = {
        type: 'text',
        textValue: 'https://initial-link.com',
        timestamp: new Date().toISOString(),
      };

      const { getByDisplayValue } = render(
        <VerificationInput
          {...defaultProps}
          config={VERIFICATION_CONFIGS.TEXT_INPUT}
          initialData={initialData}
        />
      );

      expect(getByDisplayValue('https://initial-link.com')).toBeTruthy();
    });
  });
});
