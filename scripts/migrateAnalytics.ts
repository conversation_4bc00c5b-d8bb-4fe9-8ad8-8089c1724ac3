#!/usr/bin/env tsx

/**
 * Analytics Migration Script
 * 
 * This script migrates analytics, analytics_daily, and userLogIns collections
 * from the root level to subcollections under user documents.
 * 
 * Usage:
 *   npm run migrate:analytics [--dry-run] [--cleanup]
 * 
 * Options:
 *   --dry-run    Preview the migration without making changes
 *   --cleanup    Remove old collections after successful migration
 */

import { analyticsMigration } from '../services/database/migrations/migrateAnalyticsToSubcollections';

async function main() {
  const args = process.argv.slice(2);
  const isDryRun = args.includes('--dry-run');
  const shouldCleanup = args.includes('--cleanup');

  console.log('🚀 Analytics Migration Script');
  console.log('==============================');
  
  if (isDryRun) {
    console.log('🔍 Running in DRY RUN mode - no changes will be made');
    console.log('');
    
    try {
      await analyticsMigration.dryRun();
      console.log('');
      console.log('✅ Dry run completed successfully!');
      console.log('💡 Run without --dry-run to perform the actual migration');
    } catch (error) {
      console.error('❌ Dry run failed:', error);
      process.exit(1);
    }
    
    return;
  }

  console.log('⚠️  This will migrate analytics data to user subcollections');
  console.log('📊 Collections to migrate: analytics, analytics_daily, userLogIns');
  console.log('');

  // Confirm before proceeding
  if (process.env.NODE_ENV === 'production') {
    console.log('🚨 PRODUCTION ENVIRONMENT DETECTED');
    console.log('⚠️  Please ensure you have a backup before proceeding');
    console.log('');
    
    // In production, require explicit confirmation
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const answer = await new Promise<string>((resolve) => {
      rl.question('Are you sure you want to proceed? (yes/no): ', resolve);
    });
    
    rl.close();

    if (answer.toLowerCase() !== 'yes') {
      console.log('❌ Migration cancelled');
      process.exit(0);
    }
  }

  try {
    console.log('🔄 Starting migration...');
    console.log('');
    
    const startTime = Date.now();
    
    // Run the migration
    await analyticsMigration.migrateAllAnalytics();
    
    const duration = Date.now() - startTime;
    console.log('');
    console.log(`✅ Migration completed successfully in ${duration}ms`);
    
    if (shouldCleanup) {
      console.log('');
      console.log('🧹 Cleaning up old collections...');
      
      await analyticsMigration.cleanupOldCollections();
      
      console.log('✅ Cleanup completed successfully');
    } else {
      console.log('');
      console.log('💡 Old collections are still present');
      console.log('💡 Run with --cleanup flag to remove them after verifying the migration');
    }
    
    console.log('');
    console.log('🎉 Analytics migration completed successfully!');
    console.log('');
    console.log('📋 Next steps:');
    console.log('  1. Verify the migrated data in the new subcollections');
    console.log('  2. Test your application to ensure analytics are working');
    console.log('  3. Run with --cleanup flag to remove old collections (if not done already)');
    
  } catch (error) {
    console.error('');
    console.error('❌ Migration failed:', error);
    console.error('');
    console.error('🔧 Troubleshooting:');
    console.error('  1. Check your Firebase permissions');
    console.error('  2. Ensure your database rules allow the migration');
    console.error('  3. Check the error details above');
    console.error('  4. Consider running --dry-run first to identify issues');
    
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// Run the script
main().catch((error) => {
  console.error('Script failed:', error);
  process.exit(1);
});
