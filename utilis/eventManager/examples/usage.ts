// Event Manager Usage Examples
// This file demonstrates how to use the new centralized event system

import { 
  initializeEventManager,
  emitUserEvent,
  emitProgramEvent,
  emitPaymentEvent,
  emitAchievementEvent,
  sendNotification,
  EventType,
  EventPriority,
  NotificationType 
} from '../index';

// Initialize the event system (typically done once in your app startup)
export function setupEventSystem() {
  const eventManager = initializeEventManager({
    maxRetries: 3,
    retryDelay: 1000,
    batchSize: 10,
    enableAnalytics: true,
    enableLogging: true,
    firebaseConfig: {
      collection: 'events',
      enablePersistence: true,
      enableRealtime: false,
    },
    notificationConfig: {
      enabled: true,
      types: ['account', 'program', 'points', 'reminder', 'system', 'achievement'],
      priorities: ['low', 'medium', 'high', 'critical'],
      deliveryMethods: ['firebase', 'push'],
    },
  });

  console.log('Event system initialized');
  return eventManager;
}

// Example: User signup event
export async function handleUserSignup(userEmail: string, firstName: string, lastName: string) {
  try {
    await emitUserEvent(EventType.USER_SIGNUP, {
      userEmail,
      firstName,
      lastName,
      source: 'SignUpScreen',
      deviceInfo: {
        platform: 'mobile',
        version: '1.0.0',
      },
    }, EventPriority.HIGH);
    
    console.log('User signup event emitted successfully');
  } catch (error) {
    console.error('Failed to emit user signup event:', error);
  }
}

// Example: User login event
export async function handleUserLogin(userEmail: string, platform: string) {
  try {
    await emitUserEvent(EventType.USER_LOGIN, {
      userEmail,
      source: 'LoginScreen',
      deviceInfo: {
        platform,
        version: '1.0.0',
      },
    }, EventPriority.LOW);
    
    console.log('User login event emitted successfully');
  } catch (error) {
    console.error('Failed to emit user login event:', error);
  }
}

// Example: Program join event
export async function handleProgramJoin(
  userEmail: string, 
  programId: string, 
  programName: string, 
  programCategory: string
) {
  try {
    await emitProgramEvent(EventType.PROGRAM_JOIN, {
      userEmail,
      programId,
      programName,
      programCategory,
      source: 'ProgramDetailsScreen',
    }, EventPriority.MEDIUM);
    
    console.log('Program join event emitted successfully');
  } catch (error) {
    console.error('Failed to emit program join event:', error);
  }
}

// Example: Payment success event
export async function handlePaymentSuccess(
  userEmail: string,
  paymentIntentId: string,
  amount: number,
  currency: string,
  programId?: string
) {
  try {
    await emitPaymentEvent(EventType.PAYMENT_SUCCESS, {
      userEmail,
      paymentIntentId,
      amount,
      currency,
      programId,
      source: 'PaymentScreen',
    }, EventPriority.HIGH);
    
    console.log('Payment success event emitted successfully');
  } catch (error) {
    console.error('Failed to emit payment success event:', error);
  }
}

// Example: Achievement unlocked event
export async function handleAchievementUnlocked(
  userEmail: string,
  achievementName: string,
  achievementType: string,
  points?: number
) {
  try {
    await emitAchievementEvent(EventType.ACHIEVEMENT_UNLOCKED, {
      userEmail,
      achievementName,
      achievementType,
      points,
      source: 'AchievementSystem',
    }, EventPriority.MEDIUM);
    
    console.log('Achievement unlocked event emitted successfully');
  } catch (error) {
    console.error('Failed to emit achievement event:', error);
  }
}

// Example: Custom notification
export async function sendCustomNotification(
  userEmail: string,
  title: string,
  message: string,
  type: NotificationType = NotificationType.SYSTEM,
  priority: EventPriority = EventPriority.MEDIUM
) {
  try {
    await sendNotification(userEmail, title, message, type, priority, {
      source: 'CustomNotification',
      timestamp: new Date().toISOString(),
    });
    
    console.log('Custom notification sent successfully');
  } catch (error) {
    console.error('Failed to send custom notification:', error);
  }
}

// Example: Batch operations
export async function handleBatchUserActions(users: Array<{email: string, firstName: string}>) {
  const promises = users.map(user => 
    emitUserEvent(EventType.USER_SIGNUP, {
      userEmail: user.email,
      firstName: user.firstName,
      source: 'BatchImport',
    }, EventPriority.LOW)
  );
  
  try {
    await Promise.all(promises);
    console.log(`Batch processed ${users.length} user events`);
  } catch (error) {
    console.error('Failed to process batch user events:', error);
  }
}

// Example: Error handling and retry
export async function handleCriticalSystemEvent(errorMessage: string, affectedUsers: string[]) {
  try {
    // Emit system error event
    const eventManager = initializeEventManager();
    await eventManager.emit(EventType.SYSTEM_ERROR, {
      timestamp: new Date().toISOString(),
      source: 'SystemMonitor',
      errorMessage,
      affectedUsers,
    }, EventPriority.CRITICAL);
    
    // Send notifications to affected users
    const notificationPromises = affectedUsers.map(userEmail =>
      sendNotification(
        userEmail,
        'System Issue Detected',
        'We are aware of a system issue and are working to resolve it. We apologize for any inconvenience.',
        NotificationType.SYSTEM,
        EventPriority.HIGH
      )
    );
    
    await Promise.all(notificationPromises);
    console.log('Critical system event handled successfully');
  } catch (error) {
    console.error('Failed to handle critical system event:', error);
  }
}

// Example: Migration from old notification system
export async function migrateFromOldNotificationSystem(
  userEmail: string,
  oldNotification: {
    title: string;
    message: string;
    type: "account" | "program" | "points" | "reminder";
    priority: "low" | "medium" | "high";
  }
) {
  // Map old types to new types
  const typeMapping: Record<string, NotificationType> = {
    account: NotificationType.ACCOUNT,
    program: NotificationType.PROGRAM,
    points: NotificationType.POINTS,
    reminder: NotificationType.REMINDER,
  };

  const priorityMapping: Record<string, EventPriority> = {
    low: EventPriority.LOW,
    medium: EventPriority.MEDIUM,
    high: EventPriority.HIGH,
  };

  try {
    await sendNotification(
      userEmail,
      oldNotification.title,
      oldNotification.message,
      typeMapping[oldNotification.type] || NotificationType.SYSTEM,
      priorityMapping[oldNotification.priority] || EventPriority.MEDIUM
    );
    
    console.log('Successfully migrated old notification to new system');
  } catch (error) {
    console.error('Failed to migrate old notification:', error);
    throw error;
  }
}
