import { BaseService } from './BaseService';
import {
  UserAnalytics,
  UserAnalyticsDaily,
  UserLogin,
  ServiceResponse,
  QueryOptions,
  COLLECTIONS,
  SUBCOLLECTIONS,
  UnsubscribeFunction,
  DocumentSubscriptionCallback,
} from './types';

export class AnalyticsService extends BaseService {
  private readonly COLLECTION_PATH = COLLECTIONS.USERS;

  // Helper methods to get collection paths
  private getUserAnalyticsPath(userId: string): string {
    return `${this.COLLECTION_PATH}/${userId}/${SUBCOLLECTIONS.USER_ANALYTICS}`;
  }

  private getUserAnalyticsDailyPath(userId: string): string {
    return `${this.COLLECTION_PATH}/${userId}/${SUBCOLLECTIONS.USER_ANALYTICS_DAILY}`;
  }

  private getUserLoginsPath(userId: string): string {
    return `${this.COLLECTION_PATH}/${userId}/${SUBCOLLECTIONS.USER_LOGINS}`;
  }

  // Analytics CRUD operations
  async createAnalyticsRecord(
    userId: string,
    analyticsData: Omit<UserAnalytics, 'id'>
  ): Promise<ServiceResponse<string>> {
    const collectionPath = this.getUserAnalyticsPath(userId);
    return this.createDocument<UserAnalytics>(collectionPath, analyticsData);
  }

  async getAnalyticsRecord(
    userId: string,
    recordId: string
  ): Promise<ServiceResponse<UserAnalytics | null>> {
    const collectionPath = this.getUserAnalyticsPath(userId);
    return this.getDocument<UserAnalytics>(collectionPath, recordId);
  }

  async getUserAnalytics(
    userId: string,
    options?: QueryOptions
  ): Promise<ServiceResponse<UserAnalytics[]>> {
    const collectionPath = this.getUserAnalyticsPath(userId);
    return this.getCollection<UserAnalytics>(collectionPath, options);
  }

  async deleteAnalyticsRecord(
    userId: string,
    recordId: string
  ): Promise<ServiceResponse<void>> {
    const collectionPath = this.getUserAnalyticsPath(userId);
    return this.deleteDocument(collectionPath, recordId);
  }

  // Batch analytics operations
  async createAnalyticsBatch(
    userId: string,
    analyticsRecords: Omit<UserAnalytics, 'id'>[]
  ): Promise<ServiceResponse<string[]>> {
    try {
      const results: string[] = [];
      const collectionPath = this.getUserAnalyticsPath(userId);
      
      for (const record of analyticsRecords) {
        const result = await this.createDocument<UserAnalytics>(collectionPath, record);
        if (result.success && result.data) {
          results.push(result.data);
        } else {
          return this.createErrorResponse({
            operation: 'create',
            collection: collectionPath,
            originalError: new Error(result.error || 'Failed to create analytics record'),
            timestamp: new Date(),
          });
        }
      }
      
      return this.createSuccessResponse(results);
    } catch (error) {
      const dbError = this.handleError('create', this.getUserAnalyticsPath(userId), error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  // Daily analytics operations
  async createDailyAnalytics(
    userId: string,
    date: string,
    dailyData: Omit<UserAnalyticsDaily, 'id' | 'date'>
  ): Promise<ServiceResponse<string>> {
    const collectionPath = this.getUserAnalyticsDailyPath(userId);
    const analyticsData: Omit<UserAnalyticsDaily, 'id'> = {
      date,
      ...dailyData,
    };
    return this.createDocument<UserAnalyticsDaily>(collectionPath, analyticsData, date);
  }

  async getDailyAnalytics(
    userId: string,
    date: string
  ): Promise<ServiceResponse<UserAnalyticsDaily | null>> {
    const collectionPath = this.getUserAnalyticsDailyPath(userId);
    return this.getDocument<UserAnalyticsDaily>(collectionPath, date);
  }

  async updateDailyAnalytics(
    userId: string,
    date: string,
    updateData: Partial<UserAnalyticsDaily>
  ): Promise<ServiceResponse<void>> {
    const collectionPath = this.getUserAnalyticsDailyPath(userId);
    return this.updateDocument<UserAnalyticsDaily>(collectionPath, date, updateData);
  }

  async getUserDailyAnalytics(
    userId: string,
    options?: QueryOptions
  ): Promise<ServiceResponse<UserAnalyticsDaily[]>> {
    const collectionPath = this.getUserAnalyticsDailyPath(userId);
    return this.getCollection<UserAnalyticsDaily>(collectionPath, options);
  }

  // Login tracking operations
  async recordUserLogin(
    userId: string,
    loginData: Omit<UserLogin, 'id'>
  ): Promise<ServiceResponse<string>> {
    const collectionPath = this.getUserLoginsPath(userId);
    return this.createDocument<UserLogin>(collectionPath, loginData);
  }

  async getUserLogins(
    userId: string,
    options?: QueryOptions
  ): Promise<ServiceResponse<UserLogin[]>> {
    const collectionPath = this.getUserLoginsPath(userId);
    return this.getCollection<UserLogin>(collectionPath, options);
  }

  async getRecentLogins(
    userId: string,
    limitCount: number = 10
  ): Promise<ServiceResponse<UserLogin[]>> {
    const options: QueryOptions = {
      orderBy: { field: 'loginTime', direction: 'desc' },
      limit: limitCount
    };
    return this.getUserLogins(userId, options);
  }

  // Analytics aggregation and statistics
  async getAnalyticsStats(
    userId: string,
    startDate?: string,
    endDate?: string
  ): Promise<ServiceResponse<{
    totalEvents: number;
    eventsByType: Record<string, number>;
    eventsByPriority: Record<string, number>;
    averageProcessingTime: number;
    successRate: number;
    errors: number;
  }>> {
    try {
      const options: QueryOptions = {
        orderBy: { field: 'timestamp', direction: 'desc' }
      };

      if (startDate && endDate) {
        options.where = [
          { field: 'timestamp', operator: '>=', value: startDate },
          { field: 'timestamp', operator: '<=', value: endDate }
        ];
      }

      const analyticsResult = await this.getUserAnalytics(userId, options);
      if (!analyticsResult.success) {
        return this.createErrorResponse(analyticsResult as any);
      }

      const analytics = analyticsResult.data || [];
      
      const stats = {
        totalEvents: analytics.length,
        eventsByType: {} as Record<string, number>,
        eventsByPriority: {} as Record<string, number>,
        averageProcessingTime: 0,
        successRate: 0,
        errors: 0,
      };

      let totalProcessingTime = 0;
      let processedEvents = 0;

      analytics.forEach(record => {
        // Count by event type
        stats.eventsByType[record.eventType] = (stats.eventsByType[record.eventType] || 0) + 1;
        
        // Count by priority
        stats.eventsByPriority[record.priority] = (stats.eventsByPriority[record.priority] || 0) + 1;
        
        // Track processing time
        if (record.processingTime) {
          totalProcessingTime += record.processingTime;
          processedEvents++;
        }
        
        // Count errors
        if (!record.success) {
          stats.errors++;
        }
      });

      // Calculate averages
      if (processedEvents > 0) {
        stats.averageProcessingTime = totalProcessingTime / processedEvents;
      }

      if (stats.totalEvents > 0) {
        stats.successRate = ((stats.totalEvents - stats.errors) / stats.totalEvents) * 100;
      }

      return this.createSuccessResponse(stats);
    } catch (error) {
      const dbError = this.handleError('read', this.getUserAnalyticsPath(userId), error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  // Real-time subscriptions
  subscribeToUserAnalytics(
    userId: string,
    callback: (analytics: UserAnalytics[]) => void,
    options?: QueryOptions
  ): UnsubscribeFunction {
    const collectionPath = this.getUserAnalyticsPath(userId);
    return this.subscribeToCollection<UserAnalytics>(collectionPath, callback, options);
  }

  subscribeToUserDailyAnalytics(
    userId: string,
    callback: (dailyAnalytics: UserAnalyticsDaily[]) => void
  ): UnsubscribeFunction {
    const collectionPath = this.getUserAnalyticsDailyPath(userId);
    return this.subscribeToCollection<UserAnalyticsDaily>(collectionPath, callback);
  }

  subscribeToUserLogins(
    userId: string,
    callback: (logins: UserLogin[]) => void
  ): UnsubscribeFunction {
    const collectionPath = this.getUserLoginsPath(userId);
    return this.subscribeToCollection<UserLogin>(collectionPath, callback);
  }

  // Cleanup operations
  async cleanupOldAnalytics(
    userId: string,
    daysToKeep: number = 90
  ): Promise<ServiceResponse<number>> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
      const cutoffTimestamp = cutoffDate.toISOString();

      const options: QueryOptions = {
        where: [{ field: 'timestamp', operator: '<', value: cutoffTimestamp }]
      };

      const oldAnalyticsResult = await this.getUserAnalytics(userId, options);
      if (!oldAnalyticsResult.success) {
        return this.createErrorResponse(oldAnalyticsResult as any);
      }

      const oldRecords = oldAnalyticsResult.data || [];
      let deletedCount = 0;

      for (const record of oldRecords) {
        if (record.id) {
          const deleteResult = await this.deleteAnalyticsRecord(userId, record.id);
          if (deleteResult.success) {
            deletedCount++;
          }
        }
      }

      return this.createSuccessResponse(deletedCount);
    } catch (error) {
      const dbError = this.handleError('delete', this.getUserAnalyticsPath(userId), error as Error);
      return this.createErrorResponse(dbError);
    }
  }
}
