// Integration Example: How to use the Event System in React Components
// This file shows how to integrate the new event system into existing components

import React, { useEffect } from 'react';
import { Platform } from 'react-native';
import { 
  emitUserEvent,
  emitProgramEvent,
  emitPaymentEvent,
  sendNotification,
  EventType,
  EventPriority,
  NotificationType 
} from '../index';

// Example: Enhanced SignIn component with event system
export const EnhancedSignInExample = () => {
  
  // Example: User signup with events
  const handleUserSignup = async (userData: {
    email: string;
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    gender: string;
  }) => {
    try {
      // ... existing signup logic ...
      
      // Emit user signup event (replaces direct notification call)
      await emitUserEvent(EventType.USER_SIGNUP, {
        userEmail: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        source: 'SignUpScreen',
        deviceInfo: {
          platform: Platform.OS,
          version: '1.0.0',
        },
        metadata: {
          signupMethod: 'email',
          hasProfilePicture: false,
        },
      }, EventPriority.HIGH);

      console.log('User signup event emitted successfully');
    } catch (error) {
      console.error('Signup failed:', error);
      
      // Send error notification
      await sendNotification(
        userData.email,
        'Signup Issue',
        'There was an issue with your signup. Please try again.',
        NotificationType.ACCOUNT,
        EventPriority.HIGH
      );
    }
  };

  // Example: User login with events
  const handleUserLogin = async (email: string) => {
    try {
      // ... existing login logic ...
      
      // Emit user login event
      await emitUserEvent(EventType.USER_LOGIN, {
        userEmail: email,
        source: 'LoginScreen',
        deviceInfo: {
          platform: Platform.OS,
          version: '1.0.0',
        },
        metadata: {
          loginMethod: 'email_password',
          rememberMe: true,
        },
      }, EventPriority.LOW);

      console.log('User login event emitted successfully');
    } catch (error) {
      console.error('Login failed:', error);
    }
  };

  return null; // This is just an example
};

// Example: Enhanced Program Details component
export const EnhancedProgramDetailsExample = () => {
  
  const handleProgramJoin = async (program: {
    id: string;
    name: string;
    category: string;
    startDate: string;
  }, userEmail: string) => {
    try {
      // ... existing program join logic ...
      
      // Emit program join event
      await emitProgramEvent(EventType.PROGRAM_JOIN, {
        userEmail,
        programId: program.id,
        programName: program.name,
        programCategory: program.category,
        source: 'ProgramDetailsScreen',
        metadata: {
          joinMethod: 'direct',
          programStartDate: program.startDate,
          hasPayment: true,
        },
      }, EventPriority.MEDIUM);

      console.log('Program join event emitted successfully');
    } catch (error) {
      console.error('Program join failed:', error);
    }
  };

  const handleProgramSetupComplete = async (
    userEmail: string,
    programId: string,
    programName: string,
    category: string,
    setupValue: string
  ) => {
    try {
      // ... existing setup logic ...
      
      // Emit setup complete event
      await emitProgramEvent(EventType.PROGRAM_SETUP_COMPLETE, {
        userEmail,
        programId,
        programName,
        programCategory: category,
        source: 'CategorySetup',
        metadata: {
          setupType: category,
          setupValue,
          completedAt: new Date().toISOString(),
        },
      }, EventPriority.HIGH);

      console.log('Program setup complete event emitted successfully');
    } catch (error) {
      console.error('Setup completion event failed:', error);
    }
  };

  return null; // This is just an example
};

// Example: Enhanced Payment component
export const EnhancedPaymentExample = () => {
  
  const handlePaymentSuccess = async (
    userEmail: string,
    paymentIntentId: string,
    amount: number,
    currency: string,
    programId?: string
  ) => {
    try {
      // ... existing payment success logic ...
      
      // Emit payment success event
      await emitPaymentEvent(EventType.PAYMENT_SUCCESS, {
        userEmail,
        paymentIntentId,
        amount,
        currency,
        programId,
        source: 'PaymentConfirmation',
        metadata: {
          paymentMethod: 'stripe',
          processingTime: Date.now(),
        },
      }, EventPriority.HIGH);

      console.log('Payment success event emitted successfully');
    } catch (error) {
      console.error('Payment success event failed:', error);
    }
  };

  const handlePaymentFailure = async (
    userEmail: string,
    paymentIntentId: string,
    amount: number,
    currency: string,
    errorMessage: string
  ) => {
    try {
      // Emit payment failure event
      await emitPaymentEvent(EventType.PAYMENT_FAILED, {
        userEmail,
        paymentIntentId,
        amount,
        currency,
        source: 'PaymentConfirmation',
        metadata: {
          errorMessage,
          failureReason: 'payment_declined',
          retryAttempt: 1,
        },
      }, EventPriority.HIGH);

      console.log('Payment failure event emitted successfully');
    } catch (error) {
      console.error('Payment failure event failed:', error);
    }
  };

  return null; // This is just an example
};

// Example: Custom hook for event system
export const useEventSystem = () => {
  useEffect(() => {
    // Initialize event system when component mounts
    const initializeEvents = async () => {
      try {
        const { initializeEventSystem } = await import('../init');
        initializeEventSystem();
      } catch (error) {
        console.error('Failed to initialize event system:', error);
      }
    };

    initializeEvents();
  }, []);

  const emitCustomEvent = async (
    type: EventType,
    data: any,
    priority: EventPriority = EventPriority.MEDIUM
  ) => {
    try {
      const { getEventManager } = await import('../index');
      const eventManager = getEventManager();
      return await eventManager.emit(type, data, priority);
    } catch (error) {
      console.error('Failed to emit custom event:', error);
      throw error;
    }
  };

  const sendCustomNotification = async (
    userEmail: string,
    title: string,
    message: string,
    type: NotificationType = NotificationType.SYSTEM,
    priority: EventPriority = EventPriority.MEDIUM
  ) => {
    try {
      return await sendNotification(userEmail, title, message, type, priority);
    } catch (error) {
      console.error('Failed to send custom notification:', error);
      throw error;
    }
  };

  return {
    emitCustomEvent,
    sendCustomNotification,
  };
};

// Example: Migration helper for existing components
export const migrateNotificationCalls = () => {
  // Helper function to migrate from old notification system
  const migrateCreateNotification = async (
    userEmail: string,
    notification: {
      title: string;
      message: string;
      type: "account" | "program" | "points" | "reminder";
      priority: "low" | "medium" | "high";
    }
  ) => {
    // Map old types to new types
    const typeMapping: Record<string, NotificationType> = {
      account: NotificationType.ACCOUNT,
      program: NotificationType.PROGRAM,
      points: NotificationType.POINTS,
      reminder: NotificationType.REMINDER,
    };

    const priorityMapping: Record<string, EventPriority> = {
      low: EventPriority.LOW,
      medium: EventPriority.MEDIUM,
      high: EventPriority.HIGH,
    };

    return await sendNotification(
      userEmail,
      notification.title,
      notification.message,
      typeMapping[notification.type] || NotificationType.SYSTEM,
      priorityMapping[notification.priority] || EventPriority.MEDIUM
    );
  };

  return { migrateCreateNotification };
};

// Example: Error boundary with event system
export class EventSystemErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true };
  }

  async componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('EventSystemErrorBoundary caught an error:', error, errorInfo);
    
    try {
      // Emit system error event
      const { getEventManager } = await import('../index');
      const eventManager = getEventManager();
      
      await eventManager.emit(EventType.SYSTEM_ERROR, {
        timestamp: new Date().toISOString(),
        source: 'EventSystemErrorBoundary',
        errorMessage: error.message,
        stackTrace: error.stack,
        metadata: {
          componentStack: errorInfo.componentStack,
          errorBoundary: true,
        },
      }, EventPriority.CRITICAL);
    } catch (eventError) {
      console.error('Failed to emit error event:', eventError);
    }
  }

  render() {
    if (this.state.hasError) {
      return <div>Something went wrong with the event system.</div>;
    }

    return this.props.children;
  }
}
