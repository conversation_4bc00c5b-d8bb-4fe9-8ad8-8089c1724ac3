import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Modal,
  ActivityIndicator,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { firestoreService } from "../../../services/database";
import { useTheme } from "@/contexts/ThemeContext";
import { VerificationInput } from "../../verification/VerificationInput";
import { githubService, GitHubRepository } from "@/services/githubService";
import {
  getVerificationConfig,
  VerificationData,
  VerificationResult,
  deserializeVerificationData,
  serializeVerificationData
} from "../../../types/verification";

interface CategoryInputProps {
  program_id: string;
  category: string | undefined;
  permissionGranted: boolean | undefined;
  livesLeft: number;
  livesPurchaseLeft: number;
  day: number;
  user_id: string;
  onSubmissionSuccess: () => any;
  requestPermission: () => any;
  isLoading: boolean;
  // Add a refresh trigger to force re-checking of existing submissions
  refreshTrigger?: number;
}

const CategoryInput: React.FC<CategoryInputProps> = ({
  program_id,
  category,
  day,
  permissionGranted,
  livesLeft,
  livesPurchaseLeft,
  user_id,
  onSubmissionSuccess,
  isLoading,
  refreshTrigger = 0,
}) => {
  const { colors } = useTheme();

  // State for verification component
  const [initialVerificationData, setInitialVerificationData] = useState<VerificationData | null>(null);
  const [checkingExistingSubmission, setCheckingExistingSubmission] = useState<boolean>(true);
  const [isVerificationValid, setIsVerificationValid] = useState<boolean>(false);

  // Bailout state
  const [isBailingOut, setIsBailingOut] = useState(false);
  const [bailOutSuccessMessage, setBailOutSuccessMessage] = useState<string | null>(null);

  // Check for existing submission on mount and when dependencies change
  useEffect(() => {
    const checkExistingSubmission = async () => {
      if (!program_id || !user_id || !day || !category) {
        setCheckingExistingSubmission(false);
        return;
      }

      setCheckingExistingSubmission(true);

      try {
        const dayX = `Day ${day}`;

        const existingSubmission = await firestoreService.submissions.getSubmission(
          program_id,
          user_id,
          dayX
        );

        if (existingSubmission.success && existingSubmission.data) {
          const submissionData = existingSubmission.data;

          // Try to deserialize verification data from attachment
          if (submissionData.attachment && typeof submissionData.attachment === 'string') {
            const verificationData = deserializeVerificationData(submissionData.attachment);
            if (verificationData) {
              setInitialVerificationData(verificationData);
            } else {
              // Handle legacy data format
              const config = getVerificationConfig(category);
              if (config.type === 'text') {
                setInitialVerificationData({
                  type: 'text',
                  textValue: submissionData.attachment,
                  timestamp: submissionData.timestamp || new Date().toISOString(),
                });
              } else if (config.type === 'camera') {
                setInitialVerificationData({
                  type: 'camera',
                  images: [submissionData.attachment],
                  timestamp: submissionData.timestamp || new Date().toISOString(),
                });
              } else if (config.type === 'camera+gps') {
                // Try to parse gym data
                try {
                  const gymData = JSON.parse(submissionData.attachment);
                  setInitialVerificationData({
                    type: 'camera+gps',
                    images: gymData.imageUrls || (gymData.imageUrl ? [gymData.imageUrl] : []),
                    location: gymData.gpsCoordinates || null,
                    timestamp: gymData.timestamp || new Date().toISOString(),
                  });
                } catch (error) {
                  console.error("Error parsing legacy gym data:", error);
                  setInitialVerificationData(null);
                }
              }
            }
          } else {
            setInitialVerificationData(null);
          }
        } else {
          setInitialVerificationData(null);
        }
      } catch (error) {
        console.log("CategoryInput: Error checking existing submission:", error);
        setInitialVerificationData(null);
      } finally {
        setCheckingExistingSubmission(false);
      }
    };

    checkExistingSubmission();
  }, [program_id, user_id, day, category, refreshTrigger]);

  // Modal state
  const [bailoutModalVisible, setBailoutModalVisible] = useState(false);
  const [purchaseModalVisible, setPurchaseModalVisible] = useState(false);
  const [isPurchasing, setIsPurchasing] = useState(false);
  const [purchaseCount, setPurchaseCount] = useState(1);

  // Handle verification submission
  const handleVerificationSubmit = async (data: VerificationData): Promise<VerificationResult> => {
    if (!category) {
      return { success: false, error: "Category is required" };
    }

    try {
      const dayX = `Day ${day}`;
      const serializedData = serializeVerificationData(data);

      // Update submission using centralized service
      const result = await firestoreService.submissions.updateSubmissionStatus(
        program_id,
        user_id,
        dayX,
        "submitted",
        serializedData
      );

      if (!result.success) {
        return { success: false, error: result.error || "Failed to update submission" };
      }

      // Trigger success callback
      onSubmissionSuccess();

      return { success: true, data };
    } catch (error) {
      console.error("Error during verification submission:", error);
      return { success: false, error: "An error occurred during submission" };
    }
  };



  const handleBailOut = async () => {
    if (livesLeft <= 0) {
      Alert.alert(
        "No Lives Left",
        "You have no lives left. Please purchase more to bail out!"
      );
      return;
    }

    setIsBailingOut(true);
    const dayX = `Day ${day}`;

    try {
      // Get user email from AsyncStorage or your auth system
      const userEmail = await AsyncStorage.getItem('userEmail');

      // Use centralized service for bailout
      const result = await firestoreService.bailOutForDay(user_id, program_id, dayX);

      if (result.success) {
        // Create notification using user's email
        if (userEmail) {
          await firestoreService.notifications.createNotification(userEmail, {
            title: "Bailout Successful",
            message: `You've used a life to bail out of Day ${day}. ${livesLeft - 1} lives remaining. Stay consistent tomorrow!`,
            type: "program",
            priority: "high"
          });
        }
      } else {
        throw new Error(result.error || "Bailout failed");
      }

      // Instead of alerting, set a success message to be shown in the modal
      setBailOutSuccessMessage(
        "You have used a life. Your streak is safe for today!"
      );
    } catch (error) {
      console.error("Error during bailout:", error);
      Alert.alert("Bailout Failed", "An error occurred during bailout.");
    } finally {
      setIsBailingOut(false);
    }
  };

  const handlePurchaseCountChange = (increment: boolean) => {
    if (increment) {
      if (purchaseCount < livesPurchaseLeft) {
        setPurchaseCount(purchaseCount + 1);
      }
    } else {
      if (purchaseCount > 1) {
        setPurchaseCount(purchaseCount - 1);
      }
    }
  };

  const handlePurchaseLives = async () => {
    setIsPurchasing(true);

    try {
      const result = await firestoreService.participants.purchaseLives(
        program_id,
        user_id,
        purchaseCount
      );

      if (result.success) {
        setPurchaseModalVisible(false);
        onSubmissionSuccess();
      } else {
        Alert.alert("Error", result.error || "Failed to process purchase");
      }
    } catch (error) {
      console.error("Error during purchase:", error);
      Alert.alert(
        "Error",
        "Failed to process purchase. Please try again."
      );
    } finally {
      setIsPurchasing(false);
      setPurchaseCount(1); // Reset counter after purchase
    }
  };

  const styles = createStyles(colors);

  return (
    <View style={styles.container}>
      <View style={styles.headerRow}>
        <Text style={styles.title}>
          {category === "journals" || category === "affirmations" || category === "writing"
            ? `Submit your ${
                category === "journals" ? "Daily Journal" :
                category === "affirmations" ? "Daily Affirmation" :
                category === "writing" ? "Daily Writing" : "Daily Submission"
              }`
            : category === "gym"
            ? "Submit your Gym Workout"
            : category === "cardio"
            ? "Submit your Strava link"
            : category === "coding"
            ? "GitHub Commit Verification"
            : "Submit your link"}
        </Text>
        <View style={styles.buttonGroup}>
          {livesPurchaseLeft > 0 && (
            <TouchableOpacity
              style={styles.purchaseButton}
              onPress={() => setPurchaseModalVisible(true)}
              disabled={isLoading}
            >
              <MaterialIcons name="shopping-cart" size={16} color={colors.primary} />
            </TouchableOpacity>
          )}
          <TouchableOpacity
            style={[
              styles.bailOutButton,
              (livesLeft === 0 || isLoading) && styles.disabled
            ]}
            onPress={() => setBailoutModalVisible(true)}
            disabled={livesLeft === 0 || isLoading}
          >
            <Text style={[
              styles.bailOutButtonText,
              isLoading && styles.disabledText
            ]}>
              Bail Out ({livesLeft})
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {checkingExistingSubmission ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={colors.primary} />
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      ) : category ? (
        <VerificationInput
          config={getVerificationConfig(category)}
          programId={program_id}
          userId={user_id}
          day={day}
          onSubmit={handleVerificationSubmit}
          onValidationChange={setIsVerificationValid}
          initialData={initialVerificationData}
          disabled={isLoading}
        />
      ) : null}

      <Modal
        transparent={true}
        visible={bailoutModalVisible}
        onRequestClose={() => setBailoutModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            {isBailingOut ? (
              <ActivityIndicator size="large" color="#FFEB3B" />
            ) : bailOutSuccessMessage ? (
              <>
                <Text style={styles.modalTitle}>Bail Out Successful!</Text>
                <Text style={styles.modalMessage}>{bailOutSuccessMessage}</Text>
                <TouchableOpacity
                  style={[styles.modalButton, styles.confirmButton]}
                  onPress={() => {
                    setBailoutModalVisible(false);
                    onSubmissionSuccess(); // Notify parent of successful submission
                  }}
                >
                  <Text style={styles.confirmButtonText}>OK</Text>
                </TouchableOpacity>
              </>
            ) : (
              <>
                <Text style={styles.modalTitle}>Are you sure?</Text>
                <Text style={styles.modalMessage}>
                  Using a bailout will consume one life. Confirm to proceed.
                </Text>
                <View style={styles.modalButtons}>
                  <TouchableOpacity
                    style={[
                      styles.modalButton,
                      styles.cancelButton,
                      isBailingOut && styles.disabled,
                    ]}
                    onPress={() => setBailoutModalVisible(false)}
                    disabled={isBailingOut}
                  >
                    <Text style={styles.modalButtonText}>Cancel</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      styles.modalButton,
                      styles.confirmButton,
                      isBailingOut && styles.disabled,
                    ]}
                    onPress={handleBailOut}
                    disabled={isBailingOut}
                  >
                    <Text style={styles.modalButtonText}>Confirm</Text>
                  </TouchableOpacity>
                </View>
              </>
            )}
          </View>
        </View>
      </Modal>

      <Modal
        transparent={true}
        visible={purchaseModalVisible}
        onRequestClose={() => {
          setPurchaseModalVisible(false);
          setPurchaseCount(1);
        }}
      >
        <TouchableOpacity 
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => {
            setPurchaseModalVisible(false);
            setPurchaseCount(1);
          }}
        >
          <View 
            style={styles.modalContainer}
            onStartShouldSetResponder={() => true}
            onTouchEnd={(e) => {
              e.stopPropagation();
            }}
          >
            <View style={styles.modalContent}>
              <Text style={styles.modalTitle}>Purchase Lives</Text>
              <Text style={styles.modalMessage}>
                How many lives would you like to purchase?{'\n'}
                Available: {livesPurchaseLeft}
              </Text>
              
              <View style={styles.counterContainer}>
                <TouchableOpacity
                  style={[
                    styles.counterButton,
                    purchaseCount <= 1 && styles.disabled
                  ]}
                  onPress={(e) => {
                    e.stopPropagation();
                    handlePurchaseCountChange(false);
                  }}
                  disabled={purchaseCount <= 1 || isPurchasing}
                >
                  <Text style={styles.counterButtonText}>-</Text>
                </TouchableOpacity>
                
                <Text style={styles.counterText}>{purchaseCount}</Text>
                
                <TouchableOpacity
                  style={[
                    styles.counterButton,
                    purchaseCount >= livesPurchaseLeft && styles.disabled
                  ]}
                  onPress={(e) => {
                    e.stopPropagation();
                    handlePurchaseCountChange(true);
                  }}
                  disabled={purchaseCount >= livesPurchaseLeft || isPurchasing}
                >
                  <Text style={styles.counterButtonText}>+</Text>
                </TouchableOpacity>
              </View>

              <View style={styles.modalButtons}>
                <TouchableOpacity
                  style={[styles.modalButton, styles.cancelButton]}
                  onPress={(e) => {
                    e.stopPropagation();
                    setPurchaseModalVisible(false);
                    setPurchaseCount(1);
                  }}
                  disabled={isPurchasing}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.modalButton, styles.confirmButton]}
                  onPress={(e) => {
                    e.stopPropagation();
                    handlePurchaseLives();
                  }}
                  disabled={isPurchasing}
                >
                  {isPurchasing ? (
                    <ActivityIndicator size="small" color="#000" />
                  ) : (
                    <Text style={styles.confirmButtonText}>
                      Purchase {purchaseCount}
                    </Text>
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    padding: 10,
    backgroundColor: colors.card,
    borderRadius: 10,
    marginTop: 10,
  },
  headerRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  title: {
    fontSize: 14,
    color: colors.primary,
    fontFamily: "MontserratBold",
  },
  buttonGroup: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  purchaseButton: {
    backgroundColor: colors.surface,
    padding: 8,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  bailOutButton: {
    backgroundColor: colors.background,
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 20,
  },
  bailOutButtonText: {
    color: colors.warning,
    fontSize: 12,
  },
  loadingContainer: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 40,
    backgroundColor: colors.surface,
    borderRadius: 12,
    marginBottom: 15,
  },
  loadingText: {
    color: colors.textMuted,
    fontSize: 14,
    fontFamily: "Montserrat",
    marginTop: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '95%',
    maxWidth: 400,
  },
  modalContent: {
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    width: '95%',
    maxWidth: 400,
    margin: 20,
  },
  modalTitle: {
    fontSize: 20,
    color: colors.primary,
    fontFamily: 'MontserratBold',
    marginBottom: 12,
    textAlign: 'center',
  },
  modalMessage: {
    fontSize: 16,
    color: colors.text,
    fontFamily: 'MontserratRegular',
    marginBottom: 20,
    textAlign: 'center',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    gap: 16,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 120,
  },
  confirmButton: {
    backgroundColor: colors.primary,
  },
  cancelButton: {
    backgroundColor: colors.surface,
  },
  confirmButtonText: {
    color: colors.background,
    fontSize: 16,
    fontFamily: 'MontserratBold',
    textAlign: 'center',
  },
  cancelButtonText: {
    color: colors.text,
    fontSize: 16,
    fontFamily: 'MontserratBold',
    textAlign: 'center',
  },
  disabled: {
    opacity: 0.5,
    backgroundColor: colors.surface,
  },
  disabledText: {
    color: colors.textMuted,
  },
  disabledInput: {
    backgroundColor: colors.surface,
    color: colors.textMuted,
  },
  disabledButton: {
    opacity: 0.5,
    backgroundColor: colors.surface,
  },
  modalButtonText: {
    color: colors.text,
    fontSize: 16,
    fontFamily: 'MontserratBold',
  },
  counterContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    gap: 20,
  },
  counterButton: {
    backgroundColor: colors.primary,
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  counterButtonText: {
    fontSize: 24,
    color: colors.background,
    fontFamily: 'MontserratBold',
  },
  counterText: {
    fontSize: 24,
    color: colors.primary,
    fontFamily: "MontserratBold",
  },
});

export default CategoryInput;