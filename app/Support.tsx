import { useCameraPermissions } from "expo-camera";
import { useRouter } from "expo-router";
import React, { useEffect, useState } from "react";
import { View, StyleSheet, TouchableOpacity, Text, Alert } from "react-native";
import { WebView } from "react-native-webview";
import { useTheme } from "@/contexts/ThemeContext";

const Support = () => {
  const { colors } = useTheme();
  const router = useRouter();
  const [permission, requestPermission] = useCameraPermissions();
  const [hasPermission, setHasPermission] = useState(false);

  useEffect(() => {
    checkCameraPermission();
  }, []);

  const onMessage = (event: any) => {
    const data = event.nativeEvent.data;
    if (data === 'true') {
      // Handle the save action here
      console.log('Save button pressed in web app');
      // Add your save logic here
    }
  };


  const checkCameraPermission = async () => {
    if (!permission?.granted) {
      const newPermission = await requestPermission();
      if (!newPermission.granted) {
        Alert.alert(
          "Camera Access Required",
          "This feature requires camera access. Please enable it to continue.",
          [
            { 
              text: "Cancel", 
              onPress: () => router.back(), 
              style: "cancel" 
            },
            { 
              text: "Try Again", 
              onPress: checkCameraPermission 
            }
          ]
        );
        return;
      }
    }
    setHasPermission(true);
  };

  const styles = createStyles(colors);

  return (
    <View style={styles.container}>
      <WebView
        source={{ uri: "https://habitroyale.netlify.app/blog" }}
        onMessage={onMessage}

        style={styles.webview}
        startInLoadingState
      />
      <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
        <Text style={styles.backButtonText}>Go Back</Text>
      </TouchableOpacity>
    </View>
  );
};

export default Support;

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  webview: {
    flex: 1,
  },
  backButton: {
    padding: 10,
  },
  backButtonText: {
    color: colors.primary,
    fontWeight: "bold",
  },
});
