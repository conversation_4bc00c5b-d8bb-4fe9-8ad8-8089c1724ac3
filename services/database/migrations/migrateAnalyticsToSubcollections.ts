import { 
  collection, 
  getDocs, 
  doc, 
  setDoc, 
  deleteDoc, 
  writeBatch,
  query,
  orderBy,
  limit,
  startAfter,
  DocumentSnapshot
} from 'firebase/firestore';
import { db } from '../../../config/firebase';
import { firestoreService } from '../FirestoreService';

interface OldAnalyticsRecord {
  id?: string;
  eventType: string;
  priority: string;
  userId?: string;
  userEmail?: string;
  timestamp: string;
  source: string;
  metadata: Record<string, any>;
  processingTime?: number;
  success: boolean;
  createdAt?: any;
}

interface OldAnalyticsDailyRecord {
  id?: string;
  date: string;
  totalEvents: number;
  eventsByType: Record<string, number>;
  eventsByPriority: Record<string, number>;
  uniqueUsersCount: number;
  averageProcessingTime: number;
  successRate: number;
  errors: number;
  lastUpdated?: any;
}

interface OldUserLoginRecord {
  id?: string;
  userEmail: string;
  loginTime: string;
  platform: string;
  deviceInfo?: {
    platform: string;
    version: string;
  };
  ipAddress?: string;
  userAgent?: string;
  success: boolean;
  failureReason?: string;
  createdAt?: any;
}

export class AnalyticsMigration {
  private batchSize = 500;
  private migrationStats = {
    analytics: { processed: 0, migrated: 0, errors: 0 },
    analyticsDaily: { processed: 0, migrated: 0, errors: 0 },
    userLogins: { processed: 0, migrated: 0, errors: 0 }
  };

  async migrateAllAnalytics(): Promise<void> {
    console.log('🚀 Starting analytics migration to subcollections...');
    
    try {
      // Migrate analytics records
      await this.migrateAnalyticsRecords();
      
      // Migrate daily analytics
      await this.migrateAnalyticsDaily();
      
      // Migrate user logins
      await this.migrateUserLogins();
      
      console.log('✅ Analytics migration completed successfully!');
      console.log('📊 Migration Statistics:', this.migrationStats);
      
    } catch (error) {
      console.error('❌ Analytics migration failed:', error);
      throw error;
    }
  }

  private async migrateAnalyticsRecords(): Promise<void> {
    console.log('📈 Migrating analytics records...');
    
    let lastDoc: DocumentSnapshot | null = null;
    let hasMore = true;

    while (hasMore) {
      try {
        // Query analytics collection in batches
        let q = query(
          collection(db, 'analytics'),
          orderBy('createdAt'),
          limit(this.batchSize)
        );

        if (lastDoc) {
          q = query(q, startAfter(lastDoc));
        }

        const snapshot = await getDocs(q);
        
        if (snapshot.empty) {
          hasMore = false;
          break;
        }

        const batch = writeBatch(db);
        let batchCount = 0;

        for (const docSnapshot of snapshot.docs) {
          this.migrationStats.analytics.processed++;
          
          try {
            const data = docSnapshot.data() as OldAnalyticsRecord;
            
            // Skip records without userEmail
            if (!data.userEmail) {
              console.warn(`Skipping analytics record ${docSnapshot.id} - no userEmail`);
              continue;
            }

            // Create new analytics record in user subcollection
            const userAnalyticsRef = doc(
              db, 
              'users', 
              data.userEmail, 
              'analytics', 
              docSnapshot.id
            );

            const newAnalyticsData = {
              eventType: data.eventType,
              priority: data.priority,
              timestamp: data.timestamp,
              source: data.source,
              metadata: data.metadata || {},
              processingTime: data.processingTime,
              success: data.success,
              createdAt: data.createdAt,
              updatedAt: data.createdAt,
            };

            batch.set(userAnalyticsRef, newAnalyticsData);
            batchCount++;
            this.migrationStats.analytics.migrated++;

            // Commit batch when it reaches Firestore limit
            if (batchCount >= 500) {
              await batch.commit();
              const newBatch = writeBatch(db);
              batchCount = 0;
            }

          } catch (error) {
            console.error(`Error migrating analytics record ${docSnapshot.id}:`, error);
            this.migrationStats.analytics.errors++;
          }
        }

        // Commit remaining batch
        if (batchCount > 0) {
          await batch.commit();
        }

        lastDoc = snapshot.docs[snapshot.docs.length - 1];
        
        console.log(`Processed ${this.migrationStats.analytics.processed} analytics records...`);
        
      } catch (error) {
        console.error('Error in analytics batch migration:', error);
        throw error;
      }
    }

    console.log(`✅ Analytics records migration completed. Migrated: ${this.migrationStats.analytics.migrated}, Errors: ${this.migrationStats.analytics.errors}`);
  }

  private async migrateAnalyticsDaily(): Promise<void> {
    console.log('📊 Migrating daily analytics...');
    
    try {
      const snapshot = await getDocs(collection(db, 'analytics_daily'));
      
      // Group daily analytics by user (we'll need to determine user from the data)
      const userDailyAnalytics: Record<string, OldAnalyticsDailyRecord[]> = {};
      
      for (const docSnapshot of snapshot.docs) {
        this.migrationStats.analyticsDaily.processed++;
        
        try {
          const data = docSnapshot.data() as OldAnalyticsDailyRecord;
          
          // For daily analytics, we'll need to get all users and create the same daily record for each
          // This is a limitation of the old structure - daily analytics weren't user-specific
          const usersSnapshot = await getDocs(collection(db, 'users'));
          
          for (const userDoc of usersSnapshot.docs) {
            const userEmail = userDoc.id;
            
            if (!userDailyAnalytics[userEmail]) {
              userDailyAnalytics[userEmail] = [];
            }
            
            userDailyAnalytics[userEmail].push({
              ...data,
              id: docSnapshot.id
            });
          }
          
        } catch (error) {
          console.error(`Error processing daily analytics ${docSnapshot.id}:`, error);
          this.migrationStats.analyticsDaily.errors++;
        }
      }

      // Migrate to user subcollections
      for (const [userEmail, dailyRecords] of Object.entries(userDailyAnalytics)) {
        for (const record of dailyRecords) {
          try {
            await firestoreService.analytics.createDailyAnalytics(
              userEmail,
              record.date,
              {
                totalEvents: record.totalEvents,
                eventsByType: record.eventsByType,
                eventsByPriority: record.eventsByPriority,
                uniqueUsersCount: record.uniqueUsersCount,
                averageProcessingTime: record.averageProcessingTime,
                successRate: record.successRate,
                errors: record.errors,
                lastUpdated: record.lastUpdated,
              }
            );
            
            this.migrationStats.analyticsDaily.migrated++;
          } catch (error) {
            console.error(`Error migrating daily analytics for user ${userEmail}, date ${record.date}:`, error);
            this.migrationStats.analyticsDaily.errors++;
          }
        }
      }

      console.log(`✅ Daily analytics migration completed. Migrated: ${this.migrationStats.analyticsDaily.migrated}, Errors: ${this.migrationStats.analyticsDaily.errors}`);
      
    } catch (error) {
      console.error('Error in daily analytics migration:', error);
      throw error;
    }
  }

  private async migrateUserLogins(): Promise<void> {
    console.log('🔐 Migrating user logins...');
    
    try {
      const snapshot = await getDocs(collection(db, 'userLogIns'));
      
      const batch = writeBatch(db);
      let batchCount = 0;

      for (const docSnapshot of snapshot.docs) {
        this.migrationStats.userLogins.processed++;
        
        try {
          const data = docSnapshot.data() as OldUserLoginRecord;
          
          // Skip records without userEmail
          if (!data.userEmail) {
            console.warn(`Skipping login record ${docSnapshot.id} - no userEmail`);
            continue;
          }

          // Create new login record in user subcollection
          const userLoginRef = doc(
            db, 
            'users', 
            data.userEmail, 
            'logins', 
            docSnapshot.id
          );

          const newLoginData = {
            loginTime: data.loginTime,
            platform: data.platform,
            deviceInfo: data.deviceInfo,
            ipAddress: data.ipAddress,
            userAgent: data.userAgent,
            success: data.success,
            failureReason: data.failureReason,
            createdAt: data.createdAt,
            updatedAt: data.createdAt,
          };

          batch.set(userLoginRef, newLoginData);
          batchCount++;
          this.migrationStats.userLogins.migrated++;

          // Commit batch when it reaches Firestore limit
          if (batchCount >= 500) {
            await batch.commit();
            const newBatch = writeBatch(db);
            batchCount = 0;
          }

        } catch (error) {
          console.error(`Error migrating login record ${docSnapshot.id}:`, error);
          this.migrationStats.userLogins.errors++;
        }
      }

      // Commit remaining batch
      if (batchCount > 0) {
        await batch.commit();
      }

      console.log(`✅ User logins migration completed. Migrated: ${this.migrationStats.userLogins.migrated}, Errors: ${this.migrationStats.userLogins.errors}`);
      
    } catch (error) {
      console.error('Error in user logins migration:', error);
      throw error;
    }
  }

  // Method to cleanup old collections after successful migration
  async cleanupOldCollections(): Promise<void> {
    console.log('🧹 Cleaning up old collections...');
    
    const collectionsToCleanup = ['analytics', 'analytics_daily', 'userLogIns'];
    
    for (const collectionName of collectionsToCleanup) {
      try {
        console.log(`Deleting ${collectionName} collection...`);
        
        let hasMore = true;
        while (hasMore) {
          const snapshot = await getDocs(
            query(collection(db, collectionName), limit(500))
          );
          
          if (snapshot.empty) {
            hasMore = false;
            break;
          }

          const batch = writeBatch(db);
          snapshot.docs.forEach(doc => {
            batch.delete(doc.ref);
          });
          
          await batch.commit();
          console.log(`Deleted ${snapshot.docs.length} documents from ${collectionName}`);
        }
        
        console.log(`✅ ${collectionName} collection cleanup completed`);
        
      } catch (error) {
        console.error(`Error cleaning up ${collectionName}:`, error);
      }
    }
  }

  // Dry run method to preview migration without making changes
  async dryRun(): Promise<void> {
    console.log('🔍 Running migration dry run...');
    
    try {
      // Count analytics records
      const analyticsSnapshot = await getDocs(collection(db, 'analytics'));
      console.log(`Found ${analyticsSnapshot.size} analytics records`);
      
      // Count daily analytics records
      const dailySnapshot = await getDocs(collection(db, 'analytics_daily'));
      console.log(`Found ${dailySnapshot.size} daily analytics records`);
      
      // Count user login records
      const loginsSnapshot = await getDocs(collection(db, 'userLogIns'));
      console.log(`Found ${loginsSnapshot.size} user login records`);
      
      // Count users
      const usersSnapshot = await getDocs(collection(db, 'users'));
      console.log(`Found ${usersSnapshot.size} users`);
      
      console.log('✅ Dry run completed');
      
    } catch (error) {
      console.error('Error in dry run:', error);
      throw error;
    }
  }
}

// Export migration instance
export const analyticsMigration = new AnalyticsMigration();

// Export convenience functions
export const migrateAnalytics = () => analyticsMigration.migrateAllAnalytics();
export const cleanupOldAnalytics = () => analyticsMigration.cleanupOldCollections();
export const dryRunAnalyticsMigration = () => analyticsMigration.dryRun();
