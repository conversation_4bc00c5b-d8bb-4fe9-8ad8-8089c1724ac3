// CategoryFilter.tsx
import React from "react";
import { ScrollView, TouchableOpacity, Text, StyleSheet } from "react-native";
import { useTheme } from "@/contexts/ThemeContext";

const CATEGORIES = ["All", "gym", "cardio", "coding", "journaling", "affirmations", "writing"];

interface CategoryFilterProps {
  selectedCategory: string;
  onSelectCategory: (category: string) => void;
}

const CategoryFilter: React.FC<CategoryFilterProps> = ({
  selectedCategory,
  onSelectCategory,
}) => {
  const { colors } = useTheme();

  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={styles.filterContainer}
    >
      {CATEGORIES.map((cat) => {
        const isActive = cat === selectedCategory;
        return (
          <TouchableOpacity
            key={cat}
            style={[
              styles.filterButton,
              { backgroundColor: colors.card },
              isActive && { backgroundColor: colors.primary }
            ]}
            onPress={() => onSelectCategory(cat)}
          >
            <Text style={[
              styles.filterButtonText,
              { color: colors.text },
              isActive && { color: '#000' }
            ]}>
              {cat.charAt(0).toUpperCase() + cat.slice(1)}
            </Text>
          </TouchableOpacity>
        );
      })}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  filterContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 5,
    paddingHorizontal: 10,
  },
  filterButton: {
    // backgroundColor will be set dynamically based on theme
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    marginRight: 8,
  },
  filterButtonText: {
    // color will be set dynamically based on theme
    fontSize: 14,
    fontWeight: "bold",
  },
});

export default CategoryFilter;
