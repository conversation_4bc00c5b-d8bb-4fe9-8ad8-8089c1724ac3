export interface LocationData {
  title: string;
  address: string;
  latitude: number;
  longitude: number;
}

export type EvidenceType =
  | 'photo'
  | 'video-timelapse'
  | 'camera-only'
  | 'gps-checkin'
  | 'gps-avoid'
  | 'strava'
  | 'screen-time'
  | 'github';

export type ReportingFrequency = 'daily' | 'weekly' | 'monthly' | 'once';

export type TimingType = 'mid-night' | 'before' | 'after' | 'between';

export type RecipientType = 'anti-charity' | 'charity' | 'habit-royale' | 'no-money';

export type ReminderChannel = 'app' | 'email' | 'sms' | 'whatsapp';

export type StrictnessLevel = 'no-grace' | 'lenient' | 'reasonable' | 'strict';

export type NotificationTimingType = 'hours-before' | 'every-x-hours' | 'multiple-times';

export interface NotificationTiming {
  type: NotificationTimingType;
  hoursBeforeDeadline?: number;
  everyXHours?: number;
  multipleTimes?: string[]; // Array of times like ['09:00', '18:00']
}

export interface PremiumFeatures {
  motivationalNote?: string;
  reminderChannels: ReminderChannel[];
  strictnessLevel: StrictnessLevel;
  notificationTiming?: NotificationTiming;
  prioritySupport?: boolean;
  advancedAnalytics?: boolean;
  addToCalendar?: boolean;
}

export interface CustomStakeData {
  commitment: string;
  evidenceType: EvidenceType;
  reportingFrequency: ReportingFrequency;
  startDate?: Date;
  endDate?: Date;
  weekLength?: number;
  timesPerWeek?: number;
  timesPerMonth?: number;
  recipient: RecipientType;
  amountPerReport: number;
  referee: 'honor' | string;
  // Timing fields
  timingType?: TimingType;
  beforeTime?: string;
  afterTime?: string;
  startTime?: string;
  endTime?: string;
  // Evidence-specific fields
  location?: string; // Legacy field
  locationData?: LocationData;
  appName?: string;
  stravaActivity?: string;
  // Premium features
  premiumFeatures?: PremiumFeatures;
}

export interface EvidenceOption {
  key: EvidenceType;
  label: string;
  icon: string;
  description: string;
}

export interface FrequencyOption {
  key: ReportingFrequency;
  label: string;
  icon: string;
  description: string;
}

export interface TimingOption {
  key: TimingType;
  label: string;
  icon: string;
  description: string;
}

export interface RecipientOption {
  key: RecipientType;
  label: string;
  icon: string;
  description: string;
}

export interface ValidationError {
  field: string;
  message: string;
}

export interface FormValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

export interface CalendarDay {
  day: string;
  isCurrentMonth: boolean;
  isSelected: boolean;
  isToday: boolean;
  isDisabled: boolean;
}

export interface ReportingSchedule {
  frequency: ReportingFrequency;
  dayOfWeek?: number;
  timesPerWeek?: number;
  dayOfMonth?: number;
  timesPerMonth?: number;
}

export interface CustomStakeSubmissionData extends CustomStakeData {
  totalStake: number;
  createdAt: string;
  calculatedEndDate?: Date | string;
  reportingSchedule: ReportingSchedule;
}

// Constants
export const EVIDENCE_OPTIONS: EvidenceOption[] = [
  {
    key: 'photo',
    label: 'Photo',
    icon: 'camera',
    description: 'Take a photo as proof of completion'
  },
  {
    key: 'video-timelapse',
    label: 'Video Timelapse',
    icon: 'videocam',
    description: 'Record a timelapse video'
  },
  {
    key: 'camera-only',
    label: 'Camera Only',
    icon: 'camera-alt',
    description: 'Camera access without saving'
  },
  {
    key: 'gps-checkin',
    label: 'GPS Check-in',
    icon: 'location-on',
    description: 'Check in at a specific location'
  },
  {
    key: 'gps-avoid',
    label: 'GPS Avoid',
    icon: 'location-off',
    description: 'Avoid a specific location'
  },
  {
    key: 'strava',
    label: 'Strava',
    icon: 'directions-run',
    description: 'Connect with Strava for activity tracking'
  },
  {
    key: 'github',
    label: 'GitHub',
    icon: 'code',
    description: 'Connect with GitHub for commit verification'
  },
  {
    key: 'screen-time',
    label: 'Screen Time',
    icon: 'screen-lock-portrait',
    description: 'Monitor app usage time'
  }
];

export const FREQUENCY_OPTIONS: FrequencyOption[] = [
  {
    key: 'daily',
    label: 'Daily',
    icon: 'calendar-today',
    description: 'Report every day'
  },
  {
    key: 'weekly',
    label: 'Weekly',
    icon: 'date-range',
    description: 'Report once per week'
  },
  {
    key: 'monthly',
    label: 'Monthly',
    icon: 'calendar-month',
    description: 'Report once per month'
  },
  {
    key: 'once',
    label: 'Once',
    icon: 'event',
    description: 'Single commitment'
  },
];

export const TIMING_OPTIONS: TimingOption[] = [
  {
    key: 'mid-night',
    label: 'Mid Night',
    icon: 'schedule',
    description: 'Complete by midnight (default)'
  },
  {
    key: 'before',
    label: 'Before',
    icon: 'schedule',
    description: 'Complete before a specific time'
  },
  {
    key: 'after',
    label: 'After',
    icon: 'schedule',
    description: 'Complete after a specific time'
  },
  {
    key: 'between',
    label: 'Between',
    icon: 'schedule',
    description: 'Complete between two times'
  },
];

export const RECIPIENT_OPTIONS: RecipientOption[] = [
  {
    key: 'anti-charity',
    label: 'Anti-Charity',
    icon: 'cancel',
    description: 'Money goes to causes you oppose'
  },
  {
    key: 'charity',
    label: 'Charity',
    icon: 'heart',
    description: 'Money goes to charitable organizations'
  },
  {
    key: 'habit-royale',
    label: 'Habit Royale',
    icon: 'crown',
    description: 'Money stays in the platform pool'
  },
  {
    key: 'no-money',
    label: 'No Money at Stake',
    icon: 'close-circle',
    description: 'Accountability without financial risk'
  },
];

export interface ReminderChannelOption {
  key: ReminderChannel;
  label: string;
  icon: string;
  description: string;
  isPremium: boolean;
}

export const REMINDER_CHANNEL_OPTIONS: ReminderChannelOption[] = [
  {
    key: 'app',
    label: 'App (Push)',
    icon: 'notifications',
    description: 'Push notifications in the app',
    isPremium: false
  },
  {
    key: 'email',
    label: 'Email',
    icon: 'email',
    description: 'Email reminders',
    isPremium: false
  },
  {
    key: 'sms',
    label: 'SMS',
    icon: 'sms',
    description: 'Text message reminders',
    isPremium: true
  },
  {
    key: 'whatsapp',
    label: 'WhatsApp',
    icon: 'chat',
    description: 'WhatsApp message reminders',
    isPremium: true
  },
];

export interface StrictnessOption {
  key: StrictnessLevel;
  label: string;
  icon: string;
  description: string;
  isPremium: boolean;
}

export const STRICTNESS_OPTIONS: StrictnessOption[] = [
  {
    key: 'lenient',
    label: 'Lenient',
    icon: 'sentiment-satisfied',
    description: 'Flexible approach with multiple chances',
    isPremium: true
  },
  {
    key: 'reasonable',
    label: 'Reasonable',
    icon: 'balance',
    description: 'Balanced approach - standard accountability',
    isPremium: false
  },
  {
    key: 'strict',
    label: 'Strict',
    icon: 'gavel',
    description: 'High accountability with minimal flexibility',
    isPremium: false
  },
  {
    key: 'no-grace',
    label: 'No Grace',
    icon: 'block',
    description: 'Zero tolerance - miss once and you lose',
    isPremium: true
  },
];

export interface NotificationTimingOption {
  key: NotificationTimingType;
  label: string;
  icon: string;
  description: string;
}

export const NOTIFICATION_TIMING_OPTIONS: NotificationTimingOption[] = [
  {
    key: 'hours-before',
    label: 'Hours Before Deadline',
    icon: 'schedule',
    description: 'Get notified X hours before your deadline'
  },
  {
    key: 'every-x-hours',
    label: 'Every X Hours',
    icon: 'repeat',
    description: 'Get reminded every X hours'
  },
  {
    key: 'multiple-times',
    label: 'Multiple Times',
    icon: 'access-time',
    description: 'Set multiple specific notification times'
  },
];
