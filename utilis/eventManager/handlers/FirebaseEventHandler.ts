import { firestoreService } from '../../../services/database';
import { Event, EventHandler, EventType, FirebaseEventDocument } from '../types';

export class FirebaseEventHandler implements EventHandler {
  public readonly name = 'FirebaseEventHandler';
  public readonly priority = 1; // High priority for persistence

  private readonly collectionName: string;
  private readonly enableBatching: boolean;
  private readonly batchSize: number;
  private eventBatch: Event[] = [];
  private batchTimeout: NodeJS.Timeout | null = null;

  constructor(
    collectionName: string = 'events',
    enableBatching: boolean = true,
    batchSize: number = 10
  ) {
    this.collectionName = collectionName;
    this.enableBatching = enableBatching;
    this.batchSize = batchSize;
  }

  public shouldHandle(event: Event): boolean {
    // Handle all events for persistence
    return true;
  }

  public async handle(event: Event): Promise<void> {
    try {
      if (this.enableBatching) {
        await this.handleBatched(event);
      } else {
        await this.handleSingle(event);
      }
    } catch (error) {
      console.error(`FirebaseEventHandler failed to handle event ${event.id}:`, error);
      throw error;
    }
  }

  private async handleBatched(event: Event): Promise<void> {
    this.eventBatch.push(event);

    // Clear existing timeout
    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
    }

    // If batch is full, process immediately
    if (this.eventBatch.length >= this.batchSize) {
      await this.processBatch();
    } else {
      // Set timeout to process batch after delay
      this.batchTimeout = setTimeout(() => {
        this.processBatch();
      }, 2000); // 2 second delay
    }
  }

  private async handleSingle(event: Event): Promise<void> {
    const firebaseDoc = this.convertToFirebaseDocument(event);

    const result = await firestoreService.createEvent({
      ...firebaseDoc,
      processedAt: firebaseDoc.processedAt ? new Date().toISOString() : null,
    });

    if (!result.success) {
      throw new Error(result.error || 'Failed to store event');
    }
  }

  private async processBatch(): Promise<void> {
    if (this.eventBatch.length === 0) return;

    const batchToProcess = [...this.eventBatch];
    this.eventBatch = [];

    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
      this.batchTimeout = null;
    }

    try {
      // Process all events in the batch using centralized service
      const eventDocs = batchToProcess.map((event) => {
        const firebaseDoc = this.convertToFirebaseDocument(event);
        return {
          ...firebaseDoc,
          processedAt: firebaseDoc.processedAt ? new Date().toISOString() : null,
        };
      });

      const result = await firestoreService.createEventsBatch(eventDocs);

      if (result.success) {
        console.log(`FirebaseEventHandler: Processed batch of ${batchToProcess.length} events`);
      } else {
        throw new Error(result.error || 'Batch processing failed');
      }
    } catch (error) {
      console.error('FirebaseEventHandler: Batch processing failed:', error);
      // Re-add failed events to batch for retry
      this.eventBatch.unshift(...batchToProcess);
      throw error;
    }
  }

  private convertToFirebaseDocument(event: Event): Omit<FirebaseEventDocument, 'createdAt' | 'processedAt'> {
    const sanitizedData = this.sanitizeData(event.data);

    return {
      id: event.id,
      type: event.type,
      priority: event.priority,
      // Only include userId and userEmail if they have values
      ...(event.data.userId && { userId: event.data.userId }),
      ...(event.data.userEmail && { userEmail: event.data.userEmail }),
      data: sanitizedData,
      status: event.processedAt ? 'processed' : 'pending',
      retryCount: event.retryCount || 0,
      handlers: [this.name],
      errors: [],
    };
  }

  private sanitizeData(data: any): Record<string, any> {
    // Remove any undefined values and functions that can't be serialized to Firestore
    const sanitized: Record<string, any> = {};

    for (const [key, value] of Object.entries(data)) {
      if (value !== undefined && value !== null && typeof value !== 'function') {
        if (typeof value === 'object' && value !== null) {
          // Recursively sanitize nested objects
          const nestedSanitized = this.sanitizeData(value);
          // Only include if the nested object has properties
          if (Object.keys(nestedSanitized).length > 0) {
            sanitized[key] = nestedSanitized;
          }
        } else {
          sanitized[key] = value;
        }
      }
    }

    return sanitized;
  }

  // Analytics and querying methods
  public async getEventsByUser(userEmail: string, limitCount: number = 50): Promise<FirebaseEventDocument[]> {
    try {
      const eventsCollection = collection(db, this.collectionName);
      const q = query(
        eventsCollection,
        where('userEmail', '==', userEmail),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        ...doc.data(),
        id: doc.id,
      })) as FirebaseEventDocument[];
    } catch (error) {
      console.error('Error fetching events by user:', error);
      throw error;
    }
  }

  public async getEventsByType(eventType: EventType, limitCount: number = 50): Promise<FirebaseEventDocument[]> {
    try {
      const eventsCollection = collection(db, this.collectionName);
      const q = query(
        eventsCollection,
        where('type', '==', eventType),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        ...doc.data(),
        id: doc.id,
      })) as FirebaseEventDocument[];
    } catch (error) {
      console.error('Error fetching events by type:', error);
      throw error;
    }
  }

  public async getFailedEvents(limitCount: number = 50): Promise<FirebaseEventDocument[]> {
    try {
      const eventsCollection = collection(db, this.collectionName);
      const q = query(
        eventsCollection,
        where('status', '==', 'failed'),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        ...doc.data(),
        id: doc.id,
      })) as FirebaseEventDocument[];
    } catch (error) {
      console.error('Error fetching failed events:', error);
      throw error;
    }
  }

  public async updateEventStatus(eventId: string, status: 'pending' | 'processed' | 'failed' | 'retrying'): Promise<void> {
    try {
      const eventDoc = doc(db, this.collectionName, eventId);
      await updateDoc(eventDoc, {
        status,
        processedAt: status === 'processed' ? serverTimestamp() : null,
      });
    } catch (error) {
      console.error('Error updating event status:', error);
      throw error;
    }
  }

  // Cleanup method for old events
  public async cleanupOldEvents(daysOld: number = 30): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);
      
      const eventsCollection = collection(db, this.collectionName);
      const q = query(
        eventsCollection,
        where('createdAt', '<', Timestamp.fromDate(cutoffDate)),
        limit(100) // Process in batches to avoid timeout
      );
      
      const querySnapshot = await getDocs(q);
      let deletedCount = 0;
      
      // Note: In a production environment, you might want to use Firebase Functions
      // for bulk deletions to avoid client-side limitations
      for (const docSnapshot of querySnapshot.docs) {
        await docSnapshot.ref.delete();
        deletedCount++;
      }
      
      return deletedCount;
    } catch (error) {
      console.error('Error cleaning up old events:', error);
      throw error;
    }
  }

  // Force process any pending batched events
  public async flush(): Promise<void> {
    if (this.eventBatch.length > 0) {
      await this.processBatch();
    }
  }
}
