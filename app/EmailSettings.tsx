import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  Switch,
  TouchableOpacity,
  Alert,
  ScrollView,
  SafeAreaView,
} from "react-native";
import { useRouter } from "expo-router";

const EmailSettings: React.FC = () => {
  const router = useRouter();
  
  // Always-on email notifications (untogglable)
  const alwaysOnValue = true;
  
  // Togglable email notifications
  const [programAlerts, setProgramAlerts] = useState(true);
  const [marketingNotifications, setMarketingNotifications] = useState(false);
  const [chatRoomNotifications, setChatRoomNotifications] = useState(false);

  return (
    <SafeAreaView style={styles.safeArea}>
      <ScrollView contentContainerStyle={styles.container}>
        <Text style={styles.header}>Email Notifications</Text>

        <View style={styles.notificationItem}>
          <Text style={styles.label}>Critical Program Alerts</Text>
          <Switch value={alwaysOnValue} disabled />
        </View>
        <View style={styles.notificationItem}>
          <Text style={styles.label}>Reminders</Text>
          <Switch value={alwaysOnValue} disabled />
        </View>
        <View style={styles.notificationItem}>
          <Text style={styles.label}>Program Alerts</Text>
          <Switch value={programAlerts} onValueChange={setProgramAlerts} />
        </View>
        <View style={styles.notificationItem}>
          <Text style={styles.label}>Marketing Notifications</Text>
          <Switch
            value={marketingNotifications}
            onValueChange={setMarketingNotifications}
          />
        </View>
        <View style={styles.notificationItem}>
          <Text style={styles.label}>Chat Room Notifications</Text>
          <Switch
            value={chatRoomNotifications}
            onValueChange={setChatRoomNotifications}
          />
        </View>

        {/* Back Button */}
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Text style={styles.backButtonText}>Back</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

export default EmailSettings;

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: "#121212",
  },
  container: {
    flexGrow: 1,
    padding: 20,
    paddingBottom: 40, // extra bottom padding to ensure content doesn't overlap
    backgroundColor: "#121212",
  },
  header: {
    fontSize: 26,
    color: "#FFCC00",
    fontWeight: "bold",
    textAlign: "center",
    marginBottom: 20,
  },
  notificationItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "#1F1F1F",
    padding: 15,
    borderRadius: 10,
    marginBottom: 15,
  },
  label: {
    fontSize: 16,
    color: "white",
  },
  testButton: {
    marginTop: 15,
    alignSelf: "center",
    backgroundColor: "#FFEB3B",
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  testButtonText: {
    color: "black",
    fontSize: 16,
    fontWeight: "bold",
  },
  backButton: {
    marginTop: 30,
    alignSelf: "center",
    backgroundColor: "#2A2A2A",
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  backButtonText: {
    color: "#FFEB3B",
    fontSize: 16,
    fontWeight: "bold",
  },
});
