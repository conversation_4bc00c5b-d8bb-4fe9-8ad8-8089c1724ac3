//TO-DO: 
//1. Implement View profile
//2. Implement Points & Payouts
//3. Implement My Games
//4. Implement Support
//5. Imolement Email Settings

import React, { useEffect, useState, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from "react-native";
import { getId, getFname, getLname } from "../../utilis/variables";
import { useRouter } from "expo-router";
import { MaterialCommunityIcons, MaterialIcons } from "@expo/vector-icons";
import * as Notifications from "expo-notifications";
import { useAuth } from "@/contexts/AuthContext";
import { useTheme, ThemeMode } from "@/contexts/ThemeContext";
import { useFocusEffect } from "@react-navigation/native";
import { LinearGradient } from "expo-linear-gradient";
import Header from "@/components/Header";

function Settings() {
  const { signOut } = useAuth();
  const { themeMode, setThemeMode, colors, isDark } = useTheme();
  const [email, setEmail] = useState("");
  const [fname, setFname] = useState("");
  const [lname, setLname] = useState("");



  const router = useRouter();

  // Add caching for profile data
  const [isInitialized, setIsInitialized] = useState(false);
  const lastFetchTime = useRef<number>(0);
  const CACHE_DURATION = 10 * 60 * 1000; // 10 minutes cache for profile data

  const shouldFetchData = () => {
    const now = Date.now();
    return !isInitialized || (now - lastFetchTime.current) > CACHE_DURATION;
  };

  const fetchData = async () => {
    if (!shouldFetchData()) return;

    console.log('🔄 Profile: Fetching data...');
    lastFetchTime.current = Date.now();
      const fetchedEmail = String(await getId());
      setEmail(fetchedEmail);

      const fetchedfName = String(await getFname());
      setFname(fetchedfName);

      const fetchedLname = String(await getLname());
      setLname(fetchedLname);

      const { status } = await Notifications.getPermissionsAsync();
      if (status !== "granted") {
        const { status: newStatus } = await Notifications.requestPermissionsAsync();
        if (newStatus !== "granted") {
          console.log("Push notification permission not granted.");
        }
      }

      setIsInitialized(true);
    };

  // Initial fetch
  useEffect(() => {
    if (!isInitialized) {
      fetchData();
    }
  }, [isInitialized]);

  // Lazy loading on tab focus
  useFocusEffect(
    React.useCallback(() => {
      if (shouldFetchData()) {
        fetchData();
      }
    }, [])
  );

  const handleLogout = async () => {
    await signOut();
    // The AuthContext will automatically handle the redirect to SignIn page
  };

  // Instead of a modal, navigate to a separate ConfirmDelete screen
  const confirmDelete = () => {
    router.push({
      pathname: "/ConfirmDelete",
      params: { userEmail: email },
    });
  };

  // Example navigation placeholders for other items
  const handleNav = (destination: string) => {
    // Adjust as needed for your actual screens or logic
    router.push(destination as any);
  };

  const handleFAQPress = () => {
    // Navigate to FAQ screen which will show FAQ content in webview
    router.push("/FAQ");
  };

  // Theme selection handler
  const handleThemeSelection = () => {
    Alert.alert(
      "Choose Theme",
      "Select your preferred theme mode",
      [
        {
          text: "Light",
          onPress: () => setThemeMode('light'),
        },
        {
          text: "Dark",
          onPress: () => setThemeMode('dark'),
        },
        {
          text: "System",
          onPress: () => setThemeMode('system'),
        },
        {
          text: "Cancel",
          style: "cancel",
        },
      ]
    );
  };

  // Get theme mode display text
  const getThemeModeText = (mode: ThemeMode) => {
    switch (mode) {
      case 'light':
        return 'Light';
      case 'dark':
        return 'Dark';
      case 'system':
        return 'System';
      default:
        return 'System';
    }
  };



  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Header />
      <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
        {/* Royal Profile Header */}
        <LinearGradient
          colors={isDark ? ['#1a1a1a', '#2a2a2a', '#1f1f1f'] : ['#ffffff', '#f8f8f8', '#f5f5f5']}
          style={[styles.profileCard, { shadowColor: isDark ? "#000" : "#000" }]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.profileHeaderContent}>
            {/* Royal Avatar with Crown */}
            <View style={styles.avatarSection}>
              <LinearGradient
                colors={['#FFD700', '#FFC107', '#FF8F00']}
                style={styles.avatarGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <View style={styles.avatarInner}>
                  <MaterialCommunityIcons
                    name="account-circle"
                    size={50}
                    color="#000"
                  />
                </View>
              </LinearGradient>

              {/* Crown above avatar */}
              <View style={styles.crownContainer}>
                <MaterialCommunityIcons
                  name="crown"
                  size={18}
                  color={colors.primary}
                />
                {/* Crown sparkles */}
                <View style={styles.crownSparkles}>
                  <MaterialCommunityIcons name="star-four-points" size={8} color={colors.primary} style={styles.sparkleLeft} />
                  <MaterialCommunityIcons name="star-four-points" size={6} color={colors.primary} style={styles.sparkleRight} />
                </View>
              </View>
            </View>

            <View style={styles.profileInfo}>
              {/* Royal Name with Gradient Effect */}
              <LinearGradient
                colors={['#FFD700', '#FFEB3B', '#FFC107']}
                style={styles.nameGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                <Text style={styles.royalName}>{`${fname} ${lname}`}</Text>
              </LinearGradient>

              <Text style={[styles.email, { color: colors.textMuted }]}>{email}</Text>

              {/* Become Royal Member Button - Right below email */}
              <TouchableOpacity
                style={styles.royalMemberButton}
                onPress={() => {
                  // TODO: Navigate to subscription/upgrade page
                  console.log('Navigate to Royal Member upgrade');
                }}
                activeOpacity={0.8}
              >
                <LinearGradient
                  colors={['#FFD700', '#FFEB3B', '#FFC107']}
                  style={styles.royalMemberGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <Text style={styles.royalMemberText}>👑 Become a Royal Member</Text>
                </LinearGradient>
              </TouchableOpacity>
            </View>
          </View>
        </LinearGradient>

        {/* Account Actions */}
        <LinearGradient
          colors={isDark ? ['#1a1a1a', '#2a2a2a', '#1f1f1f'] : ['#ffffff', '#f8f8f8', '#f5f5f5']}
          style={[styles.actionCard, { shadowColor: isDark ? "#000" : "#000" }]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.sectionHeader}>
            <MaterialCommunityIcons name="account-star" size={20} color={colors.primary} />
            <Text style={[styles.sectionTitle, { color: colors.primary }]}>Account</Text>
          </View>

          <View style={[styles.actionItem, styles.disabledActionItem, { borderBottomColor: colors.border }]}>
            <View style={styles.actionLeft}>
              <LinearGradient
                colors={['#FFD700', '#FFEB3B', '#FFC107']}
                style={[styles.actionIconGradient, styles.blurredIcon]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <MaterialCommunityIcons name="account" size={18} color="#000" />
              </LinearGradient>
              <View style={styles.actionTextContainer}>
                <View style={styles.titleWithTag}>
                  <Text style={[styles.actionTitle, { color: colors.text }]}>View Profile</Text>
                  <View style={styles.comingSoonTag}>
                    <Text style={styles.comingSoonTagText}>Coming Soon</Text>
                  </View>
                </View>
                <Text style={[styles.actionSubtitle, styles.comingSoonSubtitle, { color: colors.textMuted }]}>Let others see your progress, winnings and badges</Text>
              </View>
            </View>
            <MaterialIcons name="chevron-right" size={20} color={colors.textMuted} style={styles.blurredChevron} />
          </View>

          <TouchableOpacity
            style={[styles.actionItem, { borderBottomColor: colors.border }]}
            onPress={() => handleNav("/PointsPayouts")}
          >
            <View style={styles.actionLeft}>
              <LinearGradient
                colors={['#FFD700', '#FFEB3B', '#FFC107']}
                style={styles.actionIconGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <MaterialCommunityIcons name="wallet" size={18} color="#000" />
              </LinearGradient>
              <View style={styles.actionTextContainer}>
                <Text style={[styles.actionTitle, { color: colors.text }]}>Points & Payouts</Text>
                <Text style={[styles.actionSubtitle, { color: colors.textMuted }]}>Track your earnings and rewards</Text>
              </View>
            </View>
            <MaterialIcons name="chevron-right" size={20} color={colors.primary} />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionItem, { borderBottomWidth: 0 }]}
            onPress={() => router.push("/MyPrograms")}
          >
            <View style={styles.actionLeft}>
              <LinearGradient
                colors={['#FFD700', '#FFEB3B', '#FFC107']}
                style={styles.actionIconGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <MaterialCommunityIcons name="view-list" size={18} color="#000" />
              </LinearGradient>
              <View style={styles.actionTextContainer}>
                <Text style={[styles.actionTitle, { color: colors.text }]}>My Programs</Text>
                <Text style={[styles.actionSubtitle, { color: colors.textMuted }]}>View your enrolled challenges</Text>
              </View>
            </View>
            <MaterialIcons name="chevron-right" size={20} color={colors.primary} />
          </TouchableOpacity>
        </LinearGradient>

        {/* Support */}
        <LinearGradient
          colors={isDark ? ['#1a1a1a', '#2a2a2a', '#1f1f1f'] : ['#ffffff', '#f8f8f8', '#f5f5f5']}
          style={[styles.settingsCard, { shadowColor: isDark ? "#000" : "#000" }]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.sectionHeader}>
            <MaterialCommunityIcons name="help-circle" size={20} color={colors.primary} />
            <Text style={[styles.sectionTitle, { color: colors.primary }]}>Support</Text>
          </View>

          <TouchableOpacity
            style={[styles.actionItem, { borderBottomColor: colors.border }]}
            onPress={() => handleNav("/Disputes")}
          >
            <View style={styles.actionLeft}>
              <LinearGradient
                colors={['#FFD700', '#FFEB3B', '#FFC107']}
                style={styles.actionIconGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <MaterialCommunityIcons name="gavel" size={18} color="#000" />
              </LinearGradient>
              <View style={styles.actionTextContainer}>
                <Text style={[styles.actionTitle, { color: colors.text }]}>Disputes</Text>
                <Text style={[styles.actionSubtitle, { color: colors.textMuted }]}>Report issues or disputes</Text>
              </View>
            </View>
            <MaterialIcons name="chevron-right" size={20} color={colors.primary} />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionItem, { borderBottomColor: colors.border }]}
            onPress={() => handleNav("/Support")}
          >
            <View style={styles.actionLeft}>
              <LinearGradient
                colors={['#FFD700', '#FFEB3B', '#FFC107']}
                style={styles.actionIconGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <MaterialCommunityIcons name="help-circle" size={18} color="#000" />
              </LinearGradient>
              <View style={styles.actionTextContainer}>
                <Text style={[styles.actionTitle, { color: colors.text }]}>Support</Text>
                <Text style={[styles.actionSubtitle, { color: colors.textMuted }]}>Get help and assistance</Text>
              </View>
            </View>
            <MaterialIcons name="chevron-right" size={20} color={colors.primary} />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionItem, { borderBottomWidth: 0 }]}
            onPress={() => handleFAQPress()}
          >
            <View style={styles.actionLeft}>
              <LinearGradient
                colors={['#FFD700', '#FFEB3B', '#FFC107']}
                style={styles.actionIconGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <MaterialCommunityIcons name="frequently-asked-questions" size={18} color="#000" />
              </LinearGradient>
              <View style={styles.actionTextContainer}>
                <Text style={[styles.actionTitle, { color: colors.text }]}>FAQs</Text>
                <Text style={[styles.actionSubtitle, { color: colors.textMuted }]}>Frequently asked questions</Text>
              </View>
            </View>
            <MaterialIcons name="chevron-right" size={20} color={colors.primary} />
          </TouchableOpacity>
        </LinearGradient>

        {/* Preferences */}
        <LinearGradient
          colors={isDark ? ['#1a1a1a', '#2a2a2a', '#1f1f1f'] : ['#ffffff', '#f8f8f8', '#f5f5f5']}
          style={[styles.settingsCard, { shadowColor: isDark ? "#000" : "#000" }]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.sectionHeader}>
            <MaterialCommunityIcons name="palette" size={20} color={colors.primary} />
            <Text style={[styles.sectionTitle, { color: colors.primary }]}>Preferences</Text>
          </View>

          <TouchableOpacity
            style={[styles.actionItem, { borderBottomWidth: 0 }]}
            onPress={handleThemeSelection}
          >
            <View style={styles.actionLeft}>
              <LinearGradient
                colors={['#FFD700', '#FFEB3B', '#FFC107']}
                style={styles.actionIconGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <MaterialCommunityIcons
                  name={isDark ? "weather-night" : "weather-sunny"}
                  size={18}
                  color="#000"
                />
              </LinearGradient>
              <View style={styles.actionTextContainer}>
                <Text style={[styles.actionTitle, { color: colors.text }]}>Theme</Text>
                <Text style={[styles.actionSubtitle, { color: colors.textMuted }]}>
                  Currently: {getThemeModeText(themeMode)}
                </Text>
              </View>
            </View>
            <MaterialIcons name="chevron-right" size={20} color={colors.primary} />
          </TouchableOpacity>
        </LinearGradient>

        {/* Account Actions */}
        <LinearGradient
          colors={isDark ? ['#1a1a1a', '#2a2a2a', '#1f1f1f'] : ['#ffffff', '#f8f8f8', '#f5f5f5']}
          style={[styles.settingsCard, { shadowColor: isDark ? "#000" : "#000" }]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.sectionHeader}>
            <MaterialCommunityIcons name="account-cog" size={20} color={colors.primary} />
            <Text style={[styles.sectionTitle, { color: colors.primary }]}>Account Actions</Text>
          </View>

          <TouchableOpacity
            style={[styles.actionItem, { borderBottomColor: colors.border }]}
            onPress={handleLogout}
          >
            <View style={styles.actionLeft}>
              <LinearGradient
                colors={['#1E88E5', '#42A5F5', '#64B5F6']}
                style={styles.actionIconGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <MaterialCommunityIcons name="logout" size={18} color="#fff" />
              </LinearGradient>
              <View style={styles.actionTextContainer}>
                <Text style={[styles.actionTitle, { color: colors.info }]}>Log Out</Text>
                <Text style={[styles.actionSubtitle, { color: colors.textMuted }]}>Sign out of your account</Text>
              </View>
            </View>
            <MaterialIcons name="chevron-right" size={20} color={colors.info} />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionItem, { borderBottomWidth: 0 }]}
            onPress={confirmDelete}
          >
            <View style={styles.actionLeft}>
              <LinearGradient
                colors={['#E53935', '#EF5350', '#F44336']}
                style={styles.actionIconGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <MaterialCommunityIcons name="delete" size={18} color="#fff" />
              </LinearGradient>
              <View style={styles.actionTextContainer}>
                <Text style={[styles.actionTitle, { color: colors.error }]}>Delete Account</Text>
                <Text style={[styles.actionSubtitle, { color: colors.textMuted }]}>Permanently remove your account</Text>
              </View>
            </View>
            <MaterialIcons name="chevron-right" size={20} color={colors.error} />
          </TouchableOpacity>
        </LinearGradient>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor will be set dynamically based on theme
  },
  scrollContent: {
    paddingBottom: 16,
    paddingHorizontal: 12,
  },
  profileCard: {
    marginTop: 12,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#FFD700',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.12,
    shadowRadius: 6,
    elevation: 4,
    marginBottom: 12,
  },
  profileHeaderContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  // Royal Avatar Styles
  avatarSection: {
    alignItems: 'center',
    marginRight: 16,
    position: 'relative',
  },
  avatarGradient: {
    width: 70,
    height: 70,
    borderRadius: 35,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: '#FFD700',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.25,
    shadowRadius: 6,
    elevation: 4,
  },
  avatarInner: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  crownContainer: {
    position: 'absolute',
    top: -12,
    alignItems: 'center',
    zIndex: 1,
  },
  crownSparkles: {
    position: 'absolute',
    width: 40,
    height: 20,
    top: 0,
  },
  sparkleLeft: {
    position: 'absolute',
    left: -8,
    top: 2,
  },
  sparkleRight: {
    position: 'absolute',
    right: -8,
    top: 4,
  },
  // Profile Info Styles
  profileInfo: {
    flex: 1,
  },
  nameGradient: {
    borderRadius: 6,
    paddingHorizontal: 10,
    paddingVertical: 4,
    marginBottom: 4,
    alignSelf: 'flex-start',
  },
  royalName: {
    fontSize: 18,
    fontFamily: "CasinoFlatShadowItalic",
    color: '#000',
    textAlign: 'center',
  },
  email: {
    fontSize: 12,
    fontFamily: "MontserratRegular",
    marginBottom: 4,
  },
  statusBadgeGradient: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: "flex-start",
    shadowColor: '#FFD700',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.15,
    shadowRadius: 3,
    elevation: 2,
  },
  royalStatusText: {
    fontSize: 11,
    fontFamily: "MontserratBold",
    color: '#000',
    textAlign: 'center',
  },
  // Royal Member Button Styles
  royalMemberButton: {
    marginTop: 6,
    alignSelf: 'flex-start',
  },
  royalMemberGradient: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    shadowColor: '#FFD700',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.15,
    shadowRadius: 2,
    elevation: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  royalMemberText: {
    fontSize: 10,
    fontFamily: "MontserratBold",
    color: '#000',
    textAlign: 'center',
  },
  // Section Header Styles
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 215, 0, 0.2)',
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: "CasinoFlatShadowItalic",
    marginLeft: 10,
    flex: 1,
  },
  // Card Styles
  actionCard: {
    borderRadius: 12,
    padding: 16,
    shadowColor: '#FFD700',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.08,
    shadowRadius: 6,
    elevation: 4,
    marginBottom: 12,
  },
  settingsCard: {
    borderRadius: 12,
    padding: 16,
    shadowColor: '#FFD700',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.08,
    shadowRadius: 6,
    elevation: 4,
    marginBottom: 12,
  },
  actionItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 12,
    borderBottomWidth: 1,
    // borderBottomColor will be set dynamically based on theme
  },
  actionLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  actionIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
    // backgroundColor will be set dynamically based on theme
  },
  actionIconGradient: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
    shadowColor: '#FFD700',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.15,
    shadowRadius: 3,
    elevation: 2,
  },
  actionTextContainer: {
    flex: 1,
  },
  actionTitle: {
    fontSize: 15,
    fontFamily: "MontserratBold",
    marginBottom: 1,
    // color will be set dynamically based on theme
  },
  actionSubtitle: {
    fontSize: 11,
    fontFamily: "MontserratRegular",
    // color will be set dynamically based on theme
  },
  // Coming Soon / Disabled Styles
  disabledActionItem: {
    opacity: 0.6,
  },
  blurredIcon: {
    opacity: 0.7,
  },
  blurredChevron: {
    opacity: 0.5,
  },
  comingSoonTitle: {
    fontStyle: 'italic',
    opacity: 0.8,
  },
  comingSoonSubtitle: {
    fontStyle: 'italic',
    opacity: 0.7,
  },
  // Title with Coming Soon Tag
  titleWithTag: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 1,
  },
  comingSoonTag: {
    backgroundColor: '#FFD700',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    marginLeft: 8,
    shadowColor: '#FFD700',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 2,
  },
  comingSoonTagText: {
    fontSize: 8,
    fontFamily: "MontserratBold",
    color: '#000',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
});

export default Settings;
