import React from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  Dimensions,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { useTheme } from "@/contexts/ThemeContext";
import BreathingDot from "./BreathingDot";
import { Commit } from "@/services/database/types";

// Get screen height for dynamic dropdown sizing
const { height: SCREEN_HEIGHT } = Dimensions.get('window');

interface Program {
  id: string;
  name?: string;
  setupStatus?: boolean | null;
}

// Using imported Commit type from database

interface Props {
  programs: Program[];
  commits?: Commit[];
  selectedProgram: Program | null;
  selectedCommit?: Commit | null;
  isOpen: boolean;
  onToggle: () => void;
  onSelect: (program: Program) => void;
  onSelectCommit?: (commit: Commit) => void;
}

const ProgramDropdown: React.FC<Props> = ({
  programs,
  commits = [],
  selectedProgram,
  selectedCommit,
  isOpen,
  onToggle,
  onSelect,
  onSelectCommit,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  console.log('🔍 DEBUG: Dropdown received programs:', programs.map(p => ({
    id: p.id,
    name: p.name,
    status: p.status
  })));

  console.log('🔍 DEBUG: Dropdown received commits:', commits.map(c => ({
    id: c.id,
    title: c.title,
    status: c.status
  })));

  const hasAnyPendingSetup = programs.some((p) => p.setupStatus === false);
  const hasItems = programs.length > 0 || commits.length > 0;

  return (
    <>
      {isOpen && <View style={styles.overlay} />}
      <View style={styles.container}>
        <TouchableOpacity style={styles.dropdownButton} onPress={onToggle}>
          <Text style={styles.dropdownButtonText} numberOfLines={1}>
            {selectedProgram
              ? selectedProgram.name || `Program ID: ${selectedProgram.id}`
              : selectedCommit
                ? selectedCommit.title || 'Untitled Commit'
                : "Select Program or Commit"}
          </Text>
          {hasItems && (
            <View style={styles.rightSection}>
              <MaterialIcons
                name={isOpen ? "keyboard-arrow-up" : "keyboard-arrow-down"}
                size={24}
                color={colors.text}
              />
              {hasAnyPendingSetup && <BreathingDot />}
            </View>
          )}
        </TouchableOpacity>

        {isOpen && (
          <View style={styles.dropdownMenuContainer}>
            <View style={styles.dropdownMenu}>
              <View style={styles.dropdownMenuContent}>
                {/* Programs Section */}
                {programs.length > 0 && (
                  <>
                    <View style={styles.sectionHeader}>
                      <Text style={styles.sectionHeaderText}>Programs</Text>
                    </View>
                    {programs.map((item) => (
                      <TouchableOpacity
                        key={`program-${item.id}`}
                        style={styles.dropdownItem}
                        onPress={() => onSelect(item)}
                      >
                        <View style={styles.dropdownItemContent}>
                          <Text style={styles.dropdownItemText}>
                            {item.name || `Program ID: ${item.id}`}
                          </Text>
                          {item.setupStatus === false && <BreathingDot />}
                        </View>
                      </TouchableOpacity>
                    ))}
                  </>
                )}

                {/* Commits Section */}
                {commits.length > 0 && (
                  <>
                    {programs.length > 0 && <View style={styles.sectionDivider} />}
                    <View style={styles.sectionHeader}>
                      <Text style={styles.sectionHeaderText}>Commits</Text>
                    </View>
                    {commits.map((item) => (
                      <TouchableOpacity
                        key={`commit-${item.id}`}
                        style={styles.dropdownItem}
                        onPress={() => onSelectCommit?.(item)}
                      >
                        <View style={styles.dropdownItemContent}>
                          <Text style={styles.dropdownItemText}>
                            {item.title || 'Untitled Commit'}
                          </Text>
                          <View style={[styles.statusBadge, {
                            backgroundColor: item.status === 'active' ? '#4CAF50' :
                                           item.status === 'completed' ? '#2196F3' :
                                           item.status === 'failed' ? '#F44336' : '#9E9E9E'
                          }]}>
                            <Text style={styles.statusBadgeText}>
                              {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                            </Text>
                          </View>
                        </View>
                      </TouchableOpacity>
                    ))}
                  </>
                )}
              </View>
            </View>
          </View>
        )}
      </View>
    </>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 5,
  },
  container: {
    marginBottom: 5,
    marginHorizontal: 10,
    position: "relative",
    marginTop: 10,
    zIndex: 6,
  },
  dropdownButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.surface,
    padding: 15,
    borderRadius: 10,
    overflow: "hidden",
  },
  dropdownButtonText: {
    flex: 1,
    textAlign: "center",
    color: colors.primary,
    fontFamily: "MontserratBold",
    fontSize: 18,
  },
  rightSection: {
    flexDirection: "row",
    alignItems: "center",
  },
  dropdownMenuContainer: {
    position: "absolute",
    top: "100%",
    left: 0,
    right: 0,
    maxHeight: SCREEN_HEIGHT * 0.3, // 30% of screen height
    zIndex: 10,
  },
  dropdownMenu: {
    backgroundColor: colors.card,
    borderRadius: 10,
    maxHeight: "100%",
  },
  dropdownMenuContent: {
    flexGrow: 1,
  },
  dropdownItem: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  dropdownItemText: {
    color: colors.text,
    fontSize: 14,
    fontFamily: "MontserratBold",
  },
  dropdownItemContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  sectionHeader: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    backgroundColor: colors.background,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  sectionHeaderText: {
    color: colors.textMuted,
    fontSize: 12,
    fontFamily: "MontserratBold",
    textTransform: "uppercase",
    letterSpacing: 0.5,
  },
  sectionDivider: {
    height: 1,
    backgroundColor: colors.border,
    marginVertical: 4,
  },
  statusBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    minWidth: 50,
    alignItems: 'center',
  },
  statusBadgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontFamily: 'MontserratMedium',
  },
});

export default ProgramDropdown;
