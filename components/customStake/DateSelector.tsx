import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { CalendarDay } from '@/types/customStake';

interface DateSelectorProps {
  visible: boolean;
  onClose: () => void;
  onDateSelect: (date: Date) => void;
  title: string;
  minDate?: Date;
  maxDate?: Date;
  selectedDate?: Date;
}

export const DateSelector: React.FC<DateSelectorProps> = ({
  visible,
  onClose,
  onDateSelect,
  title,
  minDate,
  maxDate,
  selectedDate,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  
  const [calendarDate, setCalendarDate] = useState(new Date());
  const [tempSelectedDate, setTempSelectedDate] = useState<Date | null>(selectedDate || null);

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const generateCalendarDays = useMemo(() => {
    const year = calendarDate.getFullYear();
    const month = calendarDate.getMonth();
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // First day of the month and how many days in the month
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days: CalendarDay[] = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push({
        day: '',
        isCurrentMonth: false,
        isSelected: false,
        isToday: false,
        isDisabled: false,
      });
    }

    // Add all days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const currentDate = new Date(year, month, day);
      currentDate.setHours(0, 0, 0, 0);

      const isSelected = tempSelectedDate &&
                        tempSelectedDate.getDate() === day &&
                        tempSelectedDate.getMonth() === month &&
                        tempSelectedDate.getFullYear() === year;

      // Check if date is disabled based on restrictions
      let isDisabled = false;
      if (minDate && maxDate) {
        isDisabled = currentDate < minDate || currentDate > maxDate;
      }

      days.push({
        day: day.toString(),
        isCurrentMonth: true,
        isSelected: isSelected,
        isToday: currentDate.toDateString() === today.toDateString(),
        isDisabled: isDisabled,
      });
    }

    return days;
  }, [calendarDate, tempSelectedDate, minDate, maxDate]);

  const handleDatePress = (day: CalendarDay) => {
    if (day.isCurrentMonth && !day.isDisabled) {
      const newDate = new Date(calendarDate);
      newDate.setDate(parseInt(day.day));
      setTempSelectedDate(newDate);
    }
  };

  const handleConfirm = () => {
    if (tempSelectedDate) {
      onDateSelect(tempSelectedDate);
      onClose();
    }
  };

  const handleCancel = () => {
    setTempSelectedDate(selectedDate || null);
    setCalendarDate(new Date());
    onClose();
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(calendarDate);
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCalendarDate(newDate);
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={handleCancel}
    >
      <KeyboardAvoidingView
        style={styles.modalOverlay}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.datePickerModal}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>{title}</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={handleCancel}
              accessibilityLabel="Close date picker"
              accessibilityRole="button"
            >
              <MaterialIcons name="close" size={24} color={colors.text} />
            </TouchableOpacity>
          </View>

          <ScrollView
            style={styles.calendarScrollView}
            showsVerticalScrollIndicator={Platform.OS === 'web'}
          >
            <View style={styles.calendarContainer}>
              <View style={styles.calendarHeader}>
                <TouchableOpacity
                  style={styles.calendarNavButton}
                  onPress={() => navigateMonth('prev')}
                  accessibilityLabel="Previous month"
                  accessibilityRole="button"
                >
                  <MaterialIcons name="chevron-left" size={24} color={colors.text} />
                </TouchableOpacity>

                <Text style={styles.calendarTitle}>
                  {calendarDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                </Text>

                <TouchableOpacity
                  style={styles.calendarNavButton}
                  onPress={() => navigateMonth('next')}
                  accessibilityLabel="Next month"
                  accessibilityRole="button"
                >
                  <MaterialIcons name="chevron-right" size={24} color={colors.text} />
                </TouchableOpacity>
              </View>

              <View style={styles.calendarGrid}>
                {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                  <Text key={day} style={styles.calendarDayHeader}>{day}</Text>
                ))}
                {generateCalendarDays.map((day, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.calendarDay,
                      day.isCurrentMonth && styles.calendarDayActive,
                      day.isSelected && styles.calendarDaySelected,
                      day.isToday && styles.calendarDayToday,
                      day.isDisabled && styles.calendarDayDisabled,
                    ]}
                    onPress={() => handleDatePress(day)}
                    disabled={!day.isCurrentMonth || day.isDisabled}
                    accessibilityLabel={day.isCurrentMonth ? `Select ${day.day}` : undefined}
                    accessibilityRole={day.isCurrentMonth ? "button" : undefined}
                  >
                    <Text
                      style={[
                        styles.calendarDayText,
                        day.isCurrentMonth && styles.calendarDayTextActive,
                        day.isSelected && styles.calendarDayTextSelected,
                        day.isToday && styles.calendarDayTextToday,
                        day.isDisabled && styles.calendarDayTextDisabled,
                      ]}
                    >
                      {day.day}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View style={styles.selectedDateContainer}>
              {tempSelectedDate ? (
                <Text style={styles.selectedDateText}>
                  Selected: {formatDate(tempSelectedDate)}
                </Text>
              ) : (
                <Text style={styles.noDateSelectedText}>
                  No date selected
                </Text>
              )}
            </View>
          </ScrollView>

          <View style={styles.datePickerButtons}>
            <TouchableOpacity
              style={styles.datePickerButton}
              onPress={handleCancel}
              accessibilityLabel="Cancel date selection"
              accessibilityRole="button"
            >
              <Text style={styles.datePickerButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.datePickerButton,
                styles.datePickerButtonPrimary,
                !tempSelectedDate && styles.datePickerButtonDisabled
              ]}
              onPress={handleConfirm}
              disabled={!tempSelectedDate}
              accessibilityLabel="Confirm date selection"
              accessibilityRole="button"
            >
              <Text style={[
                styles.datePickerButtonText,
                styles.datePickerButtonTextPrimary,
                !tempSelectedDate && styles.datePickerButtonTextDisabled
              ]}>
                Confirm
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

interface DateButtonProps {
  selectedDate?: Date;
  onPress: () => void;
  placeholder: string;
}

export const DateButton: React.FC<DateButtonProps> = ({
  selectedDate,
  onPress,
  placeholder,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <TouchableOpacity
      style={styles.blankButton}
      onPress={onPress}
      accessibilityLabel={selectedDate ? `Selected date: ${formatDate(selectedDate)}` : placeholder}
      accessibilityRole="button"
      accessibilityHint="Tap to select date"
    >
      <Text style={[styles.blankText, !selectedDate && styles.placeholderText]}>
        {selectedDate ? formatDate(selectedDate) : placeholder}
      </Text>
      <MaterialIcons name="calendar-today" size={16} color="#000000" />
    </TouchableOpacity>
  );
};

interface ThemeColors {
  surface: string;
  text: string;
  textMuted: string;
  primary: string;
  background: string;
  border: string;
}

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  datePickerModal: {
    backgroundColor: colors.surface,
    borderRadius: 20,
    padding: 20,
    width: Platform.OS === 'web' ? 400 : '90%',
    maxHeight: Platform.OS === 'web' ? 600 : '80%',
    maxWidth: Platform.OS === 'web' ? 400 : undefined,
  },
  calendarScrollView: {
    maxHeight: Platform.OS === 'web' ? 400 : undefined,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontFamily: 'MontserratBold',
    color: colors.text,
    flex: 1,
    textAlign: 'center',
  },
  closeButton: {
    padding: 4,
    borderRadius: 20,
    backgroundColor: colors.surface,
  },
  calendarContainer: {
    marginTop: 20,
    marginBottom: 10,
  },
  calendarHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 15,
    paddingHorizontal: 10,
  },
  calendarNavButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
  },
  calendarTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: colors.text,
    textAlign: 'center',
    flex: 1,
  },
  calendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  calendarDayHeader: {
    width: '14.28%',
    textAlign: 'center',
    fontSize: 12,
    fontFamily: 'MontserratBold',
    color: colors.textMuted,
    paddingVertical: 8,
  },
  calendarDay: {
    width: '14.28%',
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    marginVertical: 2,
  },
  calendarDayActive: {
    backgroundColor: 'transparent',
  },
  calendarDaySelected: {
    backgroundColor: colors.primary,
  },
  calendarDayToday: {
    borderWidth: 2,
    borderColor: colors.primary,
  },
  calendarDayText: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
  },
  calendarDayTextActive: {
    color: colors.text,
  },
  calendarDayTextSelected: {
    color: '#000',
    fontFamily: 'MontserratBold',
  },
  calendarDayTextToday: {
    color: colors.primary,
    fontFamily: 'MontserratBold',
  },
  calendarDayDisabled: {
    opacity: 0.3,
    backgroundColor: colors.border,
  },
  calendarDayTextDisabled: {
    color: colors.textMuted,
    opacity: 0.5,
  },
  selectedDateContainer: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 12,
    marginTop: 10,
    marginBottom: 5,
    borderWidth: 1,
    borderColor: colors.border,
    alignItems: 'center',
  },
  selectedDateText: {
    fontSize: 16,
    fontFamily: 'MontserratMedium',
    color: colors.text,
  },
  noDateSelectedText: {
    fontSize: 16,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
    fontStyle: 'italic',
  },
  datePickerButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 15,
    gap: 15,
  },
  datePickerButton: {
    flex: 1,
    backgroundColor: colors.border,
    borderRadius: 10,
    paddingVertical: 12,
    alignItems: 'center',
  },
  datePickerButtonPrimary: {
    backgroundColor: colors.primary,
  },
  datePickerButtonDisabled: {
    opacity: 0.5,
  },
  datePickerButtonText: {
    fontSize: 16,
    fontFamily: 'MontserratMedium',
    color: colors.text,
  },
  datePickerButtonTextPrimary: {
    color: '#000',
  },
  datePickerButtonTextDisabled: {
    opacity: 0.5,
  },
  blankButton: {
    backgroundColor: '#FFEB3B',
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: '#FFEB3B',
    marginHorizontal: 4,
    marginVertical: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 80,
  },
  blankText: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    color: '#000000',
    textAlign: 'center',
    marginRight: 4,
  },
  placeholderText: {
    color: '#000000',
  },
});
