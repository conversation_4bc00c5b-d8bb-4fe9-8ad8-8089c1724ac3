import { STRIPE_CONFIG, API_CONFIG, validateStripeConfig, validateAPIConfig } from '@/config/payments';
import { mockCreatePaymentIntent, mockConfirmPayment, logPaymentEvent } from './mockPaymentAPI';

// Payment service interface
export interface PaymentResult {
  success: boolean;
  paymentIntentId?: string;
  error?: string;
}

export interface PaymentData {
  amount: number; // Amount in cents
  currency: string;
  description: string;
  programId: string;
  userId: string;
}

// Web-specific Stripe implementation
let stripePromise: any = null;

const initializeStripeWeb = async () => {
  // Validate configuration before initializing
  if (!validateStripeConfig()) {
    throw new Error('Stripe configuration is invalid. Please check your publishable key.');
  }

  // Web implementation using @stripe/stripe-js
  const { loadStripe } = await import('@stripe/stripe-js');
  if (!stripePromise) {
    stripePromise = loadStripe(STRIPE_CONFIG.PUBLISHABLE_KEY);
  }
  return stripePromise;
};

// Create payment intent (shared between platforms)
const createPaymentIntent = async (paymentData: PaymentData) => {
  // Validate API configuration
  if (!validateAPIConfig()) {
    throw new Error('API configuration is invalid. Please check your backend URL.');
  }

  // In development mode, use mock API
  if (__DEV__) {
    logPaymentEvent('Using mock payment intent creation', { amount: paymentData.amount });
    return await mockCreatePaymentIntent(paymentData);
  }

  // Real API call for production
  const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.CREATE_PAYMENT_INTENT}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(paymentData),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data = await response.json();
  logPaymentEvent('Payment intent created successfully', { paymentIntentId: data.id });

  return {
    clientSecret: data.client_secret,
    paymentIntentId: data.id,
  };
};

// Process Stripe payment for web
const processStripePaymentWeb = async (paymentData: PaymentData): Promise<PaymentResult> => {
  try {
    logPaymentEvent('Starting web Stripe payment', { amount: paymentData.amount });

    const stripe = await initializeStripeWeb();

    if (!stripe) {
      throw new Error('Stripe failed to initialize');
    }

    // Create payment intent
    const { clientSecret, paymentIntentId } = await createPaymentIntent(paymentData);

    // In development mode with mock API, simulate payment success
    if (__DEV__ && clientSecret.includes('mock')) {
      logPaymentEvent('Using mock payment confirmation for web', { paymentIntentId });

      const mockResult = await mockConfirmPayment(paymentIntentId, 'card');

      if (mockResult.status === 'succeeded') {
        return {
          success: true,
          paymentIntentId: mockResult.id,
        };
      } else {
        return {
          success: false,
          error: 'Mock payment failed for testing',
        };
      }
    }

    // For real implementation, you would need to collect card details from a Stripe Elements form
    // This is a simplified version for demonstration
    console.warn('Web payment requires Stripe Elements integration. Please implement card collection form.');

    return {
      success: false,
      error: 'Web payment integration not fully implemented. Please use mobile app for payments.',
    };
  } catch (error) {
    logPaymentEvent('Web payment error', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
};

// Main payment processing function for web
export const processStripePayment = async (paymentData: PaymentData): Promise<PaymentResult> => {
  return processStripePaymentWeb(paymentData);
};

// Google Pay not supported on web
export const processGooglePayPayment = async (paymentData: PaymentData): Promise<PaymentResult> => {
  return {
    success: false,
    error: 'Google Pay is not supported on web platform',
  };
};

// Apple Pay not supported on web
export const processApplePayPayment = async (paymentData: PaymentData): Promise<PaymentResult> => {
  return {
    success: false,
    error: 'Apple Pay is not supported on web platform',
  };
};
