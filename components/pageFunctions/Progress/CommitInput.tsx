import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';
import { VerificationInput } from '@/components/verification/VerificationInput';
import { getCommitVerificationConfig } from '@/types/verification';
import { firestoreService } from '@/services/database';
import CommitSetup from './CommitSetup';
import type {
  VerificationData,
  VerificationResult
} from '@/types/verification';
import { Commit } from '@/services/database/types';
import { WeeklyProgressIndicator } from './WeeklyProgressIndicator';

interface CommitInputProps {
  commit: Commit;
  userId: string;
  onSubmissionSuccess: () => void;
  onCommitUpdate?: (updatedCommit: Commit) => void;
  isLoading?: boolean;
  refreshTrigger?: number;
  selectedDate?: string; // Optional selected date in YYYY-MM-DD format, defaults to today
}

const CommitInput: React.FC<CommitInputProps> = ({
  commit,
  userId,
  onSubmissionSuccess,
  onCommitUpdate,
  isLoading = false,
  refreshTrigger = 0,
  selectedDate,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  // State for setup and verification
  const [currentCommit, setCurrentCommit] = useState<Commit>(commit);
  const [initialVerificationData, setInitialVerificationData] = useState<VerificationData | null>(null);
  const [checkingExistingSubmission, setCheckingExistingSubmission] = useState<boolean>(true);
  const [isVerificationValid, setIsVerificationValid] = useState<boolean>(false);
  const [currentDaySubmission, setCurrentDaySubmission] = useState<string>('');
  const [submissionStatus, setSubmissionStatus] = useState<'upcoming' | 'submitted' | 'verified'>('upcoming');

  // Weekly mode specific state
  const [weeklyStatus, setWeeklyStatus] = useState<{
    submittedCount: number;
    pendingCount: number;
    canSubmit: boolean;
    canSubmitToday: boolean;
    nextSubmissionId: string | null;
    todaySubmissionExists: boolean;
  } | null>(null);

  // Update current commit when prop changes
  useEffect(() => {
    setCurrentCommit(commit);
  }, [commit]);

  // Calculate current day for the commit based on selected date or today
  const getCurrentDay = (): string => {
    const startDate = new Date(commit.schedule.startDate);
    // Use selected date if provided, otherwise use today
    const targetDate = selectedDate ? new Date(selectedDate) : new Date();
    targetDate.setHours(0, 0, 0, 0);
    startDate.setHours(0, 0, 0, 0);

    if (commit.schedule.frequency === 'once') {
      return 'Day 1';
    } else if (commit.schedule.frequency === 'daily') {
      const daysDiff = Math.floor((targetDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
      return `Day ${Math.max(1, daysDiff + 1)}`;
    } else if (commit.schedule.frequency === 'weekly') {
      const weeksDiff = Math.floor((targetDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 7));
      return `Week ${Math.max(1, weeksDiff + 1)}`;
    } else if (commit.schedule.frequency === 'monthly') {
      const monthsDiff = Math.floor((targetDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 30));
      return `Month ${Math.max(1, monthsDiff + 1)}`;
    }
    return 'Day 1';
  };



  // Handle setup completion
  const handleSetupComplete = (updatedCommit: Commit) => {
    setCurrentCommit(updatedCommit);
    onCommitUpdate?.(updatedCommit);
  };

  // Check for existing submission on mount and when dependencies change
  useEffect(() => {
    const checkExistingSubmission = async () => {
      if (!currentCommit.id || !userId) {
        setCheckingExistingSubmission(false);
        return;
      }

      setCheckingExistingSubmission(true);
      const daySubmission = getCurrentDay();
      setCurrentDaySubmission(daySubmission);

      try {
        if (currentCommit.schedule.frequency === 'weekly') {
          // Handle weekly mode with multiple submissions per week
          const timesPerWeek = currentCommit.schedule.timesPerWeek || 1;
          // Pass the target date (selected date or today) to check submissions for that specific date
          const targetDate = selectedDate || new Date().toISOString().split('T')[0];
          const weeklyStatusResult = await firestoreService.commits.getWeeklySubmissionStatus(
            currentCommit.id,
            daySubmission,
            timesPerWeek,
            targetDate
          );

          if (weeklyStatusResult.success && weeklyStatusResult.data) {
            const status = weeklyStatusResult.data;
            setWeeklyStatus(status);

            // Check submission status based on daily and weekly limits
            if (!status.canSubmit) {
              // All weekly submissions are done
              setSubmissionStatus('submitted');
            } else if (status.todaySubmissionExists) {
              // Already submitted today, but can submit more this week
              setSubmissionStatus('submitted');
            } else {
              // Can submit today
              setSubmissionStatus('upcoming');
            }

            // Set initial data only from submissions made on the target date
            const targetDate = selectedDate || new Date().toISOString().split('T')[0];
            const targetDateSubmission = status.submissions
              .filter(s => s.status === 'submitted' && s.timestamp && s.timestamp.split('T')[0] === targetDate)
              .sort((a, b) => new Date(b.timestamp || '').getTime() - new Date(a.timestamp || '').getTime())[0];

            if (targetDateSubmission && targetDateSubmission.attachment) {
              try {
                const parsedData = JSON.parse(targetDateSubmission.attachment);
                setInitialVerificationData(parsedData);
              } catch (e) {
                console.warn('Failed to parse existing submission data:', e);
              }
            } else {
              // Clear initial data if no submission exists for the target date
              setInitialVerificationData(null);
            }
          } else {
            setSubmissionStatus('upcoming');
          }
        } else {
          // Handle daily, monthly, and once modes
          const existingSubmission = await firestoreService.commits.getSubmissionDetails(
            currentCommit.id,
            currentCommit.schedule.frequency,
            daySubmission,
            'Submission 1' // Default to first submission for monthly
          );

          if (existingSubmission.success && existingSubmission.data) {
            const submissionData = existingSubmission.data;

            if (submissionData.status === 'submitted' && submissionData.attachment) {
              try {
                const verificationData = JSON.parse(submissionData.attachment);
                setInitialVerificationData(verificationData);
                setSubmissionStatus('submitted');
              } catch (error) {
                console.error('Error parsing verification data:', error);
                setInitialVerificationData(null);
                setSubmissionStatus('upcoming');
              }
            } else {
              setSubmissionStatus('upcoming');
            }
          } else {
            setSubmissionStatus('upcoming');
          }
        }
      } catch (error) {
        console.error('Error checking existing submission:', error);
      } finally {
        setCheckingExistingSubmission(false);
      }
    };

    checkExistingSubmission();
  }, [currentCommit.id, userId, refreshTrigger, selectedDate]);

  // Handle verification submission
  const handleVerificationSubmit = async (data: VerificationData): Promise<VerificationResult> => {
    if (!currentCommit.evidence?.type) {
      return { success: false, error: "Evidence type is required" };
    }

    try {
      const serializedData = JSON.stringify(data);
      let submissionId = 'Submission 1'; // Default for non-weekly modes

      // For weekly mode, use the next available submission slot
      if (currentCommit.schedule.frequency === 'weekly' && weeklyStatus?.nextSubmissionId) {
        // Double-check that user can submit today
        if (!weeklyStatus.canSubmitToday) {
          return {
            success: false,
            error: weeklyStatus.todaySubmissionExists
              ? "You have already submitted once today. You can submit again tomorrow."
              : "You have completed all submissions for this week."
          };
        }
        submissionId = weeklyStatus.nextSubmissionId;
      }

      // Update commit submission using the helper method
      const result = await firestoreService.commits.updateSubmissionDetails(
        currentCommit.id!,
        currentCommit.schedule.frequency,
        currentDaySubmission,
        {
          attachment: serializedData,
          status: 'submitted',
          timestamp: new Date().toISOString()
        },
        submissionId
      );

      if (!result.success) {
        return { success: false, error: result.error || "Failed to update submission" };
      }

      // Update UI state
      if (currentCommit.schedule.frequency === 'weekly') {
        // Refresh weekly status after submission
        const timesPerWeek = currentCommit.schedule.timesPerWeek || 1;
        // Pass the target date (selected date or today) to check submissions for that specific date
        const targetDate = selectedDate || new Date().toISOString().split('T')[0];
        const weeklyStatusResult = await firestoreService.commits.getWeeklySubmissionStatus(
          currentCommit.id!,
          currentDaySubmission,
          timesPerWeek,
          targetDate
        );

        if (weeklyStatusResult.success && weeklyStatusResult.data) {
          const updatedStatus = weeklyStatusResult.data;
          setWeeklyStatus(updatedStatus);

          // Update submission status based on daily and weekly limits
          if (!updatedStatus.canSubmit) {
            setSubmissionStatus('submitted'); // All weekly submissions are done
            setInitialVerificationData(data); // Keep the data for preview when all done
          } else if (updatedStatus.todaySubmissionExists) {
            setSubmissionStatus('submitted'); // Already submitted today
            setInitialVerificationData(null); // Clear data since they can submit again tomorrow
          } else {
            setSubmissionStatus('upcoming'); // Can still submit today
            setInitialVerificationData(null); // Clear data so user can submit again
          }
        }
      } else {
        setSubmissionStatus('submitted');
        setInitialVerificationData(data);
      }
      onSubmissionSuccess();

      return { success: true, data };
    } catch (error) {
      console.error("Error during verification submission:", error);
      return { success: false, error: "An error occurred during submission" };
    }
  };

  // Get verification config based on commit evidence type
  const getVerificationConfig = () => {
    const evidenceType = currentCommit.evidence?.type || 'photo';
    return getCommitVerificationConfig(evidenceType);
  };

  const getSubmissionTitle = () => {
    // For weekly mode, show contextual titles based on status
    if (currentCommit.schedule.frequency === 'weekly' && weeklyStatus) {
      if (!weeklyStatus.canSubmit) {
        return 'Weekly Challenge Complete! 🏆';
      } else if (weeklyStatus.todaySubmissionExists) {
        return 'Submission Recorded';
      } else if (weeklyStatus.canSubmitToday) {
        return 'Ready to Submit';
      } else {
        return 'Submission Status';
      }
    }

    // For other modes, show evidence-specific titles
    const evidenceType = currentCommit.evidence?.type || 'photo';

    // Handle all evidence types with a more flexible approach using string matching
    const evidenceTypeStr = evidenceType as string;

    if (evidenceTypeStr === 'photo') {
      return 'Submit your Photo Evidence';
    } else if (evidenceTypeStr === 'video' || evidenceTypeStr.includes('video')) {
      return 'Submit your Video Evidence';
    } else if (evidenceTypeStr.includes('camera')) {
      return 'Submit your Camera Evidence';
    } else if (evidenceTypeStr === 'gps-checkin') {
      return 'Check in at Location';
    } else if (evidenceTypeStr === 'gps-avoid') {
      return 'Confirm Location Avoidance';
    } else if (evidenceTypeStr === 'strava') {
      return 'Submit your Strava Activity';
    } else if (evidenceTypeStr === 'github') {
      return 'GitHub Commit Verification';
    } else if (evidenceTypeStr === 'screen-time') {
      return 'Submit your Screen Time Data';
    } else if (evidenceTypeStr === 'honor') {
      return 'Submit your Honor System Evidence';
    } else {
      return 'Submit your Evidence';
    }
  };



  // Helper function to get ordinal numbers (1st, 2nd, 3rd, 4th, etc.)
  const getOrdinalNumber = (num: number): string => {
    const suffix = ['th', 'st', 'nd', 'rd'];
    const value = num % 100;
    return num + (suffix[(value - 20) % 10] || suffix[value] || suffix[0]);
  };

  // Render submitted status UI
  const renderSubmittedStatus = () => (
    <View style={styles.statusContainer}>
      <View style={styles.statusHeader}>
        <MaterialIcons name="check-circle" size={24} color={colors.primary} />
        <Text style={styles.statusHeaderTitle}>Great job! 🎉</Text>
      </View>
      <Text style={styles.statusSubtitle}>
        {currentCommit.schedule.frequency === 'weekly' && weeklyStatus
          ? !weeklyStatus.canSubmit
            ? `Perfect! All submissions completed this week 🏆`
            : `${getOrdinalNumber(weeklyStatus.submittedCount)} submission recorded! 🎯`
          : `Submission completed successfully`
        }
      </Text>
      <Text style={styles.statusDescription}>
        Your evidence has been submitted successfully. The Habit Royale Team will review your progress.
      </Text>

      {/* Show submitted evidence preview */}
      {initialVerificationData && (
        <View style={styles.evidencePreview}>
          <Text style={styles.evidencePreviewTitle}>Submitted Evidence:</Text>
          {initialVerificationData.images && initialVerificationData.images.length > 0 && (
            <Text style={styles.evidencePreviewText}>
              📸 {initialVerificationData.images.length} photo{initialVerificationData.images.length > 1 ? 's' : ''} submitted
            </Text>
          )}
          {initialVerificationData.location && (
            <Text style={styles.evidencePreviewText}>
              📍 Location verified
            </Text>
          )}
          {initialVerificationData.textValue && (
            <Text style={styles.evidencePreviewText}>
              📝 Text submission: {initialVerificationData.textValue.substring(0, 50)}...
            </Text>
          )}
        </View>
      )}
    </View>
  );

  // Render verified status UI
  const renderVerifiedStatus = () => (
    <View style={styles.statusContainer}>
      <View style={styles.statusHeader}>
        <MaterialIcons name="verified" size={24} color={colors.success || '#4CAF50'} />
        <Text style={[styles.statusHeaderTitle, { color: colors.success || '#4CAF50' }]}>
          Verified! ✅
        </Text>
      </View>
      <Text style={styles.statusSubtitle}>
        Submission verified for {currentDaySubmission}
      </Text>
      <Text style={styles.statusDescription}>
        Your commitment has been reviewed and verified by the Habit Royale Team. Excellent work!
      </Text>
    </View>
  );

  // Show setup if setupStatus is false
  if (!currentCommit.setupStatus) {
    return (
      <CommitSetup
        commit={currentCommit}
        onSetupComplete={handleSetupComplete}
      />
    );
  }

  return (
    <View style={styles.container}>
      {/* Commit Info - Always at top */}
      <View style={styles.commitInfo}>
        <Text style={styles.commitTitle}>{currentCommit.title}</Text>
        <Text style={styles.commitFrequency}>
          {currentCommit.schedule.frequency.charAt(0).toUpperCase() + currentCommit.schedule.frequency.slice(1)} • {currentCommit.evidence?.type?.charAt(0).toUpperCase() + currentCommit.evidence?.type?.slice(1) || 'Photo'}
        </Text>
      </View>

      {/* Weekly Progress Indicator - Only for weekly mode */}
      {currentCommit.schedule.frequency === 'weekly' && weeklyStatus && (
        <WeeklyProgressIndicator
          submittedCount={weeklyStatus.submittedCount}
          totalRequired={weeklyStatus.submittedCount + weeklyStatus.pendingCount}
          todaySubmissionExists={weeklyStatus.todaySubmissionExists}
          canSubmitToday={weeklyStatus.canSubmitToday}
          style={styles.weeklyProgress}
        />
      )}

      {/* Status Title */}
      <View style={styles.statusTitleContainer}>
        <Text style={styles.statusTitle}>
          {getSubmissionTitle()}
        </Text>
      </View>

      {checkingExistingSubmission ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={colors.primary} />
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      ) : submissionStatus === 'verified' ? (
        renderVerifiedStatus()
      ) : submissionStatus === 'submitted' ? (
        // For weekly mode, show submitted status if user cannot submit today or has completed all submissions
        // For other modes, always show submitted status
        currentCommit.schedule.frequency === 'weekly'
          ? (weeklyStatus && (!weeklyStatus.canSubmitToday || !weeklyStatus.canSubmit) ? renderSubmittedStatus() : (
              <VerificationInput
                config={getVerificationConfig()}
                programId={currentCommit.id || ''}
                userId={userId}
                day={parseInt(currentDaySubmission.split(' ')[1]) || 1}
                onSubmit={handleVerificationSubmit}
                onValidationChange={setIsVerificationValid}
                initialData={initialVerificationData}
                disabled={isLoading}
              />
            ))
          : renderSubmittedStatus()
      ) : (
        <VerificationInput
          config={getVerificationConfig()}
          programId={currentCommit.id || ''}
          userId={userId}
          day={parseInt(currentDaySubmission.split(' ')[1]) || 1}
          onSubmit={handleVerificationSubmit}
          onValidationChange={setIsVerificationValid}
          initialData={initialVerificationData}
          disabled={isLoading || (currentCommit.schedule.frequency === 'weekly' && weeklyStatus && !weeklyStatus.canSubmitToday) || false}
        />
      )}
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 16,
    margin: 16,
    borderWidth: 1,
    borderColor: colors.border || colors.textMuted + '20',
    borderStyle: 'dashed',
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    color: colors.text,
    flex: 1,
  },
  dayIndicator: {
    fontSize: 14,
    fontFamily: 'MontserratSemiBold',
    color: colors.primary,
    backgroundColor: colors.primary + '20',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  weeklyProgress: {
    marginVertical: 14,
    paddingHorizontal: 4,
  },
  commitInfo: {
    marginBottom: 8,
    paddingBottom: 14,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  statusTitleContainer: {
    marginBottom: 14,
    alignItems: 'center',
  },
  statusTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: colors.text,
    textAlign: 'center',
    marginBottom: 4,
    letterSpacing: 0.3,
    lineHeight: 24,
  },
  commitTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: colors.text,
    marginBottom: 5,
    textAlign: 'center',
  },
  commitFrequency: {
    fontSize: 13,
    fontFamily: 'MontserratMedium',
    color: colors.textSecondary,
    textAlign: 'center',
    letterSpacing: 0.5,
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
    marginTop: 8,
  },
  statusContainer: {
    backgroundColor: colors.background,
    borderRadius: 12,
    padding: 18,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.primary + '20',
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusHeaderTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: colors.primary,
    marginLeft: 8,
  },
  statusSubtitle: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    color: colors.text,
    marginBottom: 8,
    textAlign: 'center',
  },
  statusDescription: {
    fontSize: 15,
    fontFamily: 'MontserratRegular',
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 22,
  },
  evidencePreview: {
    backgroundColor: colors.surface,
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    width: '100%',
  },
  evidencePreviewTitle: {
    fontSize: 14,
    fontFamily: 'MontserratBold',
    color: colors.text,
    marginBottom: 8,
  },
  evidencePreviewText: {
    fontSize: 13,
    fontFamily: 'MontserratRegular',
    color: colors.textSecondary,
    marginBottom: 4,
  },
});

export default CommitInput;
