export const calculateCurrentDay = (startDate: string | undefined) => {
    if (!startDate) return 1;
    
    // Create date objects with time set to midnight to avoid time-of-day issues
    const start = new Date(startDate);
    start.setHours(0, 0, 0, 0);
    
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    // Calculate difference in days
    const diff = Math.round(
        (today.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)
    );
    
    return diff + 1;
};

export const calculateDaysToStart = (startDate: string | undefined): number => {
    if (!startDate) return 0;
    const currentDate = new Date();
    const start = new Date(startDate);
    const difference = start.getTime() - currentDate.getTime();
    return Math.ceil(difference / (1000 * 60 * 60 * 24));
};
