import React, { useState, useCallback, useMemo } from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { MaterialCommunityIcons, MaterialIcons } from "@expo/vector-icons";
import { useTheme } from "@/contexts/ThemeContext";

interface FAQSectionProps {
  category: string;
  betAmount: number;
  startDate: string;
  endDate: string;
}

interface Step {
  title: string;
  description: string;
  iconName: string;
}

const FAQSection: React.FC<FAQSectionProps> = React.memo(({
  category,
  betAmount,
  startDate,
  endDate,
}) => {
  const { colors, isDark } = useTheme();
  const styles = createStyles(colors, isDark);
  // Memoize steps calculation
  const steps = useMemo<Step[]>(() => {
    const baseSteps: Step[] = [
      {
        title: "Pledge it!",
        description: "Add your money to the pool.",
        iconName: "currency-usd",
      },
    ];
    const lowerCategory = category.toLowerCase();
    if (lowerCategory === "cardio") {
      baseSteps.push(
        {
          title: "Connect Account",
          description: "Strava",
          iconName: "account",
        },
        {
          title: "Make Progress",
          description: "Upload your Strava activity everyday",
          iconName: "chart-line",
        }
      );
    } else if (lowerCategory === "coding") {
      baseSteps.push(
        {
          title: "Connect Account",
          description: "GitHub",
          iconName: "code-tags",
        },
        {
          title: "Make Progress",
          description: "Make at least 2 commits everyday",
          iconName: "source-branch",
        }
      );
    } else if (lowerCategory === "gym") {
      baseSteps.push(
        {
          title: "Give Permission",
          description: "Camera, GPS access required",
          iconName: "camera",
        },
        {
          title: "Make Progress",
          description: "Upload a picture from the gym",
          iconName: "image",
        }
      );
    } else {
      baseSteps.push(
        {
          title: "Connect Account",
          description: "Connect your account",
          iconName: "account",
        },
        {
          title: "Make Progress",
          description: "Follow your daily routine",
          iconName: "check",
        }
      );
    }
    baseSteps.push({
      title: "Claim your Money",
      description: "Your money back + your share of the pot.",
      iconName: "cash",
    });
    return baseSteps;
  }, [category]);

  // Memoize FAQ questions calculation
  const faqQuestions = useMemo(() => [
    {
      question: "Why do I need to pay money?",
      answer: "Put your money on the line to stay motivated — finish and get it back. If others drop out, you profit!"
    },
    {
      question: "How do I get my money back?",
      answer: "If you make it to the end of the challenge by staying consistent, you will get your money along with your share of the losers pot.",
    },
    {
      question: "Can I take days off?",
      answer: "You can use your lives to skip a day which is provided to you for free. You can also purchase more lives but has a limit.",
    },
    {
      question: "How many lives do I get and how many can I purchase?",
      answer: "Depends on the duration of the challenge, you can check it in the challenge details before signup",
    },
    {
      question: "How will my submission be verified?",
      answer: "Your submission will be verified by the moderator assigned to your challenge. They will check your submission and verify if it is valid or not.",
    },
    {
      question: "What if my submission is invalid?",
      answer: "If your submission is invalid, a life will be deducted from your lives. If you're out of lives, you will be disqualified.",
    },
    {
      question: "Will all the winners get same share of the pot?",
      answer: "No, the share of the pot is distributed based on the number of lives winners have used at the end of the challenge.",
    },
    {
      question: "What happens if I don't make it?",
      answer: "If you don't make it, you lose your money and will be retained in the pool, which will be shared among the winners.",
    },
    {
      question: "How do I track my progress?",
      answer: "Log your daily submissions and monitor improvements on our interactive dashboard.",
    },
    {
      question: "How is my submission data used?",
      answer: "Your data is only used to verify the legitamacy of your submissions and will be deleted 15 days after the challenge ends if there's no pending dispute.",
    },
    {
      question: "Is my data safe?",
      answer: "Your data is encrypted and stored securely. We take data security seriously and will never share your data with anyone no matter what.",
    }
  ], [category, betAmount, startDate, endDate]);

  const [expanded, setExpanded] = useState<Record<number, boolean>>({});

  const toggleFAQ = useCallback((index: number) => {
    setExpanded(prev => ({ ...prev, [index]: !prev[index] }));
  }, []);

  return (
    <View style={styles.container}>
      {/* How It Works Section */}
      <View style={styles.howItWorksContainer}>
        <Text style={styles.howItWorksTitle}>How It Works</Text>
        <View style={styles.stepsGrid}>
          {steps.map((step, idx) => (
            <View key={idx} style={styles.stepCard}>
              <View style={styles.stepNumberContainer}>
                <Text style={styles.stepNumber}>{idx + 1}</Text>
              </View>
              <MaterialCommunityIcons name={step.iconName as any} size={28} color={colors.primary} />
              <Text style={styles.stepTitle}>{step.title}</Text>
              <Text style={styles.stepDescription}>{step.description}</Text>
            </View>
          ))}
        </View>
      </View>

      {/* FAQ Section */}
      <View style={styles.faqContainer}>
        <Text style={styles.faqTitle}>Frequently Asked Questions</Text>
        {faqQuestions.map((item, index) => (
          <View key={index} style={styles.faqItem}>
            <TouchableOpacity onPress={() => toggleFAQ(index)} style={styles.faqQuestionContainer}>
              <Text style={styles.faqQuestionText}>{item.question}</Text>
              <MaterialIcons name={expanded[index] ? "keyboard-arrow-up" : "keyboard-arrow-down"} size={20} color={colors.primary} />
            </TouchableOpacity>
            {expanded[index] && (
              <View style={styles.faqAnswerContainer}>
                <Text style={styles.faqAnswerText}>{item.answer}</Text>
              </View>
            )}
          </View>
        ))}
      </View>
    </View>
  );
});

const createStyles = (colors: any, isDark: boolean) => StyleSheet.create({
  container: {
    marginVertical: 5,
    paddingHorizontal: 10,
  },
  howItWorksContainer: {
    marginBottom: 5,
    padding: 8,
    backgroundColor: colors.surface,
    borderRadius: 10,
    shadowColor: isDark ? "#000" : "#000",
    shadowOpacity: isDark ? 0.3 : 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  howItWorksTitle: {
    fontSize: 20,
    fontFamily: "MontserratBold",
    color: colors.primary,
    marginBottom: 6,
    textAlign: "center",
  },
  stepsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  stepCard: {
    width: "48%",
    backgroundColor: colors.card,
    padding: 6,
    borderRadius: 10,
    alignItems: "center",
    marginBottom: 6,
    position: "relative",
    borderWidth: isDark ? 0 : 1,
    borderColor: colors.border,
  },
  stepNumberContainer: {
    position: "absolute",
    top: 4,
    left: 4,
    backgroundColor: colors.primary,
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: "center",
    alignItems: "center",
  },
  stepNumber: {
    fontSize: 10,
    fontFamily: "MontserratBold",
    color: "#000000", // Keep black text on yellow background for readability
  },
  stepTitle: {
    fontSize: 12,
    fontFamily: "MontserratBold",
    color: colors.text,
    marginTop: 4,
    textAlign: "center",
  },
  stepDescription: {
    fontSize: 10,
    fontFamily: "MontserratRegular",
    color: colors.textSecondary,
    textAlign: "center",
    marginTop: 2,
  },
  faqContainer: {
    backgroundColor: colors.surface,
    borderRadius: 10,
    padding: 10,
    marginTop: 10,
  },
  faqTitle: {
    fontSize: 20,
    fontFamily: "MontserratBold",
    color: colors.primary,
    marginBottom: 8,
    textAlign: "center",
  },
  faqItem: {
    marginBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: colors.separator,
    paddingBottom: 6,
  },
  faqQuestionContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  faqQuestionText: {
    fontSize: 14,
    color: colors.text,
    fontFamily: "MontserratBold",
    flex: 1,
  },
  faqAnswerContainer: {
    marginTop: 5,
    paddingLeft: 8,
  },
  faqAnswerText: {
    fontSize: 12,
    fontFamily: "MontserratRegular",
    color: colors.textSecondary,
  },
});

export default FAQSection;
