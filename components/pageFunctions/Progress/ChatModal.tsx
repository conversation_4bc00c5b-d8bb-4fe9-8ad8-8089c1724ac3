import { MaterialIcons } from "@expo/vector-icons";
import { Modal, Text, TouchableOpacity, View } from "react-native";
import ProgramChat from "./ProgramChat";
import { StyleSheet } from "react-native";
import { Program } from "@/types/CommonInterface";
import { useTheme } from "@/contexts/ThemeContext";

interface ChatModalProps {
  userId: string;
  userName: string;
  isChatVisible: boolean;
  selectedProgram: Program | null;
  chatVisibler: (chatVisible: boolean) => void;
}

export const ChatModal: React.FC<ChatModalProps> = ({
  userId,
  userName,
  isChatVisible,
  selectedProgram,
  chatVisibler,

}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  return (
    <Modal
      visible={Boolean(isChatVisible)}
      animationType="slide"
      onRequestClose={() => chatVisibler(false)}
    >
      <View style={styles.chatModalContainer}>
        <View style={styles.chatHeader}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => chatVisibler(false)}
          >
            <MaterialIcons name="close" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.chatTitle}>Program Chat</Text>
        </View>
        {selectedProgram && (
          <ProgramChat
            programId={selectedProgram.id}
            userId={userId}
            userName={userName}
          />
        )}
      </View>
    </Modal>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  chatModalContainer: {
    flex: 1,
    backgroundColor: colors.background,
  },
  chatHeader: {
    flexDirection: "row",
    alignItems: "center",
    padding: 15,
    backgroundColor: colors.header,
  },
  closeButton: {
    marginRight: 15,
  },
  chatTitle: {
    color: colors.text,
    fontSize: 18,
    fontWeight: "bold",
  },
});
