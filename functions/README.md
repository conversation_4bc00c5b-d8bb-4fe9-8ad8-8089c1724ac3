# Firebase Cloud Functions - GitHub OAuth

This directory contains Firebase Cloud Functions for handling GitHub OAuth authentication for the Habit Royale app.

## Setup Instructions

### 1. Install Dependencies

```bash
cd functions
npm install
```

### 2. Set Environment Variables

Set your GitHub OAuth credentials:

```bash
firebase functions:config:set github.client_id="your_github_client_id"
firebase functions:config:set github.client_secret="your_github_client_secret"
```

### 3. Build and Deploy

```bash
# Build TypeScript
npm run build

# Deploy functions
firebase deploy --only functions

# Or use the deployment script
./deploy.sh
```

## Functions

### `githubOAuthCallback`
- **URL**: `https://betonself.cloudfunctions.net/githubOAuthCallback`
- **Purpose**: Handles GitHub OAuth callback and exchanges code for access token
- **Method**: GET
- **Parameters**: `code`, `state` (from GitHub OAuth redirect)

### `getGitHubToken`
- **Purpose**: Retrieves stored GitHub access token by state parameter
- **Type**: Callable function (for mobile app)
- **Authentication**: Required

## GitHub OAuth App Configuration

Update your GitHub OAuth App with:

**Authorization callback URL:**
```
https://betonself.cloudfunctions.net/githubOAuthCallback
```

## Environment Variables

The functions expect these environment variables:

- `github.client_id` - Your GitHub OAuth App Client ID
- `github.client_secret` - Your GitHub OAuth App Client Secret

## Testing

1. Deploy the functions
2. Update GitHub OAuth App callback URL
3. Test the OAuth flow in your app
4. Check Firebase Console logs for any errors

## Security Notes

- Client Secret is stored securely in Firebase Functions config
- OAuth tokens are temporarily stored in Firestore with 1-hour expiry
- Tokens are deleted after retrieval (one-time use)
- State parameter validation prevents CSRF attacks

## Troubleshooting

### Common Issues

1. **"Function not found"** - Make sure functions are deployed
2. **"Invalid client"** - Check GitHub OAuth App configuration
3. **"Token not found"** - Token may have expired or already been used
4. **CORS errors** - Functions include proper CORS headers

### Logs

Check function logs:
```bash
firebase functions:log
```

### Local Testing

Run functions locally:
```bash
npm run serve
```

## File Structure

```
functions/
├── src/
│   ├── index.ts          # Main functions export
│   └── githubOAuth.ts    # GitHub OAuth implementation
├── lib/                  # Compiled JavaScript (auto-generated)
├── package.json
├── tsconfig.json
└── deploy.sh            # Deployment script
```
