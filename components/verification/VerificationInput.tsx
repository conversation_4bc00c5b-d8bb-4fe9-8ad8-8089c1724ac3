import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Image,
  Modal,
  ActivityIndicator,
} from "react-native";
import { MaterialIcons, Feather } from "@expo/vector-icons";
import * as Clipboard from "expo-clipboard";
import * as ImagePicker from "expo-image-picker";
import * as Location from "expo-location";
import { getStorage, ref, uploadBytes, getDownloadURL } from "firebase/storage";
import { useTheme } from "@/contexts/ThemeContext";
import { GitHubVerificationInput } from "./GitHubVerificationInput";
import {
  VerificationConfig,
  VerificationData,
  VerificationResult,
  LocationData,
  createVerificationData,
} from "../../types/verification";

// Conditional import for expo-maps
let Map: any = null;
let Marker: any = null;
let mapsAvailable = false;

try {
  const expoMaps = require('expo-maps');
  Map = expoMaps.Map;
  Marker = expoMaps.Marker;
  mapsAvailable = true;
} catch (error) {
  console.log('expo-maps not available, using fallback:', error);
  mapsAvailable = false;
}

interface VerificationInputProps {
  config: VerificationConfig;
  programId: string;
  userId: string;
  day: number;
  onSubmit: (data: VerificationData) => Promise<VerificationResult>;
  onValidationChange?: (isValid: boolean) => void;
  initialData?: VerificationData | null;
  disabled?: boolean;
}

export const VerificationInput: React.FC<VerificationInputProps> = ({
  config,
  programId,
  userId,
  day,
  onSubmit,
  onValidationChange,
  initialData,
  disabled = false,
}) => {
  const { colors } = useTheme();
  const storage = getStorage();
  
  // State for different verification types
  const [images, setImages] = useState<string[]>([]);
  const [location, setLocation] = useState<LocationData | null>(null);
  const [textValue, setTextValue] = useState<string>("");
  const [currentStep, setCurrentStep] = useState<1 | 2 | 3>(1);
  
  // UI state
  const [uploading, setUploading] = useState<boolean>(false);
  const [gettingLocation, setGettingLocation] = useState<boolean>(false);
  const [showImageModal, setShowImageModal] = useState<boolean>(false);
  const [showMapModal, setShowMapModal] = useState<boolean>(false);
  const [selectedImageUri, setSelectedImageUri] = useState<string | null>(null);

  // Initialize with existing data
  useEffect(() => {
    if (initialData) {
      setImages(initialData.images || []);
      setLocation(initialData.location || null);
      setTextValue(initialData.textValue || "");

      // Set appropriate step for camera+gps
      if (config.type === 'camera+gps') {
        if (initialData.images?.length && initialData.location) {
          setCurrentStep(3); // Review step
        } else if (initialData.images?.length) {
          setCurrentStep(2); // GPS step
        } else {
          setCurrentStep(1); // Camera step
        }
      }
    } else {
      // Clear state when initialData is null
      setImages([]);
      setLocation(null);
      setTextValue("");
      setCurrentStep(1);
    }
  }, [initialData, config.type]);

  // Validation effect
  useEffect(() => {
    const isValid = validateCurrentState();
    onValidationChange?.(isValid);
  }, [images, location, textValue, config.type, currentStep]);

  const validateCurrentState = (): boolean => {
    if (disabled) return false;

    switch (config.type) {
      case 'camera':
        return images.length > 0;
      case 'photo':
        return images.length > 0;
      case 'gps':
        return location !== null;
      case 'camera+gps':
        return currentStep === 3 && images.length > 0 && location !== null;
      case 'text':
        if (config.text?.validation) {
          return config.text.validation(textValue);
        }
        return textValue.trim().length > 0;
      default:
        return false;
    }
  };

  const handleImageCapture = async () => {
    try {
      const permissionResult = await ImagePicker.requestCameraPermissionsAsync();
      if (!permissionResult.granted) {
        Alert.alert("Permission Denied", "Camera access is required.");
        return;
      }

      const cameraConfig = config.type === 'photo' ? config.photo : config.camera;
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: cameraConfig?.allowsEditing ?? true,
        quality: cameraConfig?.quality ?? 0.5,
      });

      if (!result.canceled && result.assets?.[0]?.uri) {
        const newImageUri = result.assets[0].uri;

        if (cameraConfig?.allowMultiple) {
          setImages(prev => [...prev, newImageUri]);
        } else {
          setImages([newImageUri]);
        }

        // Auto-progress for camera+gps flow
        if (config.type === 'camera+gps' && currentStep === 1) {
          setTimeout(() => setCurrentStep(2), 500);
        }
      } else {
        Alert.alert("Capture Cancelled", "No image was captured.");
      }
    } catch (error) {
      console.error("Error capturing image:", error);
      Alert.alert("Error", "Unable to access the camera.");
    }
  };

  const handleGalleryPick = async () => {
    try {
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (!permissionResult.granted) {
        Alert.alert("Permission Denied", "Gallery access is required.");
        return;
      }

      const photoConfig = config.photo;
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: photoConfig?.allowsEditing ?? true,
        quality: photoConfig?.quality ?? 0.5,
        allowsMultipleSelection: photoConfig?.allowMultiple ?? false,
      });

      if (!result.canceled && result.assets?.length) {
        const newImageUris = result.assets.map(asset => asset.uri);

        if (photoConfig?.allowMultiple) {
          setImages(prev => [...prev, ...newImageUris]);
        } else {
          setImages([newImageUris[0]]);
        }
      } else {
        Alert.alert("Selection Cancelled", "No images were selected.");
      }
    } catch (error) {
      console.error("Error picking from gallery:", error);
      Alert.alert("Error", "Unable to access the gallery.");
    }
  };

  const handleLocationCapture = async () => {
    setGettingLocation(true);
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Location permission is required for GPS verification.'
        );
        return;
      }

      const locationResult = await Location.getCurrentPositionAsync({
        accuracy: config.gps?.accuracy === 'high' ? Location.Accuracy.High : Location.Accuracy.Balanced,
      });

      const newLocation: LocationData = {
        latitude: locationResult.coords.latitude,
        longitude: locationResult.coords.longitude,
      };

      setLocation(newLocation);

      Alert.alert('Success', 'GPS location captured successfully!', [
        {
          text: 'OK',
          onPress: () => {
            if (config.type === 'camera+gps') {
              setCurrentStep(3); // Move to review step
            }
          }
        }
      ]);
    } catch (error) {
      console.error('Error getting location:', error);
      Alert.alert('Error', 'Unable to get your current location. Please try again.');
    } finally {
      setGettingLocation(false);
    }
  };

  const handleSubmit = async () => {
    if (!validateCurrentState()) {
      Alert.alert("Validation Error", "Please complete all required fields.");
      return;
    }

    setUploading(true);
    try {
      let uploadedImages: string[] = [];

      // Upload images if present
      if (images.length > 0) {
        const uploadPromises = images.map(async (imageUri, index) => {
          const imageRef = ref(storage, `verification/${programId}/${userId}/Day${day}_${index + 1}.jpg`);
          const response = await fetch(imageUri);
          const blob = await response.blob();
          await uploadBytes(imageRef, blob);
          return await getDownloadURL(imageRef);
        });

        uploadedImages = await Promise.all(uploadPromises);
      }

      const verificationData = createVerificationData(config.type, {
        images: uploadedImages.length > 0 ? uploadedImages : undefined,
        location: location || undefined,
        textValue: textValue || undefined,
      });

      const result = await onSubmit(verificationData);
      
      if (result.success) {
        // Reset state after successful submission
        setImages([]);
        setLocation(null);
        setTextValue("");
        setCurrentStep(1);
        
        Alert.alert("Success", "Your submission has been uploaded successfully!");
      } else {
        Alert.alert("Submission Failed", result.error || "An error occurred during submission.");
      }
    } catch (error) {
      console.error("Error during submission:", error);
      Alert.alert("Submission Failed", "An error occurred during submission.");
    } finally {
      setUploading(false);
    }
  };

  const styles = createStyles(colors);

  // Render different verification types
  const renderVerificationInput = () => {
    switch (config.type) {
      case 'camera':
        return renderCameraInput();
      case 'photo':
        return renderPhotoInput();
      case 'gps':
        return renderGPSInput();
      case 'camera+gps':
        return renderCameraGPSInput();
      case 'text':
        return renderTextInput();
      case 'github':
        return renderGitHubInput();
      default:
        return null;
    }
  };

  const renderCameraInput = () => (
    <View style={styles.inputContainer}>
      {images.length === 0 ? (
        <TouchableOpacity
          style={styles.cameraButton}
          onPress={handleImageCapture}
          disabled={disabled}
        >
          <MaterialIcons name="camera-alt" size={24} color="#FFF" />
          <Text style={styles.cameraButtonText}>
            {config.camera?.allowMultiple ? "Capture First Photo" : "Capture Photo"}
          </Text>
        </TouchableOpacity>
      ) : config.camera?.allowMultiple ? (
        // Multiple photo layout for writing category
        <View style={styles.multiplePhotoContainer}>
          <Text style={styles.photoCountLabel}>
            Photo{images.length > 1 ? 's' : ''} ({images.length})
          </Text>
          <View style={styles.reviewFullGallery}>
            {images.map((uri, index) => (
              <View key={index} style={styles.reviewImageItem}>
                <TouchableOpacity
                  onPress={() => {
                    setSelectedImageUri(uri);
                    setShowImageModal(true);
                  }}
                >
                  <Image source={{ uri }} style={styles.reviewGalleryImage} />
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.reviewRemoveButton}
                  onPress={() => {
                    const newImages = images.filter((_, i) => i !== index);
                    setImages(newImages);
                  }}
                  disabled={disabled}
                >
                  <MaterialIcons name="close" size={14} color="#FFF" />
                </TouchableOpacity>
              </View>
            ))}
            <TouchableOpacity
              style={styles.addMoreImageButton}
              onPress={handleImageCapture}
              disabled={disabled}
            >
              <MaterialIcons name="add" size={24} color="#666" />
              <Text style={styles.addMoreImageText}>Add Photo</Text>
            </TouchableOpacity>
          </View>
        </View>
      ) : (
        // Single photo layout for other categories
        <View style={styles.previewContainer}>
          <TouchableOpacity
            onPress={() => {
              setSelectedImageUri(images[0]);
              setShowImageModal(true);
            }}
          >
            <Image source={{ uri: images[0] }} style={styles.imagePreview} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.clearButton}
            onPress={() => setImages([])}
            disabled={disabled}
          >
            <Text style={styles.clearButtonText}>Retake</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );

  const renderPhotoInput = () => {
    return (
      <View style={styles.inputContainer}>
        {images.length === 0 ? (
          <View style={styles.photoOptionsContainer}>
          {config.photo?.allowCamera && (
            <TouchableOpacity
              style={[styles.photoOptionButton, disabled && styles.disabledButton]}
              onPress={handleImageCapture}
              disabled={disabled}
              activeOpacity={0.7}
            >
              <MaterialIcons name="camera-alt" size={24} color="#000" />
              <Text style={styles.photoOptionButtonText}>Camera</Text>
            </TouchableOpacity>
          )}
          {config.photo?.allowGallery && (
            <TouchableOpacity
              style={[styles.photoOptionButton, disabled && styles.disabledButton]}
              onPress={handleGalleryPick}
              disabled={disabled}
              activeOpacity={0.7}
            >
              <MaterialIcons name="photo-library" size={24} color="#000" />
              <Text style={styles.photoOptionButtonText}>Gallery</Text>
            </TouchableOpacity>
          )}
        </View>
      ) : config.photo?.allowMultiple ? (
        // Multiple photo layout
        <View style={styles.multiplePhotoContainer}>
          <Text style={styles.photoCountLabel}>
            Photo{images.length > 1 ? 's' : ''} ({images.length})
          </Text>
          <View style={styles.reviewFullGallery}>
            {images.map((uri, index) => (
              <View key={index} style={styles.reviewImageItem}>
                <TouchableOpacity
                  onPress={() => {
                    setSelectedImageUri(uri);
                    setShowImageModal(true);
                  }}
                >
                  <Image source={{ uri }} style={styles.reviewGalleryImage} />
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.reviewRemoveButton}
                  onPress={() => {
                    const newImages = images.filter((_, i) => i !== index);
                    setImages(newImages);
                  }}
                  disabled={disabled}
                >
                  <MaterialIcons name="close" size={14} color="#FFF" />
                </TouchableOpacity>
              </View>
            ))}
            <View style={styles.addMoreOptionsContainer}>
              {config.photo?.allowCamera && (
                <TouchableOpacity
                  style={styles.addMoreOptionButton}
                  onPress={handleImageCapture}
                  disabled={disabled}
                >
                  <MaterialIcons name="camera-alt" size={16} color="#666" />
                  <Text style={styles.addMoreOptionText}>Camera</Text>
                </TouchableOpacity>
              )}
              {config.photo?.allowGallery && (
                <TouchableOpacity
                  style={styles.addMoreOptionButton}
                  onPress={handleGalleryPick}
                  disabled={disabled}
                >
                  <MaterialIcons name="photo-library" size={16} color="#666" />
                  <Text style={styles.addMoreOptionText}>Gallery</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </View>
      ) : (
        // Single photo layout
        <View style={styles.previewContainer}>
          <TouchableOpacity
            onPress={() => {
              setSelectedImageUri(images[0]);
              setShowImageModal(true);
            }}
          >
            <Image source={{ uri: images[0] }} style={styles.imagePreview} />
          </TouchableOpacity>
          <View style={styles.photoActionsContainer}>
            <TouchableOpacity
              style={styles.clearButton}
              onPress={() => setImages([])}
              disabled={disabled}
            >
              <Text style={styles.clearButtonText}>Clear</Text>
            </TouchableOpacity>
            <View style={styles.retakeOptionsContainer}>
              {config.photo?.allowCamera && (
                <TouchableOpacity
                  style={styles.retakeOptionButton}
                  onPress={handleImageCapture}
                  disabled={disabled}
                >
                  <MaterialIcons name="camera-alt" size={16} color="#666" />
                  <Text style={styles.retakeOptionText}>Camera</Text>
                </TouchableOpacity>
              )}
              {config.photo?.allowGallery && (
                <TouchableOpacity
                  style={styles.retakeOptionButton}
                  onPress={handleGalleryPick}
                  disabled={disabled}
                >
                  <MaterialIcons name="photo-library" size={16} color="#666" />
                  <Text style={styles.retakeOptionText}>Gallery</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </View>
      )}
      </View>
    );
  };

  const renderGPSInput = () => (
    <View style={styles.inputContainer}>
      {!location ? (
        <TouchableOpacity
          style={[styles.primaryActionButton, gettingLocation && styles.disabledButton]}
          onPress={handleLocationCapture}
          disabled={gettingLocation || disabled}
        >
          {gettingLocation ? (
            <ActivityIndicator size="small" color="#000" />
          ) : (
            <>
              <MaterialIcons name="location-on" size={20} color="#000" />
              <Text style={styles.primaryActionButtonText}>Get Location</Text>
            </>
          )}
        </TouchableOpacity>
      ) : (
        <View style={styles.locationPreview}>
          <TouchableOpacity
            style={styles.mapContainer}
            onPress={() => setShowMapModal(true)}
          >
            {mapsAvailable && Map ? (
              <Map
                style={styles.compactMap}
                initialRegion={{
                  latitude: location.latitude,
                  longitude: location.longitude,
                  latitudeDelta: 0.01,
                  longitudeDelta: 0.01,
                }}
                scrollEnabled={false}
                zoomEnabled={false}
              >
                <Marker coordinate={location} />
              </Map>
            ) : (
              <View style={styles.mapFallback}>
                <MaterialIcons name="location-on" size={24} color="#4CAF50" />
              </View>
            )}
          </TouchableOpacity>
          <Text style={styles.coordinatesText}>
            {location.latitude.toFixed(4)}, {location.longitude.toFixed(4)}
          </Text>
          <TouchableOpacity
            style={styles.clearButton}
            onPress={() => setLocation(null)}
            disabled={disabled}
          >
            <Text style={styles.clearButtonText}>Recapture</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );

  const renderTextInput = () => (
    <View style={styles.inputContainer}>
      <View style={styles.textInputContainer}>
        <TextInput
          style={styles.textInput}
          placeholder={config.text?.placeholder || "Enter text..."}
          placeholderTextColor="#888"
          value={textValue}
          onChangeText={setTextValue}
          editable={!disabled}
        />
        <TouchableOpacity
          onPress={() => Clipboard.getStringAsync().then(setTextValue)}
          disabled={disabled}
        >
          <Feather name="clipboard" size={18} color={colors.warning} />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderGitHubInput = () => (
    <GitHubVerificationInput
      onSubmit={(data) => {
        // Store GitHub data for submission
        const verificationData = createVerificationData(
          'github',
          {
            githubData: data
          }
        );
        onSubmit(verificationData);
      }}
      onValidationChange={onValidationChange || (() => {})}
      disabled={disabled}
    />
  );

  const renderCameraGPSInput = () => (
    <View style={styles.gymContainer}>
      {/* Step Progress Indicator */}
      <View style={styles.stepIndicator}>
        <View style={styles.stepRow}>
          {/* Step 1 */}
          <View style={styles.stepItem}>
            <View style={[styles.stepCircle, currentStep >= 1 && styles.stepCircleActive]}>
              {currentStep > 1 ? (
                <MaterialIcons name="check" size={14} color="#000" />
              ) : (
                <Text style={[styles.stepNumber, currentStep >= 1 && styles.stepNumberActive]}>1</Text>
              )}
            </View>
            <Text style={[styles.stepLabel, currentStep === 1 && styles.stepLabelActive]}>Photo</Text>
          </View>

          {/* Line 1 */}
          <View style={[styles.stepLine, currentStep >= 2 && styles.stepLineActive]} />

          {/* Step 2 */}
          <View style={styles.stepItem}>
            <View style={[styles.stepCircle, currentStep >= 2 && styles.stepCircleActive]}>
              {currentStep > 2 ? (
                <MaterialIcons name="check" size={14} color="#000" />
              ) : (
                <Text style={[styles.stepNumber, currentStep >= 2 && styles.stepNumberActive]}>2</Text>
              )}
            </View>
            <Text style={[styles.stepLabel, currentStep === 2 && styles.stepLabelActive]}>Location</Text>
          </View>

          {/* Line 2 */}
          <View style={[styles.stepLine, currentStep >= 3 && styles.stepLineActive]} />

          {/* Step 3 */}
          <View style={styles.stepItem}>
            <View style={[styles.stepCircle, currentStep >= 3 && styles.stepCircleActive]}>
              <Text style={[styles.stepNumber, currentStep >= 3 && styles.stepNumberActive]}>3</Text>
            </View>
            <Text style={[styles.stepLabel, currentStep === 3 && styles.stepLabelActive]}>Review</Text>
          </View>
        </View>
      </View>

      {/* Step Content */}
      {currentStep === 1 && renderCameraGPSStep1()}
      {currentStep === 2 && renderCameraGPSStep2()}
      {currentStep === 3 && renderCameraGPSStep3()}
    </View>
  );

  const renderCameraGPSStep1 = () => (
    <View style={styles.stepContent}>
      <Text style={styles.stepTitle}>📸 Capture Photos</Text>
      {images.length === 0 ? (
        <TouchableOpacity
          style={styles.primaryActionButton}
          onPress={handleImageCapture}
          disabled={disabled}
        >
          <MaterialIcons name="camera-alt" size={20} color="#000" />
          <Text style={styles.primaryActionButtonText}>Take First Photo</Text>
        </TouchableOpacity>
      ) : (
        <View style={styles.compactPreview}>
          <TouchableOpacity onPress={() => {
            setSelectedImageUri(images[0]);
            setShowImageModal(true);
          }}>
            <Image source={{ uri: images[0] }} style={styles.compactImage} />
          </TouchableOpacity>
          <Text style={styles.imageCount}>
            {images.length} photo{images.length > 1 ? 's' : ''} captured
          </Text>
          <View style={styles.compactActions}>
            <TouchableOpacity
              style={styles.compactButton}
              onPress={handleImageCapture}
              disabled={disabled}
            >
              <MaterialIcons name="add" size={16} color="#666" />
              <Text style={styles.compactButtonText}>Add More</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.compactButtonPrimary}
              onPress={() => setCurrentStep(2)}
              disabled={disabled}
            >
              <Text style={styles.compactButtonPrimaryText}>Next</Text>
              <MaterialIcons name="arrow-forward" size={16} color="#000" />
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );

  const renderCameraGPSStep2 = () => (
    <View style={styles.stepContent}>
      <Text style={styles.stepTitle}>📍 Capture GPS Location</Text>
      {!location ? (
        <TouchableOpacity
          style={[styles.primaryActionButton, gettingLocation && styles.disabledButton]}
          onPress={handleLocationCapture}
          disabled={gettingLocation || disabled}
        >
          {gettingLocation ? (
            <ActivityIndicator size="small" color="#000" />
          ) : (
            <>
              <MaterialIcons name="location-on" size={20} color="#000" />
              <Text style={styles.primaryActionButtonText}>Get Location</Text>
            </>
          )}
        </TouchableOpacity>
      ) : (
        <View style={styles.compactPreview}>
          <View style={styles.compactLocationContainer}>
            <TouchableOpacity
              style={styles.compactMapContainer}
              onPress={() => setShowMapModal(true)}
            >
              {mapsAvailable && Map && location ? (
                <Map
                  style={styles.compactMap}
                  initialRegion={{
                    latitude: location.latitude,
                    longitude: location.longitude,
                    latitudeDelta: 0.01,
                    longitudeDelta: 0.01,
                  }}
                  scrollEnabled={false}
                  zoomEnabled={false}
                >
                  <Marker coordinate={location} />
                </Map>
              ) : (
                <View style={styles.compactMapFallback}>
                  <MaterialIcons name="location-on" size={24} color="#4CAF50" />
                </View>
              )}
            </TouchableOpacity>
            <Text style={styles.compactCoordinates}>
              {location.latitude.toFixed(4)}, {location.longitude.toFixed(4)}
            </Text>
          </View>
          <View style={styles.compactActions}>
            <TouchableOpacity
              style={styles.compactButton}
              onPress={() => setCurrentStep(1)}
              disabled={disabled}
            >
              <MaterialIcons name="arrow-back" size={16} color="#666" />
              <Text style={styles.compactButtonText}>Back</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.compactButtonPrimary}
              onPress={() => setCurrentStep(3)}
              disabled={disabled}
            >
              <Text style={styles.compactButtonPrimaryText}>Review</Text>
              <MaterialIcons name="arrow-forward" size={16} color="#000" />
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );

  const renderCameraGPSStep3 = () => (
    <View style={styles.stepContent}>
      <Text style={styles.stepTitle}>✅ Review & Submit</Text>
      <View style={styles.compactReviewContainer}>
        <View style={styles.compactReviewItem}>
          <Text style={styles.compactReviewLabel}>
            Photo{images.length > 1 ? 's' : ''} ({images.length})
          </Text>
          <View style={styles.reviewFullGallery}>
            {images.map((uri, index) => (
              <View key={index} style={styles.reviewImageItem}>
                <TouchableOpacity
                  onPress={() => {
                    setSelectedImageUri(uri);
                    setShowImageModal(true);
                  }}
                >
                  <Image source={{ uri }} style={styles.reviewGalleryImage} />
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.reviewRemoveButton}
                  onPress={() => {
                    const newImages = images.filter((_, i) => i !== index);
                    setImages(newImages);
                  }}
                  disabled={disabled}
                >
                  <MaterialIcons name="close" size={14} color="#FFF" />
                </TouchableOpacity>
              </View>
            ))}
            <TouchableOpacity
              style={styles.addMoreImageButton}
              onPress={handleImageCapture}
              disabled={disabled}
            >
              <MaterialIcons name="add" size={24} color="#666" />
              <Text style={styles.addMoreImageText}>Add Photo</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.compactReviewItem}>
          <Text style={styles.compactReviewLabel}>Location</Text>
          <TouchableOpacity
            style={styles.compactReviewMapContainer}
            onPress={() => setShowMapModal(true)}
          >
            {mapsAvailable && Map && location ? (
              <Map
                style={styles.compactReviewMap}
                initialRegion={{
                  latitude: location.latitude,
                  longitude: location.longitude,
                  latitudeDelta: 0.005,
                  longitudeDelta: 0.005,
                }}
                scrollEnabled={false}
                zoomEnabled={false}
              >
                <Marker coordinate={location} />
              </Map>
            ) : (
              <View style={styles.compactReviewMapFallback}>
                <MaterialIcons name="location-on" size={20} color="#4CAF50" />
              </View>
            )}
          </TouchableOpacity>
          <Text style={styles.compactReviewCoordinates}>
            {location?.latitude.toFixed(4)}, {location?.longitude.toFixed(4)}
          </Text>
        </View>
      </View>

      <View style={styles.compactActions}>
        <TouchableOpacity
          style={styles.compactButton}
          onPress={() => setCurrentStep(2)}
          disabled={disabled}
        >
          <MaterialIcons name="arrow-back" size={16} color="#666" />
          <Text style={styles.compactButtonText}>Back</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.compactResetButton}
          onPress={() => {
            setImages([]);
            setLocation(null);
            setCurrentStep(1);
          }}
          disabled={disabled}
        >
          <MaterialIcons name="refresh" size={16} color="#ff6b6b" />
          <Text style={styles.compactResetButtonText}>Reset</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      {renderVerificationInput()}

      {/* Hide submit button for GitHub verification as it has its own verify button */}
      {config.type !== 'github' && (
        <TouchableOpacity
          style={[
            styles.submitButton,
            (uploading || disabled || !validateCurrentState()) && styles.disabledButton
          ]}
          onPress={handleSubmit}
          disabled={uploading || disabled || !validateCurrentState()}
        >
          {uploading ? (
            <ActivityIndicator size="small" color="#000" />
          ) : (
            <Text style={styles.submitButtonText}>Submit</Text>
          )}
        </TouchableOpacity>
      )}

      {/* Image Modal */}
      <Modal
        visible={showImageModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowImageModal(false)}
      >
        <View style={styles.imageModalOverlay}>
          <TouchableOpacity
            style={styles.imageModalBackground}
            activeOpacity={1}
            onPress={() => setShowImageModal(false)}
          >
            <View style={styles.imageModalContainer}>
              {selectedImageUri && (
                <Image
                  source={{ uri: selectedImageUri }}
                  style={styles.fullImage}
                  resizeMode="contain"
                />
              )}
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setShowImageModal(false)}
              >
                <MaterialIcons name="close" size={24} color="#FFF" />
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
        </View>
      </Modal>

      {/* Map Modal */}
      <Modal
        visible={showMapModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowMapModal(false)}
      >
        <View style={styles.mapModalOverlay}>
          <TouchableOpacity
            style={styles.mapModalBackground}
            activeOpacity={1}
            onPress={() => setShowMapModal(false)}
          >
            <View style={styles.mapModalContainer}>
              {location && (
                <>
                  {mapsAvailable && Map ? (
                    <>
                      <Map
                        style={styles.fullMap}
                        initialRegion={{
                          latitude: location.latitude,
                          longitude: location.longitude,
                          latitudeDelta: 0.01,
                          longitudeDelta: 0.01,
                        }}
                        scrollEnabled={true}
                        zoomEnabled={true}
                      >
                        <Marker coordinate={location} />
                      </Map>
                      <View style={styles.mapInfoOverlay}>
                        <Text style={styles.mapInfoText}>
                          📍 {location.latitude.toFixed(6)}, {location.longitude.toFixed(6)}
                        </Text>
                      </View>
                    </>
                  ) : (
                    <View style={styles.fullMapFallback}>
                      <MaterialIcons name="location-on" size={80} color="#4CAF50" />
                      <Text style={styles.fallbackText}>Map not available</Text>
                      <Text style={styles.coordinatesFullText}>
                        {location.latitude.toFixed(6)}, {location.longitude.toFixed(6)}
                      </Text>
                    </View>
                  )}
                </>
              )}
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setShowMapModal(false)}
              >
                <MaterialIcons name="close" size={24} color="#FFF" />
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
        </View>
      </Modal>
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    padding: 10,
    backgroundColor: colors.card,
    borderRadius: 10,
    marginTop: 10,
  },
  inputContainer: {
    marginBottom: 15,
  },
  textInputContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.surface,
    padding: 8,
    borderRadius: 8,
  },
  textInput: {
    flex: 1,
    color: colors.text,
    marginRight: 10,
  },
  cameraButton: {
    backgroundColor: colors.surface,
    padding: 12,
    borderRadius: 50,
    alignItems: "center",
    justifyContent: "center",
    alignSelf: "center",
    flexDirection: "row",
    width: 180,
  },
  cameraButtonText: {
    color: colors.text,
    fontSize: 14,
    fontFamily: "MontserratBold",
    marginLeft: 8,
  },
  previewContainer: {
    alignItems: "center",
  },
  multiplePhotoContainer: {
    alignItems: "center",
    paddingVertical: 10,
  },
  photoCountLabel: {
    color: colors.text,
    fontSize: 14,
    fontFamily: "MontserratBold",
    marginBottom: 12,
    textAlign: "center",
  },
  imagePreview: {
    width: 120,
    height: 120,
    borderRadius: 10,
    marginBottom: 10,
  },
  clearButton: {
    backgroundColor: colors.warning,
    padding: 8,
    borderRadius: 8,
  },
  clearButtonText: {
    color: colors.background,
    fontSize: 12,
    fontFamily: "MontserratBold",
  },
  locationPreview: {
    alignItems: "center",
  },
  mapContainer: {
    width: 120,
    height: 120,
    borderRadius: 10,
    overflow: "hidden",
    marginBottom: 10,
  },
  compactMap: {
    width: "100%",
    height: "100%",
  },
  mapFallback: {
    width: "100%",
    height: "100%",
    backgroundColor: "#f0f0f0",
    alignItems: "center",
    justifyContent: "center",
  },
  coordinatesText: {
    color: colors.textMuted,
    fontSize: 12,
    fontFamily: "Montserrat",
    marginBottom: 10,
  },
  primaryActionButton: {
    backgroundColor: colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 20,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    minWidth: 160,
  },
  primaryActionButtonText: {
    color: "#000",
    fontSize: 14,
    fontFamily: "MontserratBold",
    marginHorizontal: 6,
  },
  disabledButton: {
    opacity: 0.5,
  },
  submitButton: {
    backgroundColor: colors.primary,
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
    marginTop: 10,
  },
  submitButtonText: {
    color: colors.background,
    fontSize: 16,
    fontFamily: "MontserratBold",
  },
  // Gym-specific styles (camera+gps)
  gymContainer: {
    marginBottom: 15,
  },
  stepIndicator: {
    marginBottom: 20,
    alignItems: "center",
    backgroundColor: colors.surface,
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  stepRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  stepItem: {
    alignItems: "center",
    justifyContent: "center",
  },
  stepCircle: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: colors.background,
    borderWidth: 2,
    borderColor: "#999",
    alignItems: "center",
    justifyContent: "center",
  },
  stepCircleActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  stepNumber: {
    color: "#999",
    fontSize: 14,
    fontFamily: "MontserratBold",
  },
  stepNumberActive: {
    color: "#000",
  },
  stepLine: {
    width: 30,
    height: 2,
    backgroundColor: "#999",
    marginHorizontal: 8,
  },
  stepLineActive: {
    backgroundColor: colors.primary,
  },
  stepLabel: {
    color: "#999",
    fontSize: 11,
    fontFamily: "Montserrat",
    textAlign: "center",
    marginTop: 6,
  },
  stepLabelActive: {
    color: colors.text,
    fontFamily: "MontserratBold",
  },
  stepContent: {
    alignItems: "center",
    backgroundColor: colors.surface,
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  stepTitle: {
    fontSize: 16,
    color: colors.text,
    fontFamily: "MontserratBold",
    textAlign: "center",
    marginBottom: 16,
  },
  compactPreview: {
    alignItems: "center",
    width: "100%",
  },
  compactImage: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginBottom: 12,
  },
  imageCount: {
    color: colors.text,
    fontSize: 12,
    fontFamily: "Montserrat",
    marginBottom: 12,
  },
  compactActions: {
    flexDirection: "row",
    justifyContent: "center",
    gap: 12,
    width: "100%",
  },
  compactButton: {
    backgroundColor: colors.background,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 16,
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#ddd",
  },
  compactButtonText: {
    color: "#666",
    fontSize: 12,
    fontFamily: "Montserrat",
    marginHorizontal: 4,
  },
  compactButtonPrimary: {
    backgroundColor: colors.primary,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 16,
    flexDirection: "row",
    alignItems: "center",
  },
  compactButtonPrimaryText: {
    color: "#000",
    fontSize: 12,
    fontFamily: "MontserratBold",
    marginHorizontal: 4,
  },
  compactLocationContainer: {
    alignItems: "center",
    marginBottom: 12,
  },
  compactMapContainer: {
    width: 80,
    height: 80,
    borderRadius: 8,
    overflow: "hidden",
    marginBottom: 6,
  },
  compactMapFallback: {
    width: "100%",
    height: "100%",
    backgroundColor: "#f0f0f0",
    alignItems: "center",
    justifyContent: "center",
  },
  compactCoordinates: {
    color: colors.textMuted,
    fontSize: 10,
    fontFamily: "Montserrat",
    textAlign: "center",
  },
  // Review section styles
  compactReviewContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    width: "100%",
    marginBottom: 16,
    backgroundColor: colors.background,
    borderRadius: 8,
    padding: 12,
  },
  compactReviewItem: {
    alignItems: "center",
    flex: 1,
  },
  compactReviewLabel: {
    color: colors.text,
    fontSize: 12,
    fontFamily: "MontserratBold",
    marginBottom: 8,
    textAlign: "center",
  },
  reviewFullGallery: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "center",
    gap: 8,
  },
  reviewImageItem: {
    position: "relative",
  },
  reviewGalleryImage: {
    width: 50,
    height: 50,
    borderRadius: 6,
  },
  reviewRemoveButton: {
    position: "absolute",
    top: -5,
    right: -5,
    backgroundColor: "#ff6b6b",
    borderRadius: 10,
    width: 20,
    height: 20,
    alignItems: "center",
    justifyContent: "center",
  },
  addMoreImageButton: {
    width: 50,
    height: 50,
    borderRadius: 6,
    backgroundColor: colors.surface,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 1,
    borderColor: "#ddd",
    borderStyle: "dashed",
  },
  addMoreImageText: {
    color: "#666",
    fontSize: 8,
    fontFamily: "Montserrat",
    marginTop: 2,
  },
  compactReviewMapContainer: {
    width: 60,
    height: 60,
    borderRadius: 6,
    overflow: "hidden",
    marginBottom: 4,
  },
  compactReviewMap: {
    width: "100%",
    height: "100%",
  },
  compactReviewMapFallback: {
    width: "100%",
    height: "100%",
    backgroundColor: "#f0f0f0",
    alignItems: "center",
    justifyContent: "center",
  },
  compactReviewCoordinates: {
    color: colors.textMuted,
    fontSize: 8,
    fontFamily: "Montserrat",
    textAlign: "center",
  },
  compactResetButton: {
    backgroundColor: colors.background,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 16,
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#ff6b6b",
  },
  compactResetButtonText: {
    color: "#ff6b6b",
    fontSize: 12,
    fontFamily: "Montserrat",
    marginHorizontal: 4,
  },
  // Modal styles
  imageModalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.9)",
    justifyContent: "center",
    alignItems: "center",
  },
  imageModalBackground: {
    flex: 1,
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  imageModalContainer: {
    flex: 1,
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
    position: "relative",
  },
  fullImage: {
    width: "90%",
    height: "80%",
  },
  closeButton: {
    position: "absolute",
    top: 50,
    right: 20,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    borderRadius: 20,
    padding: 8,
  },
  mapModalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.9)",
    justifyContent: "center",
    alignItems: "center",
  },
  mapModalBackground: {
    flex: 1,
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  mapModalContainer: {
    flex: 1,
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
    position: "relative",
  },
  fullMap: {
    width: "90%",
    height: "80%",
    borderRadius: 10,
  },
  mapInfoOverlay: {
    position: "absolute",
    bottom: 100,
    left: 20,
    right: 20,
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    borderRadius: 8,
    padding: 12,
  },
  mapInfoText: {
    color: "#FFF",
    fontSize: 14,
    fontFamily: "Montserrat",
    textAlign: "center",
  },
  fullMapFallback: {
    width: "90%",
    height: "80%",
    backgroundColor: "#f0f0f0",
    borderRadius: 10,
    alignItems: "center",
    justifyContent: "center",
  },
  fallbackText: {
    color: "#666",
    fontSize: 16,
    fontFamily: "Montserrat",
    marginTop: 10,
  },
  coordinatesFullText: {
    color: "#666",
    fontSize: 12,
    fontFamily: "Montserrat",
    marginTop: 5,
    textAlign: "center",
  },
  // Photo input styles
  photoOptionsContainer: {
    flexDirection: "row",
    justifyContent: "center",
    gap: 16,
    paddingVertical: 10,
    zIndex: 1, // Ensure buttons are above other elements
  },
  photoOptionButton: {
    backgroundColor: colors.primary,
    padding: 12,
    borderRadius: 50,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
    minWidth: 120,
    elevation: 2, // Android shadow
    shadowColor: '#000', // iOS shadow
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  photoOptionButtonText: {
    color: "#000", // Black text for better contrast with primary (yellow) background
    fontSize: 14,
    fontFamily: "MontserratBold",
    marginLeft: 8,
  },
  addMoreOptionsContainer: {
    flexDirection: "row",
    gap: 8,
    justifyContent: "center",
    marginTop: 8,
  },
  addMoreOptionButton: {
    backgroundColor: colors.surface,
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#ddd",
    borderStyle: "dashed",
  },
  addMoreOptionText: {
    color: "#666",
    fontSize: 10,
    fontFamily: "Montserrat",
    marginLeft: 4,
  },
  photoActionsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 10,
    width: "100%",
  },
  retakeOptionsContainer: {
    flexDirection: "row",
    gap: 8,
  },
  retakeOptionButton: {
    backgroundColor: colors.surface,
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#ddd",
  },
  retakeOptionText: {
    color: "#666",
    fontSize: 12,
    fontFamily: "Montserrat",
    marginLeft: 4,
  },
});
