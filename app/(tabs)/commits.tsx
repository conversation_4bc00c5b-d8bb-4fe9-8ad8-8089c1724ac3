// Commits Tab - Create personal commitments and challenge yourself at your own pace

import React, { useCallback, useEffect, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
} from "react-native";
import { MaterialCommunityIcons, MaterialIcons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import Header from "@/components/Header";
import { useTheme } from "@/contexts/ThemeContext";
import { useRenderTiming, useTabSwitchTiming } from "@/utils/performance";
import { firestoreService } from "@/services/database";
import { getId } from "@/utilis/variables";
import { Commit } from "@/services/database/types";

const Commits: React.FC = () => {
  useRenderTiming('CommitsScreen');
  useTabSwitchTiming('Commits');
  const { colors } = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const [commits, setCommits] = useState<Commit[]>([]);

  const router = useRouter();

  // Fetch user's commits from database
  const fetchCommits = useCallback(async () => {
    setIsLoading(true);
    try {
      const userId = await getId();
      if (!userId) {
        console.log("No user ID found");
        setCommits([]);
        return;
      }

      const result = await firestoreService.commits.getCommitsByUser(userId);
      if (result.success && result.data) {
        setCommits(result.data);
        console.log(`Fetched ${result.data.length} commits for user`);
      } else {
        console.error("Error fetching commits:", result.error);
        setCommits([]);
      }
    } catch (error) {
      console.error("Error fetching commits:", error);
      setCommits([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchCommits();
  }, [fetchCommits]);

  const handleCreateCommit = useCallback(() => {
    console.log('Navigating to CustomStake...');
    try {
      router.push('/CustomStake');
    } catch (error) {
      console.error('Navigation error:', error);
      // Fallback - try navigating to a known working page
      router.push('/MyPrograms');
    }
  }, [router]);

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Header />
      <Text style={[styles.headerText, { color: colors.primary }]}>Commits</Text>

      {/* Description */}
      <View style={styles.descriptionContainer}>
        <Text style={[styles.descriptionText, { color: colors.textSecondary }]}>
          Create personal commitments at your own pace.
        </Text>
      </View>

      <ScrollView 
        style={styles.scrollContainer}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Create New Commit Card */}
        <TouchableOpacity
          style={[styles.createCommitCard, { backgroundColor: colors.surface, borderColor: colors.border }]}
          onPress={handleCreateCommit}
          activeOpacity={0.9}
        >
          <View style={styles.createCommitContent}>
            <View style={[styles.createCommitIconContainer, { backgroundColor: colors.primary }]}>
              <MaterialIcons name="add" size={32} color="#000" />
            </View>
            <View style={styles.createCommitTextContainer}>
              <Text style={[styles.createCommitTitle, { color: colors.text }]}>
                Create New Commit
              </Text>
              <Text style={[styles.createCommitSubtitle, { color: colors.textSecondary }]}>
                Set a personal challenge with your own stakes and timeline
              </Text>
            </View>
            <MaterialIcons name="arrow-forward-ios" size={20} color={colors.textSecondary} />
          </View>
        </TouchableOpacity>

        {/* Existing Custom Challenges */}
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
              Loading your commits...
            </Text>
          </View>
        ) : commits.length === 0 ? (
          <View style={styles.emptyStateContainer}>
            <MaterialCommunityIcons
              name="target"
              size={64}
              color={colors.textMuted}
              style={styles.emptyStateIcon}
            />
            <Text style={[styles.emptyStateTitle, { color: colors.text }]}>
              No Commits Yet
            </Text>
            <Text style={[styles.emptyStateSubtitle, { color: colors.textSecondary }]}>
              Create your first personal commitment to get started on your journey of self-improvement!
            </Text>
          </View>
        ) : (
          // Render existing commits
          <View style={styles.commitsContainer}>
            {commits.map((commit) => (
              <View key={commit.id} style={[styles.commitCard, { backgroundColor: colors.surface, borderColor: colors.border }]}>
                <View style={styles.commitHeader}>
                  <Text style={[styles.commitTitle, { color: colors.text }]} numberOfLines={2}>
                    {commit.title || 'Untitled Commitment'}
                  </Text>
                  <View style={[styles.statusBadge, {
                    backgroundColor: commit.status === 'active' ? '#4CAF50' :
                                   commit.status === 'completed' ? '#2196F3' :
                                   commit.status === 'failed' ? '#F44336' : '#9E9E9E'
                  }]}>
                    <Text style={styles.statusText}>
                      {commit.status.charAt(0).toUpperCase() + commit.status.slice(1)}
                    </Text>
                  </View>
                </View>

                {/* Description if available */}
                {commit.description && (
                  <Text style={[styles.commitDescription, { color: colors.textSecondary }]} numberOfLines={2}>
                    {commit.description}
                  </Text>
                )}

                <View style={styles.commitDetails}>
                  {/* Schedule Section */}
                  <View style={styles.detailSection}>
                    <View style={styles.detailRow}>
                      <MaterialIcons name="schedule" size={16} color={colors.textSecondary} />
                      <Text style={[styles.detailText, { color: colors.textSecondary }]}>
                        {(commit.schedule?.frequency || 'Unknown').charAt(0).toUpperCase() +
                         (commit.schedule?.frequency || 'Unknown').slice(1)} reporting
                      </Text>
                    </View>

                    {/* Duration */}
                    {commit.schedule?.duration && (
                      <View style={styles.detailRow}>
                        <MaterialIcons name="timer" size={16} color={colors.textSecondary} />
                        <Text style={[styles.detailText, { color: colors.textSecondary }]}>
                          {commit.schedule.duration} {
                            commit.schedule.frequency === 'daily' ? 'days' :
                            commit.schedule.frequency === 'weekly' ? 'weeks' :
                            commit.schedule.frequency === 'monthly' ? 'months' : 'periods'
                          }
                        </Text>
                      </View>
                    )}

                    {/* Evidence Type */}
                    <View style={styles.detailRow}>
                      <MaterialIcons
                        name={commit.evidence?.type === 'photo' ? 'camera-alt' :
                              commit.evidence?.type === 'video' ? 'videocam' :
                              commit.evidence?.type === 'gps-checkin' ? 'location-on' :
                              commit.evidence?.type === 'strava' ? 'directions-run' :
                              commit.evidence?.type === 'github' ? 'code' :
                              commit.evidence?.type === 'screen-time' ? 'screen-lock-portrait' :
                              commit.evidence?.type === 'honor' ? 'verified-user' : 'assignment'}
                        size={16}
                        color={colors.textSecondary}
                      />
                      <Text style={[styles.detailText, { color: colors.textSecondary }]}>
                        {((commit.evidence?.type || 'Unknown').charAt(0).toUpperCase() +
                          (commit.evidence?.type || 'Unknown').slice(1).replace('-', ' '))} evidence
                      </Text>
                    </View>
                  </View>

                  {/* Financial Section */}
                  <View style={styles.detailSection}>
                    <View style={styles.detailRow}>
                      <MaterialIcons name="attach-money" size={16} color={colors.textSecondary} />
                      <Text style={[styles.detailText, { color: colors.textSecondary }]}>
                        {commit.stake?.paymentRequired === false
                          ? `$${(commit.stake?.totalAtRisk || 0).toFixed(2)} at risk (No payment)`
                          : `$${(commit.stake?.totalAtRisk || 0).toFixed(2)} total at risk`}
                      </Text>
                    </View>

                    <View style={styles.detailRow}>
                      <MaterialIcons
                        name={commit.stake?.destination === 'charity' ? 'favorite' :
                              commit.stake?.destination === 'anti-charity' ? 'cancel' :
                              commit.stake?.destination === 'platform' ? 'stars' :
                              'heart-broken'}
                        size={16}
                        color={colors.textSecondary}
                      />
                      <Text style={[styles.detailText, { color: colors.textSecondary }]}>
                        {commit.stake?.destination === 'none' ? 'No money at stake' :
                         commit.stake?.destination === 'platform' ? 'Platform pool' :
                         (commit.stake?.destination || 'Unknown').charAt(0).toUpperCase() +
                         (commit.stake?.destination || 'Unknown').slice(1).replace('-', ' ')}
                      </Text>
                    </View>
                  </View>

                  {/* Start Date */}
                  {commit.schedule?.startDate && (
                    <View style={styles.detailRow}>
                      <MaterialIcons name="event" size={16} color={colors.textSecondary} />
                      <Text style={[styles.detailText, { color: colors.textSecondary }]}>
                        Started {new Date(commit.schedule.startDate).toLocaleDateString()}
                      </Text>
                    </View>
                  )}

                  {/* End Date */}
                  {commit.schedule?.endDate && (
                    <View style={styles.detailRow}>
                      <MaterialIcons name="event-available" size={16} color={colors.textSecondary} />
                      <Text style={[styles.detailText, { color: colors.textSecondary }]}>
                        Ends {new Date(commit.schedule.endDate).toLocaleDateString()}
                      </Text>
                    </View>
                  )}

                  {/* Timing Type */}
                  {commit.schedule?.deadline && commit.schedule.deadline.type !== 'midnight' && (
                    <View style={styles.detailRow}>
                      <MaterialIcons name="access-time" size={16} color={colors.textSecondary} />
                      <Text style={[styles.detailText, { color: colors.textSecondary }]}>
                        {commit.schedule.deadline.type === 'before' && commit.schedule.deadline.time ? `Before ${commit.schedule.deadline.time}` :
                         commit.schedule.deadline.type === 'after' && commit.schedule.deadline.time ? `After ${commit.schedule.deadline.time}` :
                         commit.schedule.deadline.type === 'between' && commit.schedule.deadline.startTime && commit.schedule.deadline.endTime ?
                         `Between ${commit.schedule.deadline.startTime} - ${commit.schedule.deadline.endTime}` :
                         commit.schedule.deadline.type.charAt(0).toUpperCase() + commit.schedule.deadline.type.slice(1).replace('-', ' ')}
                      </Text>
                    </View>
                  )}

                  {/* Accountability Section */}
                  <View style={styles.detailRow}>
                    <MaterialIcons name="person" size={16} color={colors.textSecondary} />
                    <Text style={[styles.detailText, { color: colors.textSecondary }]}>
                      Referee: {commit.accountability?.referee === 'honor' ? 'Honor system' : (commit.accountability?.referee || 'Unknown')}
                    </Text>
                  </View>

                  {/* Strictness Level */}
                  {commit.accountability?.strictnessLevel && (
                    <View style={styles.detailRow}>
                      <MaterialIcons name="gavel" size={16} color={colors.textSecondary} />
                      <Text style={[styles.detailText, { color: colors.textSecondary }]}>
                        {commit.accountability.strictnessLevel.charAt(0).toUpperCase() +
                         commit.accountability.strictnessLevel.slice(1)} strictness
                      </Text>
                    </View>
                  )}

                  {/* Created Date */}
                  {commit.createdAt && (
                    <View style={styles.detailRow}>
                      <MaterialIcons name="add-circle" size={16} color={colors.textSecondary} />
                      <Text style={[styles.detailText, { color: colors.textSecondary }]}>
                        Created {new Date(
                          typeof commit.createdAt === 'string'
                            ? commit.createdAt
                            : commit.createdAt.toDate()
                        ).toLocaleDateString()}
                      </Text>
                    </View>
                  )}
                </View>
              </View>
            ))}
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor will be set dynamically based on theme
  },
  headerText: {
    // color will be set dynamically based on theme
    fontSize: 22,
    fontFamily: "MontserratBold",
    textAlign: "center",
    marginVertical: 6,
  },
  descriptionContainer: {
    paddingHorizontal: 20,
    marginBottom: 15,
  },
  descriptionText: {
    // color will be set dynamically based on theme
    fontSize: 14,
    fontFamily: "MontserratRegular",
    textAlign: "center",
    lineHeight: 20,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 12,
    paddingBottom: 20,
  },
  createCommitCard: {
    // backgroundColor and borderColor will be set dynamically based on theme
    borderRadius: 15,
    marginVertical: 10,
    padding: 20,
    borderWidth: 1,
    boxShadow: '0 3px 6px rgba(0, 0, 0, 0.2)',
    elevation: 4,
  },
  createCommitContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  createCommitIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 15,
    // backgroundColor will be set dynamically based on theme
  },
  createCommitTextContainer: {
    flex: 1,
  },
  createCommitTitle: {
    // color will be set dynamically based on theme
    fontSize: 16,
    fontFamily: "MontserratBold",
    marginBottom: 4,
  },
  createCommitSubtitle: {
    // color will be set dynamically based on theme
    fontSize: 13,
    fontFamily: "MontserratRegular",
    lineHeight: 18,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 40,
  },
  loadingText: {
    // color will be set dynamically based on theme
    fontSize: 14,
    fontFamily: "MontserratRegular",
    marginTop: 10,
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 60,
    paddingHorizontal: 30,
  },
  emptyStateIcon: {
    marginBottom: 20,
  },
  emptyStateTitle: {
    // color will be set dynamically based on theme
    fontSize: 18,
    fontFamily: "MontserratBold",
    textAlign: "center",
    marginBottom: 10,
  },
  emptyStateSubtitle: {
    // color will be set dynamically based on theme
    fontSize: 14,
    fontFamily: "MontserratRegular",
    textAlign: "center",
    lineHeight: 20,
  },
  commitsContainer: {
    marginTop: 10,
  },
  commitCard: {
    // backgroundColor and borderColor will be set dynamically based on theme
    borderRadius: 15,
    marginVertical: 8,
    padding: 15,
    borderWidth: 1,
    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
    elevation: 2,
  },
  commitHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  commitTitle: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'MontserratBold',
    marginRight: 10,
  },
  commitDescription: {
    fontSize: 14,
    fontStyle: 'italic',
    marginBottom: 8,
    lineHeight: 20,
    opacity: 0.8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    minWidth: 60,
    alignItems: 'center',
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontFamily: 'MontserratMedium',
  },
  commitDetails: {
    gap: 12,
  },
  detailSection: {
    gap: 8,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  detailText: {
    fontSize: 13,
    fontFamily: 'MontserratRegular',
  },
});

export default Commits;
