import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Modal,
  TouchableOpacity,
  ActivityIndicator,
  Platform
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { MaterialCommunityIcons } from '@expo/vector-icons';
// TODO: Migrate rewards to centralized service when rewards module is implemented
import { collection, getDocs, doc, updateDoc } from 'firebase/firestore';
import { db } from '@/config/firebase';
import { getId } from '@/utilis/variables';
import { useTheme } from '@/contexts/ThemeContext';

// Platform-specific blur configuration
const getBlurConfig = () => {
  if (Platform.OS === 'web') {
    return {
      intensity: 15,
      tint: 'default' as const
    };
  } else if (Platform.OS === 'android') {
    return {
      intensity: 15, // Slightly reduced blur for Android
      tint: 'default' as const,
      experimentalBlurMethod: 'dimezisBlurView' as const
    };
  } else {
    // iOS
    return {
      intensity: 100,
      tint: 'systemMaterial' as const
    };
  }
};

interface Reward {
  subtitle: string;
  date: Date;
  status: boolean;
  title: string;
  category: string;
  docId: string; // Add this field
}

const RewardBadge: React.FC<{ reward: Reward }> = ({ reward }) => {
  const { colors, isDark } = useTheme();
  const styles = createStyles(colors);
  const formattedDate = new Date(reward.date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });

  // Get gradient colors based on category and theme
  const getGradientColors = (category: string): [string, string] => {
    switch (category.toLowerCase()) {
      case 'leaderboard':
        return isDark ? ['#FF6B6B', '#4ECDC4'] : ['#FF8A80', '#80CBC4']; // Vibrant gradient for leaderboard
      case 'program':
        return isDark ? ['#4A4A4A', '#2D3436'] : ['#90A4AE', '#607D8B']; // Metallic gradient for program
      default:
        return isDark ? ['#A8E063', '#56AB2F'] : ['#C5E1A5', '#8BC34A']; // Default gradient
    }
  };

  // Get icon based on category
  const getIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case 'leaderboard':
        return <MaterialCommunityIcons name="crown" size={28} color="#FFD700" />;
      case 'program':
        return <MaterialCommunityIcons name="trophy" size={24} color="#B8B8B8" />;
      default:
        return <MaterialCommunityIcons name="medal" size={24} color="#FFF" />;
    }
  };

  const isLeaderboard = reward.category.toLowerCase() === 'leaderboard';
  const isProgram = reward.category.toLowerCase() === 'program';

  return (
    <LinearGradient
      colors={getGradientColors(reward.category)}
      start={{ x: 0, y: 0.2 }}
      end={{ x: 1, y: 0.8 }}
      style={[
        styles.badgeContainer,
        isLeaderboard && styles.leaderboardBadge,
        isProgram && styles.programBadge
      ]}
    >
      <View style={styles.badgeContent}>
        <View style={styles.iconWrapper}>
          {getIcon(reward.category)}
        </View>
        <View style={styles.textWrapper}>
          <Text 
            style={[
              styles.title,
              isLeaderboard && styles.leaderboardTitle,
              isProgram && styles.programTitle
            ]}
            numberOfLines={1}
          >
            {reward.title}
          </Text>
          <Text 
            style={[
              styles.category,

            ]}
            numberOfLines={1}
          >
            {reward.subtitle}
          </Text>
          <Text 
            style={[
              styles.date,

            ]}
          >
            {formattedDate}
          </Text>
        </View>
      </View>
    </LinearGradient>
  );
};

const CollectRewardsModal: React.FC<{
  visible: boolean;
  onClose: () => void;
  onCollect: (rewardId: number) => Promise<void>;
  uncollectedRewards: Reward[];
}> = ({ visible, onClose, onCollect, uncollectedRewards }) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isCollecting, setIsCollecting] = useState(false);

  const handleLater = () => {
    setCurrentIndex(0); // Reset the index
    onClose(); // Close the modal
  };

  const handleNext = () => {
    setCurrentIndex(prev => 
      prev === uncollectedRewards.length - 1 ? prev : prev + 1
    );
  };

  const handlePrevious = () => {
    setCurrentIndex(prev => prev === 0 ? prev : prev - 1);
  };

  const handleCollect = async () => {
    setIsCollecting(true);
    try {
      await onCollect(currentIndex);
      
      // If this was the last reward, close the modal
      if (uncollectedRewards.length === 1) {
        onClose();
        return;
      }

      // If we're collecting the last reward in the list but there are more rewards
      if (currentIndex === uncollectedRewards.length - 1 && currentIndex > 0) {
        setCurrentIndex(currentIndex - 1);
      }
    } catch (error) {
      console.error('Error collecting reward:', error);
    } finally {
      setIsCollecting(false);
    }
  };

  // Don't render the modal if there are no uncollected rewards
  if (uncollectedRewards.length === 0) {
    return null;
  }

  const currentReward = uncollectedRewards[currentIndex];
  const hasMultipleRewards = uncollectedRewards.length > 1;

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={handleLater}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>New Rewards Available!</Text>
          {hasMultipleRewards && (
            <Text style={styles.rewardCounter}>
              {currentIndex + 1} of {uncollectedRewards.length}
            </Text>
          )}
          
          <View style={styles.navigationContainer}>
            {hasMultipleRewards && (
              <TouchableOpacity 
                style={[
                  styles.navButton,
                  (currentIndex === 0 || isCollecting) && styles.navButtonDisabled
                ]}
                onPress={handlePrevious}
                disabled={currentIndex === 0 || isCollecting}
              >
                <MaterialCommunityIcons 
                  name="chevron-left" 
                  size={30} 
                  color={currentIndex === 0 ? '#666' : '#FFEB3B'} 
                />
              </TouchableOpacity>
            )}

            <View style={[
              styles.modalBadgeContainer,
              !hasMultipleRewards && styles.singleBadgeContainer
            ]}>
              <RewardBadge reward={currentReward} />
              <TouchableOpacity 
                style={[
                  styles.collectSingleButton,
                  !hasMultipleRewards && styles.singleCollectButton,
                  isCollecting && styles.disabledButton
                ]}
                onPress={handleCollect}
                disabled={isCollecting}
              >
                {isCollecting ? (
                  <ActivityIndicator size="small" color="#000" />
                ) : (
                  <Text style={[
                    styles.collectButtonText,
                    !hasMultipleRewards && styles.singleCollectButtonText
                  ]}>
                    Collect
                  </Text>
                )}
              </TouchableOpacity>
            </View>

            {hasMultipleRewards && (
              <TouchableOpacity 
                style={[
                  styles.navButton,
                  (currentIndex === uncollectedRewards.length - 1 || isCollecting) && styles.navButtonDisabled
                ]}
                onPress={handleNext}
                disabled={currentIndex === uncollectedRewards.length - 1 || isCollecting}
              >
                <MaterialCommunityIcons 
                  name="chevron-right" 
                  size={30} 
                  color={currentIndex === uncollectedRewards.length - 1 ? '#666' : '#FFEB3B'} 
                />
              </TouchableOpacity>
            )}
          </View>

          <TouchableOpacity 
            style={[
              styles.laterButton,
              isCollecting && styles.disabledButton
            ]}
            onPress={handleLater}
            disabled={isCollecting}
          >
            <Text style={styles.laterButtonText}>Later</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const Rewards = () => {
  const { colors } = useTheme();
  const [showModal, setShowModal] = useState(true);
  const [rewards, setRewards] = useState<(Reward & { docId: string })[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchRewards = async () => {
      try {
        const userId = await getId();
        if (!userId) return;

        const rewardsRef = collection(db, `users/${userId}/rewards`);
        const rewardsSnapshot = await getDocs(rewardsRef);
        
        const fetchedRewards = rewardsSnapshot.docs.map(doc => ({
          docId: doc.id, // Store the document ID
          subtitle: doc.data().subtitle,
          date: new Date(doc.data().date.toDate()),
          status: doc.data().status,
          title: doc.data().title,
          category: doc.data().category
        }));

        setRewards(fetchedRewards);
      } catch (error) {
        console.error('Error fetching rewards:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchRewards();
  }, []);

  const handleCollectReward = async (rewardId: number) => {
    try {
      const userId = await getId();
      if (!userId) return;

      const rewardToUpdate = uncollectedRewards[rewardId];
      if (!rewardToUpdate) return;

      const rewardRef = doc(db, `users/${userId}/rewards/${rewardToUpdate.docId}`);
      
      await updateDoc(rewardRef, {
        status: true
      });

      // Update local state by finding the reward in the original rewards array
      setRewards(prevRewards => 
        prevRewards.map(reward => 
          reward.docId === rewardToUpdate.docId 
            ? { ...reward, status: true } 
            : reward
        )
      );

      // If this was the last uncollected reward, close the modal
      if (uncollectedRewards.length === 1) {
        setShowModal(false);
      }
    } catch (error) {
      console.error('Error updating reward status:', error);
      throw error;
    }
  };

  const collectedRewards = rewards.filter(reward => reward.status);
  const uncollectedRewards = rewards.filter(reward => !reward.status);

  const renderRewardItem = ({ item, index }: { item: Reward; index: number }) => (
    <View
      style={[
        styles.badgeWrapper,
        index === 0 && styles.firstBadge,
        index === collectedRewards.length - 1 && styles.lastBadge,
      ]}
    >
      <RewardBadge reward={item} />
    </View>
  );

  const styles = createStyles(colors);

  if (loading) {
    return (
      <View style={styles.container}>
        <Text style={styles.sectionTitle}>Badges</Text>
        <View style={styles.contentContainer}>
          <Text style={styles.noRewardsText}>Loading badges...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>Badges</Text>
      <View style={styles.contentWrapper}>
        <View style={styles.contentContainer}>
          {collectedRewards.length > 0 ? (
            <FlatList
              data={collectedRewards}
              renderItem={renderRewardItem}
              keyExtractor={(item) => item.docId}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.scrollContent}
              removeClippedSubviews={true}
              maxToRenderPerBatch={5}
              windowSize={5}
              initialNumToRender={5}
            />
          ) : (
            <Text style={styles.noRewardsText}>No badges collected yet</Text>
          )}
        </View>

        {/* Coming Soon Overlay */}
        <BlurView
          intensity={getBlurConfig().intensity}
          tint={getBlurConfig().tint}
          {...(Platform.OS !== 'web' && { experimentalBlurMethod: getBlurConfig().experimentalBlurMethod })}
          style={styles.comingSoonOverlay}
        >
          <View style={styles.comingSoonContent}>
            <View style={styles.comingSoonLabel}>
              <LinearGradient
                colors={['#FFD700', '#DAA520', '#B8860B']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.goldBorder}
              >
                <View style={styles.labelInner}>
                  <Text style={styles.comingSoonLabelText}>
                    COMING SOON
                  </Text>
                </View>
              </LinearGradient>
            </View>
            <View style={[styles.descriptionBackground, { backgroundColor: colors.background }]}>
              <Text style={[styles.comingSoonDescription, { color: colors.textSecondary }]}>
                Collect unique NFT badges as digital collectibles
              </Text>
            </View>
          </View>
        </BlurView>
      </View>

      <CollectRewardsModal
        visible={showModal && uncollectedRewards.length > 0}
        onClose={() => setShowModal(false)}
        onCollect={handleCollectReward}
        uncollectedRewards={uncollectedRewards}
      />
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    backgroundColor: colors.card,
    borderRadius: 10,
    padding: 12, // Reduced from 16
    marginHorizontal: 10,
  },
  sectionTitle: {
    color: '#FFFFFF', // Changed to white
    fontSize: 18,
    fontFamily: 'MontserratBold',
    textAlign: 'center',
    marginBottom: 12, // Reduced from 16
  },
  contentContainer: {
    height: 120, // Reduced from 160
  },
  scrollContent: {
    paddingHorizontal: 4,
  },
  badgeWrapper: {
    marginHorizontal: 4,
  },
  firstBadge: {
    marginLeft: 0,
  },
  lastBadge: {
    marginRight: 0,
  },
  badgeContainer: {
    width: 100,
    height: 110, // Reduced from 140
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 5,
    boxShadow: '0 2px 3.84px rgba(0, 0, 0, 0.25)',
  },
  leaderboardBadge: {
    borderWidth: 1,
    borderColor: colors.warning,
    elevation: 8,
  },
  programBadge: {
    borderWidth: 1,
    borderColor: colors.border,
    elevation: 4,
  },
  badgeContent: {
    flex: 1,
    padding: 6, // Reduced from 8
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  iconWrapper: {
    height: 28, // Reduced from 32
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 4, // Added small margin
  },
  textWrapper: {
    flex: 1,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 2, // Reduced from 4
  },
  title: {
    color: '#FFFFFF', // Changed to white
    fontSize: 11, // Reduced from 12
    fontFamily: 'MontserratBold',
    textTransform: 'uppercase',
    textAlign: 'center',
    marginBottom: 2, // Added small margin
  },
  leaderboardTitle: {
    fontSize: 13, // Reduced from 14
    color: '#FFFFFF', // Changed to white
    ...(Platform.OS === 'web' && { textShadow: '1px 1px 2px rgba(0, 0, 0, 0.75)' }),
  },
  programTitle: {
    fontSize: 11, // Reduced from 12
    color: '#FFFFFF', // Changed to white
    ...(Platform.OS === 'web' && { textShadow: '0.5px 0.5px 1px rgba(0, 0, 0, 0.4)' }),
  },
  category: {
    color: '#FFFFFF', // Changed to white
    fontSize: 9, // Reduced from 10
    fontFamily: 'MontserratRegular',
    opacity: 0.9,
    textAlign: 'center',
    marginBottom: 2, // Added small margin
  },
  date: {
    color: '#FFFFFF', // Changed to white
    fontSize: 8, // Reduced from 9
    fontFamily: 'MontserratRegular',
    opacity: 0.8,
  },
  noRewardsText: {
    color: colors.textMuted,
    fontSize: 14,
    fontStyle: 'italic',
    paddingVertical: 16, // Reduced from 20
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },

  modalContent: {
    backgroundColor: colors.card,
    borderRadius: 15,
    padding: 20,
    width: '90%',
    maxHeight: '80%',
  },

  modalTitle: {
    color: colors.primary,
    fontSize: 20,
    fontFamily: 'MontserratBold',
    textAlign: 'center',
    marginBottom: 5,
  },
  modalScroll: {
    maxHeight: '80%',
  },
  modalBadgeContainer: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 15,
    padding: 15,
    flex: 1,
    marginHorizontal: 10,
    transform: [{ scale: 1.2 }],
  },

  collectSingleButton: {
    backgroundColor: colors.primary,
    paddingVertical: 6, // Reduced from 8
    paddingHorizontal: 12, // Reduced from 16
    borderRadius: 8,
    marginTop: 10,
    width: '60%', // Reduced from 80%
    alignItems: 'center',
    alignSelf: 'center',
  },
  singleCollectButton: {
    width: '40%', // Reduced from 60%
    paddingVertical: 4, // Reduced from 6
    paddingHorizontal: 8, // Reduced from 12
  },
  collectButtonText: {
    color: colors.background,
    fontSize: 12, // Reduced from 14
    fontFamily: 'MontserratBold',
  },
  singleCollectButtonText: {
    fontSize: 10, // Reduced from 12
  },
  modalButton: {
    padding: 10, // Reduced from 15
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 10,
    alignSelf: 'center', // Center the button horizontally
    minWidth: 100, // Add minimum width for consistency
  },
  laterButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: colors.primary,
    paddingVertical: 8,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginTop: 15,
    alignSelf: 'center', // Center the button
    minWidth: 100, // Ensure minimum width
  },
  laterButtonText: {
    color: colors.primary,
    fontSize: 14,
    fontFamily: 'MontserratBold',
    textAlign: 'center',
  },
  navigationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 10,
    marginBottom: 20,
  },

  navButton: {
    padding: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  navButtonDisabled: {
    opacity: 0.5,
  },
  rewardCounter: {
    color: colors.text,
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    textAlign: 'center',
    marginBottom: 20,
  },
  singleBadgeContainer: {
    marginHorizontal: 0, // Remove horizontal margin when there's only one badge
  },
  disabledButton: {
    opacity: 0.5,
    borderColor: colors.textMuted,
  },
  contentWrapper: {
    position: "relative",
  },
  comingSoonOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 12,
    overflow: "hidden",
  },
  comingSoonContent: {
    alignItems: "center",
    padding: 16,
  },
  comingSoonLabel: {
    marginBottom: 8,
    shadowColor: "#FFD700",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
    transform: [{ rotate: '-0.5deg' }],
  },
  goldBorder: {
    borderRadius: 12,
    padding: 1.5,
  },
  labelInner: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 9,
    backgroundColor: '#1a1a1a',
    borderWidth: 0,
  },
  comingSoonLabelText: {
    fontSize: 9,
    fontFamily: "MontserratBold",
    fontWeight: "700",
    letterSpacing: 0.8,
    color: '#FFD700',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0.3, height: 0.3 },
    textShadowRadius: 0.5,
  },
  descriptionBackground: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    marginTop: 6,
    borderWidth: 1,
    borderColor: 'rgba(218, 165, 32, 0.2)',
  },
  comingSoonDescription: {
    fontSize: 12,
    textAlign: "center",
    fontFamily: "Montserrat",
  },
});

export default Rewards;








