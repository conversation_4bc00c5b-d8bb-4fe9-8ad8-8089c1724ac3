/**
 * Performance monitoring utilities for React Native app
 */
import React from 'react';

interface PerformanceMetric {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, any>;
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map();
  private isEnabled: boolean = __DEV__; // Only enable in development

  /**
   * Start timing a performance metric
   */
  startTiming(name: string, metadata?: Record<string, any>): void {
    if (!this.isEnabled) return;

    const metric: PerformanceMetric = {
      name,
      startTime: Date.now(),
      metadata,
    };

    this.metrics.set(name, metric);
    console.log(`🚀 Performance: Started timing "${name}"`);
  }

  /**
   * End timing a performance metric
   */
  endTiming(name: string): number | null {
    if (!this.isEnabled) return null;

    const metric = this.metrics.get(name);
    if (!metric) {
      console.warn(`⚠️ Performance: No timing found for "${name}"`);
      return null;
    }

    const endTime = Date.now();
    const duration = endTime - metric.startTime;

    metric.endTime = endTime;
    metric.duration = duration;

    console.log(`✅ Performance: "${name}" took ${duration}ms`);

    // Log slow operations
    if (duration > 1000) {
      console.warn(`🐌 Performance: Slow operation "${name}" took ${duration}ms`);
    }

    return duration;
  }

  /**
   * Measure the execution time of an async function
   */
  async measureAsync<T>(
    name: string,
    fn: () => Promise<T>,
    metadata?: Record<string, any>
  ): Promise<T> {
    if (!this.isEnabled) return fn();

    this.startTiming(name, metadata);
    try {
      const result = await fn();
      this.endTiming(name);
      return result;
    } catch (error) {
      this.endTiming(name);
      console.error(`❌ Performance: Error in "${name}":`, error);
      throw error;
    }
  }

  /**
   * Measure the execution time of a synchronous function
   */
  measure<T>(
    name: string,
    fn: () => T,
    metadata?: Record<string, any>
  ): T {
    if (!this.isEnabled) return fn();

    this.startTiming(name, metadata);
    try {
      const result = fn();
      this.endTiming(name);
      return result;
    } catch (error) {
      this.endTiming(name);
      console.error(`❌ Performance: Error in "${name}":`, error);
      throw error;
    }
  }

  /**
   * Get all recorded metrics
   */
  getMetrics(): PerformanceMetric[] {
    return Array.from(this.metrics.values());
  }

  /**
   * Get metrics summary
   */
  getSummary(): Record<string, { count: number; totalTime: number; avgTime: number }> {
    const summary: Record<string, { count: number; totalTime: number; avgTime: number }> = {};

    this.metrics.forEach((metric) => {
      if (metric.duration) {
        if (!summary[metric.name]) {
          summary[metric.name] = { count: 0, totalTime: 0, avgTime: 0 };
        }
        summary[metric.name].count++;
        summary[metric.name].totalTime += metric.duration;
        summary[metric.name].avgTime = summary[metric.name].totalTime / summary[metric.name].count;
      }
    });

    return summary;
  }

  /**
   * Clear all metrics
   */
  clear(): void {
    this.metrics.clear();
  }

  /**
   * Log performance summary
   */
  logSummary(): void {
    if (!this.isEnabled) return;

    const summary = this.getSummary();
    console.log('📊 Performance Summary:');
    Object.entries(summary).forEach(([name, stats]) => {
      console.log(`  ${name}: ${stats.count} calls, avg ${stats.avgTime.toFixed(2)}ms`);
    });
  }
}

// Create singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Convenience functions
export const startTiming = (name: string, metadata?: Record<string, any>) => 
  performanceMonitor.startTiming(name, metadata);

export const endTiming = (name: string) => 
  performanceMonitor.endTiming(name);

export const measureAsync = <T>(
  name: string,
  fn: () => Promise<T>,
  metadata?: Record<string, any>
) => performanceMonitor.measureAsync(name, fn, metadata);

export const measure = <T>(
  name: string,
  fn: () => T,
  metadata?: Record<string, any>
) => performanceMonitor.measure(name, fn, metadata);

// React hook for component render timing
export const useRenderTiming = (componentName: string) => {
  if (__DEV__) {
    const renderStart = Date.now();
    
    // Use useEffect to measure render time
    React.useEffect(() => {
      const renderTime = Date.now() - renderStart;
      if (renderTime > 16) { // More than one frame (60fps)
        console.log(`🎨 Render: ${componentName} took ${renderTime}ms`);
      }
    });
  }
};

// Database query performance wrapper
export const measureDatabaseQuery = async <T>(
  queryName: string,
  queryFn: () => Promise<T>
): Promise<T> => {
  return measureAsync(`DB_${queryName}`, queryFn, { type: 'database' });
};

// Image loading performance wrapper
export const measureImageLoad = (imageName: string) => {
  const startTime = Date.now();
  return {
    onLoadEnd: () => {
      const loadTime = Date.now() - startTime;
      if (loadTime > 500) { // Log slow image loads
        console.log(`🖼️ Image: ${imageName} loaded in ${loadTime}ms`);
      }
    }
  };
};

// Tab switching performance hook
export const useTabSwitchTiming = (tabName: string) => {
  if (__DEV__) {
    const switchStart = Date.now();

    React.useEffect(() => {
      const switchTime = Date.now() - switchStart;
      if (switchTime > 100) { // Log slow tab switches
        console.log(`🔄 Tab Switch: ${tabName} took ${switchTime}ms`);
      }
    });
  }
};

export default performanceMonitor;
