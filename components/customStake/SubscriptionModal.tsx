import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  ScrollView,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';

interface SubscriptionModalProps {
  visible: boolean;
  onClose: () => void;
  onSubscribe: () => void;
  featureName: string;
}

export const SubscriptionModal: React.FC<SubscriptionModalProps> = ({
  visible,
  onClose,
  onSubscribe,
  featureName,
}) => {
  const { colors } = useTheme();

  const premiumFeatures = [
    { icon: 'sms', title: 'SMS Reminders', description: 'Never miss a commitment with text alerts' },
    { icon: 'chat', title: 'WhatsApp Notifications', description: 'Get reminders on your favorite messaging app' },
    { icon: 'support-agent', title: 'Priority Support', description: 'Get help faster when you need it' },
    { icon: 'analytics', title: 'Advanced Analytics', description: 'Deep insights into your progress and patterns' },
    { icon: 'schedule', title: 'Custom Notification Times', description: 'Set reminders at your perfect times' },
    { icon: 'event', title: 'Calendar Integration', description: 'Sync commitments with your calendar' },
    { icon: 'star', title: 'Unlimited Commitments', description: 'Create as many goals as you want' },
    { icon: 'shield', title: 'Advanced Grace Days', description: 'More flexibility with smart grace day management' },
  ];

  const styles = {
    overlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
      padding: 20,
    },
    container: {
      backgroundColor: colors.background,
      borderRadius: 12, // Match app's standard border radius
      padding: 20, // Match app's standard padding
      width: '100%',
      maxWidth: 400,
      maxHeight: '80%',
    },
    header: {
      alignItems: 'center' as const,
      marginBottom: 24,
    },
    closeButton: {
      position: 'absolute' as const,
      top: 0,
      right: 0,
      padding: 8,
    },
    title: {
      fontSize: 20, // Match app's header title size
      fontFamily: 'MontserratBold',
      color: colors.text,
      textAlign: 'center' as const,
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      fontFamily: 'MontserratRegular',
      color: colors.textSecondary,
      textAlign: 'center' as const,
      lineHeight: 22,
    },
    priceSection: {
      backgroundColor: colors.surface,
      borderRadius: 8, // Match app's standard border radius
      padding: 20,
      marginBottom: 20, // Consistent spacing
      alignItems: 'center' as const,
      borderWidth: 1, // Thinner border
      borderColor: colors.primary,
    },
    priceText: {
      fontSize: 32,
      fontFamily: 'MontserratBold',
      color: colors.primary,
      marginBottom: 4,
    },
    priceSubtext: {
      fontSize: 14,
      fontFamily: 'MontserratRegular',
      color: colors.textSecondary,
    },
    featuresTitle: {
      fontSize: 18,
      fontFamily: 'MontserratSemiBold',
      color: colors.text,
      marginBottom: 16,
      textAlign: 'center' as const,
    },
    featuresList: {
      marginBottom: 24,
    },
    featureItem: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      marginBottom: 12,
    },
    featureIcon: {
      marginRight: 12,
    },
    featureContent: {
      flex: 1,
    },
    featureTitle: {
      fontSize: 14,
      fontFamily: 'MontserratSemiBold',
      color: colors.text,
      marginBottom: 2,
    },
    featureDescription: {
      fontSize: 12,
      fontFamily: 'MontserratRegular',
      color: colors.textSecondary,
      lineHeight: 16,
    },
    buttonContainer: {
      gap: 12,
    },
    subscribeButton: {
      backgroundColor: colors.primary, // App's standard gold color
      borderRadius: 8, // Match app's standard border radius
      paddingVertical: 18, // Match submit button padding
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      shadowColor: colors.primary,
      shadowOffset: {
        width: 0,
        height: 3,
      },
      shadowOpacity: 0.3,
      shadowRadius: 6,
      elevation: 8,
    },
    subscribeButtonText: {
      fontSize: 18, // Match submit button text size
      fontFamily: 'MontserratBold',
      color: '#000000', // Black text for consistency
      letterSpacing: 0.8, // Match submit button letter spacing
    },
    cancelButton: {
      backgroundColor: colors.surface,
      borderRadius: 8, // Match app's standard border radius
      paddingVertical: 18, // Match submit button padding
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      borderWidth: 1,
      borderColor: colors.border,
    },
    cancelButtonText: {
      fontSize: 18, // Match submit button text size
      fontFamily: 'MontserratBold', // Use Bold for consistency
      color: colors.text,
      letterSpacing: 0.8, // Match submit button letter spacing
    },
    highlight: {
      color: colors.primary,
      fontFamily: 'MontserratBold',
    },
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.container}>
          <View style={styles.header}>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <MaterialIcons name="close" size={24} color={colors.text} />
            </TouchableOpacity>
            <Text style={styles.title}>Unlock Premium Features</Text>
            <Text style={styles.subtitle}>
              You're trying to access <Text style={styles.highlight}>{featureName}</Text>
            </Text>
          </View>

          <View style={styles.priceSection}>
            <Text style={styles.priceText}>$10</Text>
            <Text style={styles.priceSubtext}>per month</Text>
          </View>

          <Text style={styles.featuresTitle}>What you get with Premium:</Text>
          
          <ScrollView style={styles.featuresList} showsVerticalScrollIndicator={false}>
            {premiumFeatures.map((feature, index) => (
              <View key={index} style={styles.featureItem}>
                <MaterialIcons
                  name={feature.icon as any}
                  size={20}
                  color={colors.primary}
                  style={styles.featureIcon}
                />
                <View style={styles.featureContent}>
                  <Text style={styles.featureTitle}>{feature.title}</Text>
                  <Text style={styles.featureDescription}>{feature.description}</Text>
                </View>
              </View>
            ))}
          </ScrollView>

          <View style={styles.buttonContainer}>
            <TouchableOpacity style={styles.subscribeButton} onPress={onSubscribe}>
              <Text style={styles.subscribeButtonText}>Start Premium - $10/month</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
              <Text style={styles.cancelButtonText}>Maybe Later</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};
