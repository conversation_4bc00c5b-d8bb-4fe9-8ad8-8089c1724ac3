import React, { useCallback, useEffect, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
  Alert,
  BackHandler,
  Image,
} from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { firestoreService } from "../services/database";
import { MaterialCommunityIcons, FontAwesome5 } from "@expo/vector-icons";
import { getId, getFname } from "@/utilis/variables";
import FAQSection from "@/components/pageFunctions/Explore/FAQSection";
import OctagonStamp from "@/components/pageFunctions/Explore/OctagonStamp";
import { ProtectedRoute } from "@/components/ProtectedRoute";

import { useTheme } from "@/contexts/ThemeContext";

interface Program {
  id: string;
  name: string;
  description: string;
  duration: number | string;
  startDate: string;
  endDate: string;
  betAmount: number;
  participantsCount: number;
  registrationsOpen: boolean;
  status: "upcoming" | "ongoing" | "ended";
  category: string;
  moderator: string;
  createdAt: string;
  createdBy: string;
  defaultLives: number;
  maxLivePurchase: number;
  headline: string;
  image?: {
    url: string;
    alt: string;
  };
}

// Create category icons function that uses theme colors
const getCategoryIcons = (primaryColor: string): Record<string, JSX.Element> => ({
  gym: (
    <MaterialCommunityIcons name="weight-lifter" size={60} color={primaryColor} />
  ),
  cardio: <FontAwesome5 name="running" size={60} color={primaryColor} />,
  coding: <MaterialCommunityIcons name="laptop" size={60} color={primaryColor} />,
  journaling: <FontAwesome5 name="book-open" size={60} color={primaryColor} />,
  affirmations: (
    <MaterialCommunityIcons name="message-text" size={60} color={primaryColor} />
  ),
  writing: <MaterialCommunityIcons name="pencil" size={60} color={primaryColor} />,
});

const ProgramDetailsComponent: React.FC = () => {
  const { colors, isDark } = useTheme();
  const categoryIcons = getCategoryIcons(colors.primary);
  const styles = createStyles(colors, isDark);

  const { programId, paymentCompleted } = useLocalSearchParams<{
    programId?: string;
    paymentCompleted?: string;
  }>();
  const router = useRouter();
  const [program, setProgram] = useState<Program | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [signUpLoading, setSignUpLoading] = useState<boolean>(false);
  const [userId, setUserId] = useState<string>("");
  const [isUserSignedUp, setIsUserSignedUp] = useState<boolean>(false);



  // Combined fetching of userId and program data
  useEffect(() => {
    const fetchData = async () => {
      try {
        const id = String(await getId());
        setUserId(id);
        if (!programId) return;
        setLoading(true);

        const result = await firestoreService.programs.getProgramById(programId);
        if (result.success && result.data) {
          setProgram(result.data as Program);
        } else {
          Alert.alert("Error", "Program not found");
          router.back();
        }
      } catch (error) {
        console.error("Error fetching program:", error);
        Alert.alert("Error", "Something went wrong");
        router.back();
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [programId, router]);

  // Check participant status once userId & programId are available
  useEffect(() => {
    const checkParticipant = async () => {
      if (!userId || !programId) return;
      const result = await firestoreService.participants.isParticipantEnrolled(programId, userId);
      if (result.success) {
        setIsUserSignedUp(result.data || false);
      }
    };
    checkParticipant();
  }, [userId, programId]);

  // Prevent hardware back press during sign-up
  useEffect(() => {
    const onBackPress = () => signUpLoading;
    const backHandler = BackHandler.addEventListener(
      "hardwareBackPress",
      onBackPress
    );
    return () => backHandler.remove();
  }, [signUpLoading]);

  const formatDate = useCallback((dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      day: "numeric",
      month: "short",
      year: "numeric",
    });
  }, []);

  const handleSignUp = useCallback(async () => {
    if (!program || !userId) return;
    setSignUpLoading(true);

    try {
      const result = await firestoreService.enrollUserInProgram(userId, program.id, {
        fname: (await getFname()) || 'User',
        paymentDone: paymentCompleted === 'true'
      });

      if (result.success) {
        Alert.alert(
          "Success!",
          "You have successfully signed up for the program.",
          [
            {
              text: "Go to Progress",
              onPress: () =>
                router.replace(
                  `/progress?selectedProgramId=${program.id}&t=${Date.now()}`
                ),
            },
          ]
        );
      } else {
        Alert.alert("Error", result.error || "Failed to sign up for program");
      }
    } catch (error) {
      console.error("Error signing up for program:", error);
      Alert.alert("Error", "Failed to sign up. Please try again.");
    } finally {
      setSignUpLoading(false);
    }
  }, [program, userId, paymentCompleted, router]);

  const handleBeforeSignUp = () => {
    // Check if payment was already completed (coming back from payment screen)
    if (paymentCompleted === 'true') {
      handleSignUp();
    } else {
      // Direct redirect to payment confirmation page
      router.push(`/PaymentConfirmation?programId=${program?.id}`);
    }
  };

  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  if (!program) return null;

  const weeks = Math.ceil(Number(program.duration) / 7);

  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
      {program.registrationsOpen && <OctagonStamp />}

      {/* PROGRAM IMAGE BANNER */}
      {program.image?.url && (
        <Image
          source={{ uri: program.image.url }}
          style={styles.programBanner}
          resizeMode="cover"
          loadingIndicatorSource={require('@/assets/images/icon.png')}
          fadeDuration={200}
        />
      )}

      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <View style={styles.headerContent}>
          <View style={styles.titleWithIcon}>
            {categoryIcons[program.category] || (
              <MaterialCommunityIcons name="tag" size={40} color={colors.primary} />
            )}
            <Text style={[styles.programTitle, { color: colors.text }]}>{program.name}</Text>
          </View>
          <View style={styles.programInfoContainer}>
            <Text style={[styles.programInfoText, { color: colors.textSecondary }]}>Created by </Text>
            <TouchableOpacity
              onPress={() => console.log("Creator pressed")}
              style={styles.creatorButton}
            >
              <Text style={[styles.creatorButtonText, { color: colors.primary }]}>{program.createdBy}</Text>
            </TouchableOpacity>
            <Text style={[styles.programInfoText, { color: colors.textSecondary }]}> | Moderated by </Text>
            <TouchableOpacity
              onPress={() => console.log("Moderator pressed")}
              style={styles.moderatorButton}
            >
              <Text style={[styles.moderatorButtonText, { color: colors.primary }]}>
                {program.moderator}
              </Text>
            </TouchableOpacity>
          </View>
          <View style={styles.pledgePoolContainer}>
            <View style={styles.pledgeContainer}>
              <Text style={[styles.pledgeLabel, { color: colors.textMuted }]}>Pledge Amount</Text>
              <Text style={[styles.pledgeValue, { color: colors.text }]}>${program.betAmount}</Text>
            </View>
            <View style={styles.participantsContainer}>
              <Text style={[styles.participantsLabel, { color: colors.textMuted }]}>Participants</Text>
              <Text style={[styles.participantsValue, { color: colors.text }]}>
                {program.participantsCount}
              </Text>
            </View>
            <View style={styles.poolContainer}>
              <Text style={[styles.poolLabel, { color: colors.textMuted }]}>Pool Amount</Text>
              <Text style={[styles.poolValue, { color: colors.text }]}>
                ${program.participantsCount * program.betAmount}
              </Text>
            </View>
          </View>
          <Text style={[styles.programHeadline, { color: colors.textSecondary }]}>{program.headline}</Text>
        </View>
      </View>
      <View style={[styles.detailsCard, { backgroundColor: colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Challenge Details</Text>
        <Text style={[styles.fullWidthText, { color: colors.textSecondary }]}>{program.description}</Text>
        <View style={[styles.divider, { backgroundColor: colors.separator }]} />
        <View style={styles.dateInfoContainer}>
          <View style={styles.dateItem}>
            <Text style={[styles.dateLabel, { color: colors.textMuted }]}>Duration</Text>
            <Text style={[styles.dateValue, { color: colors.text }]}>{weeks} Weeks</Text>
          </View>
          <View style={[styles.verticalDivider, { backgroundColor: colors.separator }]} />
          <View style={styles.dateItem}>
            <Text style={[styles.dateLabel, { color: colors.textMuted }]}>Start Date</Text>
            <Text style={[styles.dateValue, { color: colors.text }]}>
              {formatDate(program.startDate)}
            </Text>
          </View>
          <View style={styles.dateItem}>
            <Text style={[styles.dateLabel, { color: colors.textMuted }]}>End Date</Text>
            <Text style={[styles.dateValue, { color: colors.text }]}>{formatDate(program.endDate)}</Text>
          </View>
        </View>
        <View style={[styles.divider, { backgroundColor: colors.separator }]} />
        <View style={styles.dateInfoContainer}>
          <View style={styles.dateItem}>
            <Text style={[styles.dateLabel, { color: colors.textMuted }]}>Default Lives</Text>
            <Text style={[styles.dateValue, { color: colors.text }]}>{program.defaultLives}</Text>
          </View>
          <View style={[styles.verticalDivider, { backgroundColor: colors.separator }]} />
          <View style={styles.dateItem}>
            <Text style={[styles.dateLabel, { color: colors.textMuted }]}>Max Lives Purchase</Text>
            <Text style={[styles.dateValue, { color: colors.text }]}>{program.maxLivePurchase}</Text>
          </View>
        </View>
      </View>
      <FAQSection
        category={program.category}
        betAmount={program.betAmount}
        startDate={program.startDate}
        endDate={program.endDate}
      />
      {program.registrationsOpen ? (
        !isUserSignedUp ? (
          <TouchableOpacity
            style={[styles.ctaButton, { backgroundColor: colors.primary }]}
            // onPress={handleSignUp}
            onPress={handleBeforeSignUp}
            disabled={signUpLoading}
          >
            {signUpLoading ? (
              <ActivityIndicator size="small" color="#000" />
            ) : (
              <Text style={styles.ctaButtonText}>Join the Challenge</Text>
            )}
          </TouchableOpacity>
        ) : (
          <View style={[styles.joinedMessageContainer, { backgroundColor: colors.success }]}>
            <Text style={styles.joinedMessageText}>Already Joined</Text>
          </View>
        )
      ) : (
        <View style={[styles.closedContainer, { backgroundColor: colors.error }]}>
          <Text style={styles.closedText}>Registration Closed</Text>
        </View>
      )}
      <TouchableOpacity
        style={[styles.backButton, { backgroundColor: colors.surface, borderColor: colors.border }]}
        onPress={() => {
          if (!signUpLoading) {
            router.back();
          }
        }}
        disabled={signUpLoading}
      >
        <Text style={[styles.backButtonText, { color: colors.text }]}>Go Back</Text>
      </TouchableOpacity>

    </ScrollView>
  );
};

const createStyles = (colors: any, isDark: boolean) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.background
  },
  header: {
    backgroundColor: colors.surface,
    paddingTop: 40,
    paddingBottom: 12,
    paddingHorizontal: 20,
    alignItems: "center",
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  headerContent: { alignItems: "center" },
  titleWithIcon: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 8,
  },
  programTitle: {
    fontSize: 24,
    fontFamily: "MontserratBold",
    color: colors.text,
    marginLeft: 12,
    textAlign: "center",
  },
  programInfoContainer: {
    marginTop: 4,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  programInfoText: {
    fontSize: 12,
    color: colors.textSecondary,
    fontFamily: "MontserratRegular",
  },
  creatorButton: {
    backgroundColor: colors.border,
    paddingVertical: 2,
    paddingHorizontal: 4,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  creatorButtonText: {
    fontSize: 12,
    color: colors.primary,
    fontFamily: "MontserratBold"
  },
  moderatorButton: {
    backgroundColor: colors.border,
    paddingVertical: 2,
    paddingHorizontal: 4,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  moderatorButtonText: {
    fontSize: 12,
    color: colors.primary,
    fontFamily: "MontserratBold"
  },
  pledgePoolContainer: {
    flexDirection: "row",
    justifyContent: "space-evenly",
    alignItems: "center",
    marginTop: 6,
  },
  pledgeContainer: {
    backgroundColor: colors.surface,
    padding: 6,
    borderRadius: 8,
    alignItems: "center",
    minWidth: 80,
  },
  pledgeLabel: {
    fontSize: 12,
    color: colors.textMuted,
    fontFamily: "MontserratRegular",
  },
  pledgeValue: {
    fontSize: 16,
    color: colors.text,
    fontFamily: "MontserratBold"
  },
  participantsContainer: {
    backgroundColor: colors.surface,
    padding: 6,
    borderRadius: 8,
    alignItems: "center",
    minWidth: 80,
  },
  participantsLabel: {
    fontSize: 12,
    color: colors.textMuted,
    fontFamily: "MontserratRegular",
  },
  participantsValue: {
    fontSize: 16,
    color: colors.text,
    fontFamily: "MontserratBold"
  },
  poolContainer: {
    backgroundColor: colors.surface,
    padding: 6,
    borderRadius: 8,
    alignItems: "center",
    minWidth: 80,
  },
  poolLabel: {
    fontSize: 12,
    color: colors.textMuted,
    fontFamily: "MontserratRegular",
  },
  poolValue: {
    fontSize: 16,
    color: colors.text,
    fontFamily: "MontserratBold"
  },
  programHeadline: {
    fontSize: 16,
    color: colors.textSecondary,
    marginTop: 4,
    textAlign: "center",
    fontFamily: "MontserratRegular",
  },
  detailsCard: {
    backgroundColor: colors.surface,
    marginHorizontal: 10,
    marginTop: 10,
    marginBottom: 5,
    borderRadius: 15,
    padding: 15,
    shadowColor: isDark ? "#000" : "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: isDark ? 0.4 : 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: "MontserratBold",
    color: colors.primary,
    marginBottom: 8,
  },
  fullWidthText: {
    fontSize: 14,
    color: colors.text,
    marginBottom: 10,
    fontFamily: "MontserratRegular",
  },
  dateInfoContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    alignItems: "center",
    marginVertical: 6,
  },
  dateItem: { alignItems: "center" },
  dateLabel: {
    fontSize: 12,
    color: colors.textMuted,
    fontFamily: "MontserratRegular",
  },
  dateValue: {
    fontSize: 14,
    color: colors.text,
    fontFamily: "MontserratBold"
  },
  verticalDivider: {
    borderLeftWidth: 1,
    borderLeftColor: colors.separator,
    marginHorizontal: 10,
    height: 40,
  },
  twoColumnContainer: { flexDirection: "row", justifyContent: "space-between" },
  column: { flex: 1, paddingRight: 5 },
  detailRow: { marginVertical: 3 },
  detailLabel: {
    fontSize: 14,
    color: colors.textMuted,
    fontFamily: "MontserratRegular",
  },
  detailValue: {
    fontSize: 14,
    color: colors.text,
    fontFamily: "MontserratBold"
  },
  divider: {
    borderBottomWidth: 1,
    borderBottomColor: colors.separator,
    marginVertical: 10,
  },
  ctaButton: {
    backgroundColor: colors.primary,
    marginHorizontal: 10,
    paddingVertical: 12,
    borderRadius: 15,
    alignItems: "center",
    marginTop: 10,
  },
  ctaButtonText: {
    color: "#000000", // Keep black text on yellow button for readability
    fontSize: 16,
    fontFamily: "MontserratBold"
  },
  closedContainer: {
    marginHorizontal: 10,
    marginTop: 10,
    paddingVertical: 12,
    borderRadius: 15,
    alignItems: "center",
    borderWidth: 1,
    borderColor: colors.error,
  },
  closedText: {
    color: colors.error,
    fontSize: 16,
    fontFamily: "MontserratBold"
  },
  backButton: {
    marginHorizontal: 10,
    marginTop: 15,
    paddingVertical: 12,
    borderRadius: 15,
    alignItems: "center",
    borderWidth: 1,
    borderColor: colors.border,
  },
  backButtonText: {
    color: colors.textMuted,
    fontSize: 16,
    fontFamily: "MontserratRegular",
  },
  joinedMessageContainer: {
    marginHorizontal: 10,
    marginTop: 10,
    paddingVertical: 12,
    borderRadius: 15,
    alignItems: "center",
    backgroundColor: isDark ? colors.surface : colors.card,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  joinedMessageText: {
    color: colors.primary,
    fontSize: 16,
    fontFamily: "MontserratBold",
  },

  cancelButton: {
    marginTop: 10,
    alignItems: "center",
  },
  programBanner: {
    width: "100%",
    aspectRatio: 16 / 9, // This ensures 16:9 ratio with no cropping
    marginBottom: 2, // Further reduced from 5 to 2 to minimize gap
  },
});

const ProgramDetails: React.FC = () => {
  return (
    <ProtectedRoute>
      <ProgramDetailsComponent />
    </ProtectedRoute>
  );
};

export default ProgramDetails;
