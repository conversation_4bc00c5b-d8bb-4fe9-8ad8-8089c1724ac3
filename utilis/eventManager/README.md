# Event Management System

A comprehensive, centralized notification and event management system for Habit Royale that pushes important events to Firebase and provides a unified interface for handling all application events.

## Overview

The Event Management System provides:
- **Centralized Event Processing**: All events flow through a single system
- **Firebase Integration**: Automatic persistence of events to Firestore
- **Notification Management**: Unified notification delivery (in-app, push, etc.)
- **Analytics & Monitoring**: Built-in event tracking and performance monitoring
- **Middleware Support**: Extensible processing pipeline with validation and filtering
- **Error Handling**: Robust retry mechanisms and error tracking
- **Scalable Architecture**: Designed for easy maintenance and future enhancements

## Quick Start

### 1. Initialize the System

```typescript
import { initializeEventSystem } from '@/utilis/eventManager/init';

// Call this during app startup (e.g., in App.tsx)
initializeEventSystem();
```

### 2. Emit Events

```typescript
import { 
  emitUserEvent, 
  emitProgramEvent, 
  sendNotification,
  EventType, 
  EventPriority, 
  NotificationType 
} from '@/utilis/eventManager';

// User signup
await emitUserEvent(EventType.USER_SIGNUP, {
  userEmail: '<EMAIL>',
  firstName: 'John',
  lastName: 'Doe',
  source: 'SignUpScreen',
});

// Program join
await emitProgramEvent(EventType.PROGRAM_JOIN, {
  userEmail: '<EMAIL>',
  programId: 'program-123',
  programName: 'Morning Workout',
  source: 'ProgramDetailsScreen',
});

// Custom notification
await sendNotification(
  '<EMAIL>',
  'Welcome!',
  'Thanks for joining Habit Royale!',
  NotificationType.ACCOUNT,
  EventPriority.HIGH
);
```

## Architecture

### Core Components

1. **EventManager**: Central orchestrator that manages the event processing pipeline
2. **Handlers**: Process events (Firebase, Notifications, Analytics)
3. **Middleware**: Pre-process events (Validation, Filtering)
4. **Types**: TypeScript definitions for type safety

### Event Flow

```
Event Emission → Middleware Pipeline → Handler Processing → Firebase Storage
                      ↓
                 Validation → Filtering → Notifications + Analytics
```

## Event Types

### User Events
- `USER_SIGNUP`: New user registration
- `USER_LOGIN`: User authentication
- `USER_LOGOUT`: User session end
- `USER_PROFILE_UPDATE`: Profile changes

### Program Events
- `PROGRAM_JOIN`: User joins a program
- `PROGRAM_LEAVE`: User leaves a program
- `PROGRAM_START`: Program begins
- `PROGRAM_END`: Program completes
- `PROGRAM_SETUP_COMPLETE`: Account setup finished

### Payment Events
- `PAYMENT_INITIATED`: Payment process started
- `PAYMENT_SUCCESS`: Payment completed successfully
- `PAYMENT_FAILED`: Payment failed

### Achievement Events
- `ACHIEVEMENT_UNLOCKED`: New achievement earned
- `STREAK_MILESTONE`: Streak milestone reached

### System Events
- `SYSTEM_ERROR`: System errors
- `SYSTEM_MAINTENANCE`: Maintenance notifications

## Handlers

### FirebaseEventHandler
- Persists all events to Firestore
- Supports batching for performance
- Provides analytics queries
- Automatic cleanup of old events

### NotificationEventHandler
- Creates in-app notifications
- Sends push notifications for high-priority events
- Maps events to appropriate notification types
- Respects user preferences

### AnalyticsEventHandler
- Tracks event metrics
- Aggregates daily statistics
- Monitors system performance
- Error tracking and reporting

## Middleware

### ValidationMiddleware
- Validates event structure and data
- Ensures required fields are present
- Sanitizes and normalizes data
- Type-specific validation rules

### FilterMiddleware
- Rate limiting protection
- User preference filtering
- Quiet hours support
- Priority-based filtering

## Configuration

```typescript
const config: EventManagerConfig = {
  maxRetries: 3,
  retryDelay: 1000,
  batchSize: 10,
  enableAnalytics: true,
  enableLogging: true,
  firebaseConfig: {
    collection: 'events',
    enablePersistence: true,
    enableRealtime: false,
  },
  notificationConfig: {
    enabled: true,
    types: ['account', 'program', 'points', 'reminder', 'system', 'achievement'],
    priorities: ['low', 'medium', 'high', 'critical'],
    deliveryMethods: ['firebase', 'push'],
  },
};
```

## Migration from Legacy System

The system maintains backward compatibility with the existing notification system:

```typescript
// Old way (still works)
import { createNotification } from '@/utilis/notifications';
await createNotification(userEmail, {
  title: 'Welcome!',
  message: 'Thanks for joining!',
  type: 'account',
  priority: 'high',
});

// New way (recommended)
import { sendNotification, NotificationType, EventPriority } from '@/utilis/eventManager';
await sendNotification(
  userEmail,
  'Welcome!',
  'Thanks for joining!',
  NotificationType.ACCOUNT,
  EventPriority.HIGH
);
```

## Best Practices

### 1. Use Appropriate Event Types
Choose the most specific event type for better analytics and filtering.

### 2. Set Correct Priorities
- `CRITICAL`: System failures, security issues
- `HIGH`: Payment confirmations, important account changes
- `MEDIUM`: Program updates, achievements
- `LOW`: Login notifications, routine updates

### 3. Include Relevant Metadata
Add context that might be useful for analytics or debugging:

```typescript
await emitProgramEvent(EventType.PROGRAM_JOIN, {
  userEmail: '<EMAIL>',
  programId: 'program-123',
  programName: 'Morning Workout',
  programCategory: 'fitness',
  source: 'ProgramDetailsScreen',
  metadata: {
    referralSource: 'friend_invite',
    deviceType: 'mobile',
  },
});
```

### 4. Handle Errors Gracefully
```typescript
try {
  await emitUserEvent(EventType.USER_SIGNUP, userData);
} catch (error) {
  console.error('Failed to emit signup event:', error);
  // Continue with app flow - don't block user experience
}
```

## Monitoring and Debugging

### Health Check
```typescript
import { healthCheck } from '@/utilis/eventManager/init';
const isHealthy = await healthCheck();
```

### System Statistics
```typescript
import { getSystemStats } from '@/utilis/eventManager/init';
const stats = await getSystemStats();
console.log('Event system stats:', stats);
```

### Development Testing
```typescript
import { testEventSystem } from '@/utilis/eventManager/init';
await testEventSystem(); // Only in development
```

## Firebase Collections

The system creates the following Firestore collections:

- `events`: Individual event records
- `analytics`: Detailed analytics data
- `analytics_daily`: Aggregated daily statistics
- `analytics_errors`: Error tracking
- `analytics_stats`: System-wide statistics

## Future Enhancements

The system is designed to easily support:
- Email notifications
- SMS notifications
- Webhook integrations
- Real-time event streaming
- Advanced analytics dashboards
- A/B testing for notifications
- Machine learning-based personalization

## Troubleshooting

### Common Issues

1. **Events not appearing in Firebase**
   - Check Firebase configuration
   - Verify network connectivity
   - Check console for error messages

2. **Notifications not being delivered**
   - Verify notification permissions
   - Check user preferences
   - Ensure event types are mapped correctly

3. **Performance issues**
   - Adjust batch sizes
   - Review middleware configuration
   - Monitor queue sizes

### Debug Mode

Enable detailed logging in development:
```typescript
initializeEventSystem({
  enableLogging: true,
  enableAnalytics: true,
});
```

## Support

For questions or issues with the Event Management System:
1. Check the console logs for error messages
2. Review the configuration settings
3. Use the health check and stats functions
4. Refer to the usage examples in `examples/usage.ts`
