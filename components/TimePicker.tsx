import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/contexts/ThemeContext';

interface TimePickerProps {
  visible: boolean;
  onClose: () => void;
  onTimeSelect: (time: string) => void;
  initialTime?: string;
  title: string;
}

const TimePicker: React.FC<TimePickerProps> = ({
  visible,
  onClose,
  onTimeSelect,
  initialTime,
  title,
}) => {
  const { colors } = useTheme();
  
  // Parse initial time or default to 12:00
  const parseTime = (timeStr?: string) => {
    if (!timeStr) return { hour: 12, minute: 0 };
    const [hourStr, minuteStr] = timeStr.split(':');
    return {
      hour: parseInt(hourStr, 10) || 12,
      minute: parseInt(minuteStr, 10) || 0,
    };
  };

  const { hour: initialHour, minute: initialMinute } = parseTime(initialTime);
  const [selectedHour, setSelectedHour] = useState(initialHour);
  const [selectedMinute, setSelectedMinute] = useState(initialMinute);

  // Generate hours (0-23)
  const hours = Array.from({ length: 24 }, (_, i) => i);
  
  // Generate minutes (0, 5, 10, 15, ..., 55)
  const minutes = Array.from({ length: 12 }, (_, i) => i * 5);

  const formatTime = (hour: number, minute: number) => {
    return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
  };

  const handleConfirm = () => {
    const timeString = formatTime(selectedHour, selectedMinute);
    onTimeSelect(timeString);
    onClose();
  };

  const handleCancel = () => {
    // Reset to initial values
    setSelectedHour(initialHour);
    setSelectedMinute(initialMinute);
    onClose();
  };

  const renderTimeColumn = (
    values: number[],
    selectedValue: number,
    onSelect: (value: number) => void,
    formatValue?: (value: number) => string
  ) => (
    <View style={styles.timeColumn}>
      <ScrollView 
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {values.map((value) => (
          <TouchableOpacity
            key={value}
            style={[
              styles.timeItem,
              selectedValue === value && { backgroundColor: colors.primary },
            ]}
            onPress={() => onSelect(value)}
          >
            <Text
              style={[
                styles.timeText,
                { color: selectedValue === value ? '#ffffff' : colors.text },
              ]}
            >
              {formatValue ? formatValue(value) : value.toString().padStart(2, '0')}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={handleCancel}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: colors.surface }]}>
          {/* Header */}
          <View style={[styles.header, { borderBottomColor: colors.border }]}>
            <TouchableOpacity onPress={handleCancel} style={styles.cancelButton}>
              <Text style={[styles.cancelText, { color: colors.textMuted }]}>Cancel</Text>
            </TouchableOpacity>
            <Text style={[styles.title, { color: colors.text }]}>{title}</Text>
            <TouchableOpacity onPress={handleConfirm} style={styles.confirmButton}>
              <Text style={[styles.confirmText, { color: colors.primary }]}>Done</Text>
            </TouchableOpacity>
          </View>

          {/* Selected Time Display */}
          <View style={[styles.selectedTimeContainer, { backgroundColor: colors.background }]}>
            <Text style={[styles.selectedTimeText, { color: colors.text }]}>
              {formatTime(selectedHour, selectedMinute)}
            </Text>
          </View>

          {/* Time Picker */}
          <View style={styles.timePickerContainer}>
            <View style={styles.timePickerRow}>
              {/* Hours */}
              <View style={styles.columnContainer}>
                <Text style={[styles.columnLabel, { color: colors.textMuted }]}>Hour</Text>
                {renderTimeColumn(hours, selectedHour, setSelectedHour)}
              </View>

              {/* Separator */}
              <View style={styles.separator}>
                <Text style={[styles.separatorText, { color: colors.text }]}>:</Text>
              </View>

              {/* Minutes */}
              <View style={styles.columnContainer}>
                <Text style={[styles.columnLabel, { color: colors.textMuted }]}>Minute</Text>
                {renderTimeColumn(minutes, selectedMinute, setSelectedMinute)}
              </View>
            </View>
          </View>

          {/* Quick Time Options */}
          <View style={[styles.quickOptionsContainer, { borderTopColor: colors.border }]}>
            <Text style={[styles.quickOptionsLabel, { color: colors.textMuted }]}>Quick Select</Text>
            <View style={styles.quickOptionsRow}>
              {[
                { label: '6:00', hour: 6, minute: 0 },
                { label: '9:00', hour: 9, minute: 0 },
                { label: '12:00', hour: 12, minute: 0 },
                { label: '18:00', hour: 18, minute: 0 },
                { label: '21:00', hour: 21, minute: 0 },
              ].map((option) => (
                <TouchableOpacity
                  key={option.label}
                  style={[styles.quickOption, { borderColor: colors.border }]}
                  onPress={() => {
                    setSelectedHour(option.hour);
                    setSelectedMinute(option.minute);
                  }}
                >
                  <Text style={[styles.quickOptionText, { color: colors.text }]}>
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxWidth: 400,
    borderRadius: 16,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  cancelButton: {
    padding: 8,
  },
  cancelText: {
    fontSize: 16,
    fontWeight: '500',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  confirmButton: {
    padding: 8,
  },
  confirmText: {
    fontSize: 16,
    fontWeight: '600',
  },
  selectedTimeContainer: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  selectedTimeText: {
    fontSize: 32,
    fontWeight: '300',
    fontFamily: 'monospace',
  },
  timePickerContainer: {
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  timePickerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  columnContainer: {
    alignItems: 'center',
  },
  columnLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 8,
  },
  timeColumn: {
    height: 150,
    width: 80,
  },
  scrollContent: {
    paddingVertical: 60,
  },
  timeItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginVertical: 2,
    borderRadius: 8,
    alignItems: 'center',
  },
  timeText: {
    fontSize: 18,
    fontWeight: '500',
  },
  separator: {
    paddingHorizontal: 16,
    paddingTop: 20,
  },
  separatorText: {
    fontSize: 24,
    fontWeight: '300',
  },
  quickOptionsContainer: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderTopWidth: 1,
  },
  quickOptionsLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 12,
  },
  quickOptionsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  quickOption: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    borderWidth: 1,
  },
  quickOptionText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default TimePicker;
