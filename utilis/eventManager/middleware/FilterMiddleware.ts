import { Event, EventMiddleware, EventType, EventPriority, NotificationType } from '../types';
import { getId } from '@/utilis/variables';

interface UserPreferences {
  notifications: {
    enabled: boolean;
    types: NotificationType[];
    priorities: EventPriority[];
    quietHours?: {
      enabled: boolean;
      start: string; // HH:MM format
      end: string;   // HH:MM format
      timezone: string;
    };
  };
  events: {
    enableAnalytics: boolean;
    enableTracking: boolean;
  };
}

export class FilterMiddleware implements EventMiddleware {
  public readonly name = 'FilterMiddleware';
  public readonly order = 2; // Run after validation

  private userPreferencesCache: Map<string, UserPreferences> = new Map();
  private cacheExpiry: Map<string, number> = new Map();
  private readonly cacheTimeout = 5 * 60 * 1000; // 5 minutes

  public async process(event: Event): Promise<Event | null> {
    try {
      // Apply rate limiting
      if (this.isRateLimited(event)) {
        console.log(`FilterMiddleware: Event ${event.id} rate limited`);
        return null;
      }

      // Apply user preference filtering
      if (!(await this.passesUserPreferences(event))) {
        console.log(`FilterMiddleware: Event ${event.id} filtered by user preferences`);
        return null;
      }

      // Apply quiet hours filtering for notifications
      if (this.isNotificationEvent(event) && (await this.isInQuietHours(event))) {
        console.log(`FilterMiddleware: Event ${event.id} filtered by quiet hours`);
        return null;
      }

      // Apply priority filtering
      if (!this.passesPriorityFilter(event)) {
        console.log(`FilterMiddleware: Event ${event.id} filtered by priority`);
        return null;
      }

      console.log(`FilterMiddleware: Event ${event.id} passed all filters`);
      return event;
    } catch (error) {
      console.error(`FilterMiddleware: Error processing event ${event.id}:`, error);
      // In case of error, let the event pass through
      return event;
    }
  }

  private isRateLimited(event: Event): boolean {
    // Simple rate limiting based on event type and user
    const key = `${event.data.userEmail || 'anonymous'}_${event.type}`;
    const now = Date.now();
    const rateLimitWindow = 60 * 1000; // 1 minute
    const maxEventsPerWindow = this.getMaxEventsPerWindow(event.type);

    if (!this.rateLimitCache) {
      this.rateLimitCache = new Map();
    }

    const userEvents = this.rateLimitCache.get(key) || [];
    
    // Remove old events outside the window
    const recentEvents = userEvents.filter(timestamp => now - timestamp < rateLimitWindow);
    
    if (recentEvents.length >= maxEventsPerWindow) {
      return true;
    }

    // Add current event
    recentEvents.push(now);
    this.rateLimitCache.set(key, recentEvents);
    
    return false;
  }

  private rateLimitCache: Map<string, number[]> = new Map();

  private getMaxEventsPerWindow(eventType: EventType): number {
    switch (eventType) {
      case EventType.USER_LOGIN:
        return 5; // Max 5 login events per minute
      case EventType.NOTIFICATION_SENT:
        return 10; // Max 10 notifications per minute
      case EventType.PROGRAM_SUBMISSION:
        return 3; // Max 3 submissions per minute
      default:
        return 20; // Default limit
    }
  }

  private async passesUserPreferences(event: Event): Promise<boolean> {
    const userEmail = event.data.userEmail;
    if (!userEmail) {
      return true; // No user context, let it pass
    }

    try {
      const preferences = await this.getUserPreferences(userEmail);
      
      // Check if notifications are enabled for notification events
      if (this.isNotificationEvent(event)) {
        if (!preferences.notifications.enabled) {
          return false;
        }

        // Check notification type preferences
        const notificationType = this.getNotificationTypeFromEvent(event);
        if (notificationType && !preferences.notifications.types.includes(notificationType)) {
          return false;
        }

        // Check priority preferences
        if (!preferences.notifications.priorities.includes(event.priority)) {
          return false;
        }
      }

      // Check analytics preferences
      if (this.isAnalyticsEvent(event) && !preferences.events.enableAnalytics) {
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error checking user preferences:', error);
      return true; // Default to allowing the event
    }
  }

  private async isInQuietHours(event: Event): Promise<boolean> {
    const userEmail = event.data.userEmail;
    if (!userEmail) {
      return false;
    }

    try {
      const preferences = await this.getUserPreferences(userEmail);
      const quietHours = preferences.notifications.quietHours;

      if (!quietHours?.enabled) {
        return false;
      }

      const now = new Date();
      const currentTime = now.toLocaleTimeString('en-US', { 
        hour12: false, 
        hour: '2-digit', 
        minute: '2-digit',
        timeZone: quietHours.timezone || 'UTC'
      });

      return this.isTimeInRange(currentTime, quietHours.start, quietHours.end);
    } catch (error) {
      console.error('Error checking quiet hours:', error);
      return false;
    }
  }

  private isTimeInRange(currentTime: string, startTime: string, endTime: string): boolean {
    const current = this.timeToMinutes(currentTime);
    const start = this.timeToMinutes(startTime);
    const end = this.timeToMinutes(endTime);

    if (start <= end) {
      // Same day range
      return current >= start && current <= end;
    } else {
      // Overnight range
      return current >= start || current <= end;
    }
  }

  private timeToMinutes(time: string): number {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  }

  private passesPriorityFilter(event: Event): boolean {
    // Always allow critical events
    if (event.priority === EventPriority.CRITICAL) {
      return true;
    }

    // Filter out low priority events during high load
    if (this.isHighLoad() && event.priority === EventPriority.LOW) {
      return false;
    }

    return true;
  }

  private isHighLoad(): boolean {
    // Simple high load detection based on queue size
    // In a real implementation, you might check system metrics
    return false; // Placeholder
  }

  private isNotificationEvent(event: Event): boolean {
    const notificationEvents = [
      EventType.USER_SIGNUP,
      EventType.USER_LOGIN,
      EventType.PROGRAM_JOIN,
      EventType.PROGRAM_SETUP_COMPLETE,
      EventType.PAYMENT_SUCCESS,
      EventType.PAYMENT_FAILED,
      EventType.NOTIFICATION_SENT,
      EventType.ACHIEVEMENT_UNLOCKED,
      EventType.STREAK_MILESTONE,
    ];

    return notificationEvents.includes(event.type);
  }

  private isAnalyticsEvent(event: Event): boolean {
    // Most events are analytics events except system errors
    return event.type !== EventType.SYSTEM_ERROR;
  }

  private getNotificationTypeFromEvent(event: Event): NotificationType | null {
    switch (event.type) {
      case EventType.USER_SIGNUP:
      case EventType.USER_LOGIN:
      case EventType.PAYMENT_SUCCESS:
      case EventType.PAYMENT_FAILED:
        return NotificationType.ACCOUNT;
      
      case EventType.PROGRAM_JOIN:
      case EventType.PROGRAM_SETUP_COMPLETE:
        return NotificationType.PROGRAM;
      
      case EventType.ACHIEVEMENT_UNLOCKED:
      case EventType.STREAK_MILESTONE:
        return NotificationType.ACHIEVEMENT;
      
      default:
        return null;
    }
  }

  private async getUserPreferences(userEmail: string): Promise<UserPreferences> {
    // Check cache first
    const cached = this.userPreferencesCache.get(userEmail);
    const cacheTime = this.cacheExpiry.get(userEmail);
    
    if (cached && cacheTime && Date.now() - cacheTime < this.cacheTimeout) {
      return cached;
    }

    // Default preferences
    const defaultPreferences: UserPreferences = {
      notifications: {
        enabled: true,
        types: Object.values(NotificationType),
        priorities: Object.values(EventPriority),
        quietHours: {
          enabled: false,
          start: '22:00',
          end: '08:00',
          timezone: 'UTC',
        },
      },
      events: {
        enableAnalytics: true,
        enableTracking: true,
      },
    };

    // In a real implementation, you would fetch from Firebase/database
    // For now, return default preferences
    this.userPreferencesCache.set(userEmail, defaultPreferences);
    this.cacheExpiry.set(userEmail, Date.now());

    return defaultPreferences;
  }
}
