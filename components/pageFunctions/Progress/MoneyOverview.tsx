import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { useTheme } from "@/contexts/ThemeContext";

interface MoneyOverviewProps {
  stats: {
    activeCount: number;
    activeMoney: number;
    disqualifiedCount: number;
    disqualifiedMoney: number;
    userMoneyBack: number;
    userShare: number;
  } | null;
}

export default function MoneyOverview({ stats }: MoneyOverviewProps) {
  if (!stats) return null;

  const { colors } = useTheme();
  const styles = createStyles(colors);

  const {
    activeCount,
    activeMoney,
    disqualifiedCount,
    disqualifiedMoney,
    userMoneyBack,
    userShare
  } = stats;

  return (
    <View style={styles.container}>
      <View style={styles.outerBox}>
        <View style={styles.innerBox}>
          <View style={styles.innerBoxHeader}>
            <Text style={styles.headerLabel}>active</Text>
            <Text style={styles.headerLabel}>eliminated</Text>
          </View>
          <View style={styles.innerContent}>
            <View style={styles.dataColumn}>
              <Text style={styles.countText}>{activeCount}</Text>
              <Text style={styles.amountText}>(${activeMoney})</Text>
            </View>
            <View style={styles.verticalDivider} />
            <View style={styles.dataColumn}>
              <Text style={styles.countText}>{disqualifiedCount}</Text>
              <Text style={styles.amountText}>(${disqualifiedMoney})</Text>
            </View>
          </View>
        </View>

        <View style={[styles.innerBox, { marginTop: 16 }]}>
          <View style={styles.innerBoxHeaderCentered}>
            <Text style={styles.headerLabelCentered}>
              make it through & get
            </Text>
          </View>
          <View style={styles.innerContent}>
            <View style={styles.breakdownColumn}>
              <Text style={styles.breakdownDesc}>your money back</Text>
              <Text style={styles.breakdownValue}>${userMoneyBack}</Text>
            </View>
            <View style={styles.verticalDivider} />
            <View style={styles.breakdownColumn}>
              <Text style={styles.breakdownDesc}>
                your % of eliminated pool
              </Text>
              <Text style={styles.breakdownValue}>
                ${userShare.toFixed(2)} (or more)
              </Text>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
}

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    padding: 3,
    backgroundColor: colors.background,
    marginTop: 6,
    borderRadius: 40
  },
  outerBox: {
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    paddingVertical: 6,
    paddingHorizontal: 6,
  },
  innerBox: {
    backgroundColor: colors.card,
    borderRadius: 6,
    marginVertical: 3,
    paddingVertical: 6,
    paddingHorizontal: 8,
    position: "relative",
  },
  // Updated header: use full width with space-around so labels are centered
  innerBoxHeader: {
    position: "absolute",
    top: -8,
    left: 0,
    right: 0,
    flexDirection: "row",
    justifyContent: "space-around",
  },
  innerBoxHeaderCentered: {
    position: "absolute",
    top: -8,
    left: 0,
    right: 0,
    alignItems: "center",
  },
  headerLabel: {
    fontSize: 10,
    fontWeight: "bold",
    color: colors.primary,
    backgroundColor: colors.card,
    paddingHorizontal: 6,
    textAlign: "center",
  },
  headerLabelCentered: {
    fontSize: 10,
    fontWeight: "bold",
    color: colors.primary,
    backgroundColor: colors.card,
    paddingHorizontal: 6,
    textAlign: "center",
  },
  innerContent: {
    flexDirection: "row",
    justifyContent: "space-around",
    alignItems: "center",
    marginTop: 4,
  },
  dataColumn: {
    flex: 1,
    alignItems: "center",
  },
  countText: {
    fontSize: 16,
    color: colors.text,
    fontWeight: "bold",
  },
  amountText: {
    fontSize: 11,
    color: colors.textMuted,
    marginTop: 1,
  },
  verticalDivider: {
    width: 1,
    backgroundColor: colors.border,
    height: "60%",
    marginHorizontal: 12,
  },
  breakdownColumn: {
    flex: 1,
    alignItems: "center",
  },
  breakdownDesc: {
    fontSize: 10,
    color: colors.textSecondary,
  },
  breakdownValue: {
    fontSize: 15,
    color: colors.text,
    fontWeight: "bold",
    marginTop: 2,
  },
});
