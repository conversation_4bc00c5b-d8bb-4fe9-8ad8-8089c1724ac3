import { Tabs } from "expo-router";
import React from "react";
import { Platform } from "react-native";

import { HapticTab } from "@/components/HapticTab";
import { CustomTabBar } from "@/components/CustomTabBar";
import { Colors } from "@/constants/Colors";
import { useColorScheme } from "@/hooks/useColorScheme";
import { Ionicons, MaterialCommunityIcons } from "@expo/vector-icons";
import { ProtectedRoute } from "@/components/ProtectedRoute";

export default function TabLayout() {
  const colorScheme = useColorScheme();

  return (
    <ProtectedRoute>
      <Tabs
        tabBar={(props) => <CustomTabBar {...props} />}
        screenOptions={{
          headerShown: false,
          lazy: true, // Enable lazy loading for better performance
          unmountOnBlur: false, // Keep tabs mounted but inactive
        }}
      >
        <Tabs.Screen name="index" />
        <Tabs.Screen name="commits" />
        <Tabs.Screen name="progress" />
        <Tabs.Screen name="points" />
        <Tabs.Screen name="profile" />
      </Tabs>
    </ProtectedRoute>
  );
}
