import { BaseService } from './BaseService';
import {
  Submission,
  ServiceResponse,
  QueryOptions,
  COLLECTIONS,
  SUBCOLLECTIONS,
  UnsubscribeFunction,
  DocumentSubscriptionCallback,
} from './types';

export class SubmissionService extends BaseService {
  private getSubmissionPath(programId: string, userId: string): string {
    return `${COLLECTIONS.PROGRAMS}/${programId}/${SUBCOLLECTIONS.PROGRAM_PARTICIPANTS}/${userId}/${SUBCOLLECTIONS.PARTICIPANT_SUBMISSIONS}`;
  }

  // Submission CRUD operations
  async createSubmission(
    programId: string,
    userId: string,
    dayId: string,
    submissionData: Omit<Submission, 'id'>
  ): Promise<ServiceResponse<string>> {
    const collectionPath = this.getSubmissionPath(programId, userId);
    return this.createDocument<Submission>(collectionPath, submissionData, dayId);
  }

  async getSubmission(
    programId: string,
    userId: string,
    dayId: string
  ): Promise<ServiceResponse<Submission | null>> {
    const collectionPath = this.getSubmissionPath(programId, userId);
    return this.getDocument<Submission>(collectionPath, dayId);
  }

  async updateSubmission(
    programId: string,
    userId: string,
    dayId: string,
    submissionData: Partial<Submission>
  ): Promise<ServiceResponse<void>> {
    const collectionPath = this.getSubmissionPath(programId, userId);
    return this.updateDocument<Submission>(collectionPath, dayId, submissionData);
  }

  async deleteSubmission(
    programId: string,
    userId: string,
    dayId: string
  ): Promise<ServiceResponse<void>> {
    const collectionPath = this.getSubmissionPath(programId, userId);
    return this.deleteDocument(collectionPath, dayId);
  }

  // Submission status management
  async submitForDay(
    programId: string,
    userId: string,
    dayId: string,
    attachment?: string
  ): Promise<ServiceResponse<string>> {
    const submissionData: Omit<Submission, 'id'> = {
      status: 'submitted',
      attachment,
      timestamp: new Date().toISOString(),
    };

    return this.createSubmission(programId, userId, dayId, submissionData);
  }

  async updateSubmissionStatus(
    programId: string,
    userId: string,
    dayId: string,
    status: Submission['status'],
    attachment?: string
  ): Promise<ServiceResponse<void>> {
    const updateData: Partial<Submission> = {
      status,
      timestamp: new Date().toISOString(),
    };

    if (attachment !== undefined) {
      updateData.attachment = attachment;
    }

    return this.updateSubmission(programId, userId, dayId, updateData);
  }

  async markAsBailed(
    programId: string,
    userId: string,
    dayId: string,
    livesInfo?: string
  ): Promise<ServiceResponse<void>> {
    const attachment = livesInfo ? `Bailed Out ${livesInfo}` : 'Bailed Out';
    return this.updateSubmissionStatus(programId, userId, dayId, 'bailed', attachment);
  }

  async markAsNotSubmitted(
    programId: string,
    userId: string,
    dayId: string
  ): Promise<ServiceResponse<void>> {
    return this.updateSubmissionStatus(programId, userId, dayId, 'not_submitted');
  }

  // Bulk submission operations
  async initializeSubmissionsForProgram(
    programId: string,
    userId: string,
    totalDays: number
  ): Promise<ServiceResponse<string[]>> {
    try {
      const results: string[] = [];
      
      for (let day = 1; day <= totalDays; day++) {
        const dayId = `Day ${day}`;
        const submissionData: Omit<Submission, 'id'> = {
          status: 'upcoming',
        };

        const result = await this.createSubmission(programId, userId, dayId, submissionData);
        if (result.success && result.data) {
          results.push(result.data);
        } else {
          return this.createErrorResponse({
            operation: 'create',
            collection: this.getSubmissionPath(programId, userId),
            originalError: new Error(result.error || 'Failed to create submission'),
            timestamp: new Date(),
          });
        }
      }
      
      return this.createSuccessResponse(results);
    } catch (error) {
      const dbError = this.handleError('create', this.getSubmissionPath(programId, userId), error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  // Submission querying
  async getAllSubmissions(
    programId: string,
    userId: string
  ): Promise<ServiceResponse<Submission[]>> {
    const collectionPath = this.getSubmissionPath(programId, userId);
    return this.getCollection<Submission>(collectionPath);
  }

  async getSubmissionsByStatus(
    programId: string,
    userId: string,
    status: Submission['status']
  ): Promise<ServiceResponse<Submission[]>> {
    const collectionPath = this.getSubmissionPath(programId, userId);
    const options: QueryOptions = {
      where: [{ field: 'status', operator: '==', value: status }]
    };
    return this.getCollection<Submission>(collectionPath, options);
  }

  async getSubmittedSubmissions(
    programId: string,
    userId: string
  ): Promise<ServiceResponse<Submission[]>> {
    return this.getSubmissionsByStatus(programId, userId, 'submitted');
  }

  async getBailedSubmissions(
    programId: string,
    userId: string
  ): Promise<ServiceResponse<Submission[]>> {
    return this.getSubmissionsByStatus(programId, userId, 'bailed');
  }

  async getUpcomingSubmissions(
    programId: string,
    userId: string
  ): Promise<ServiceResponse<Submission[]>> {
    return this.getSubmissionsByStatus(programId, userId, 'upcoming');
  }

  // Submission statistics
  async getSubmissionStats(
    programId: string,
    userId: string
  ): Promise<ServiceResponse<{
    total: number;
    submitted: number;
    bailed: number;
    upcoming: number;
    notSubmitted: number;
    completionRate: number;
  }>> {
    try {
      const allSubmissionsResult = await this.getAllSubmissions(programId, userId);
      if (!allSubmissionsResult.success) {
        return this.createErrorResponse(allSubmissionsResult as any);
      }

      const submissions = allSubmissionsResult.data || [];
      const total = submissions.length;
      const submitted = submissions.filter(s => s.status === 'submitted').length;
      const bailed = submissions.filter(s => s.status === 'bailed').length;
      const upcoming = submissions.filter(s => s.status === 'upcoming').length;
      const notSubmitted = submissions.filter(s => s.status === 'not_submitted').length;
      const completionRate = total > 0 ? (submitted / total) * 100 : 0;

      const stats = {
        total,
        submitted,
        bailed,
        upcoming,
        notSubmitted,
        completionRate,
      };

      return this.createSuccessResponse(stats);
    } catch (error) {
      const dbError = this.handleError('read', this.getSubmissionPath(programId, userId), error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  // Program-wide submission statistics
  async getProgramSubmissionStats(
    programId: string,
    participantIds: string[]
  ): Promise<ServiceResponse<{
    totalSubmissions: number;
    totalPossibleSubmissions: number;
    overallCompletionRate: number;
    participantStats: Array<{
      userId: string;
      submitted: number;
      total: number;
      completionRate: number;
    }>;
  }>> {
    try {
      let totalSubmissions = 0;
      let totalPossibleSubmissions = 0;
      const participantStats: Array<{
        userId: string;
        submitted: number;
        total: number;
        completionRate: number;
      }> = [];

      for (const userId of participantIds) {
        const statsResult = await this.getSubmissionStats(programId, userId);
        if (statsResult.success && statsResult.data) {
          const userStats = statsResult.data;
          totalSubmissions += userStats.submitted;
          totalPossibleSubmissions += userStats.total;
          
          participantStats.push({
            userId,
            submitted: userStats.submitted,
            total: userStats.total,
            completionRate: userStats.completionRate,
          });
        }
      }

      const overallCompletionRate = totalPossibleSubmissions > 0 
        ? (totalSubmissions / totalPossibleSubmissions) * 100 
        : 0;

      const programStats = {
        totalSubmissions,
        totalPossibleSubmissions,
        overallCompletionRate,
        participantStats,
      };

      return this.createSuccessResponse(programStats);
    } catch (error) {
      const dbError = this.handleError('read', `${COLLECTIONS.PROGRAMS}/${programId}`, error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  // Real-time subscriptions
  subscribeToSubmission(
    programId: string,
    userId: string,
    dayId: string,
    callback: DocumentSubscriptionCallback<Submission>
  ): UnsubscribeFunction {
    const collectionPath = this.getSubmissionPath(programId, userId);
    return this.subscribeToDocument<Submission>(collectionPath, dayId, callback);
  }

  subscribeToUserSubmissions(
    programId: string,
    userId: string,
    callback: (submissions: Submission[]) => void
  ): UnsubscribeFunction {
    const collectionPath = this.getSubmissionPath(programId, userId);
    return this.subscribeToCollection<Submission>(collectionPath, callback);
  }

  // Helper methods
  async isSubmissionCompleted(
    programId: string,
    userId: string,
    dayId: string
  ): Promise<ServiceResponse<boolean>> {
    const result = await this.getSubmission(programId, userId, dayId);
    if (!result.success) {
      return this.createErrorResponse(result as any);
    }
    
    const isCompleted = result.data?.status === 'submitted';
    return this.createSuccessResponse(isCompleted);
  }

  async getSubmissionStatus(
    programId: string,
    userId: string,
    dayId: string
  ): Promise<ServiceResponse<Submission['status'] | null>> {
    const result = await this.getSubmission(programId, userId, dayId);
    if (!result.success) {
      return this.createErrorResponse(result as any);
    }
    
    const status = result.data?.status || null;
    return this.createSuccessResponse(status);
  }

  // Validation
  validateSubmissionData(submissionData: Partial<Submission>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (submissionData.status && !['upcoming', 'submitted', 'bailed', 'not_submitted'].includes(submissionData.status)) {
      errors.push('Invalid submission status');
    }

    if (submissionData.status === 'submitted' && !submissionData.attachment) {
      errors.push('Attachment is required for submitted status');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // Batch operations for submissions
  async updateMultipleSubmissions(
    updates: Array<{
      programId: string;
      userId: string;
      dayId: string;
      data: Partial<Submission>;
    }>
  ): Promise<ServiceResponse<void>> {
    try {
      const operations = updates.map(({ programId, userId, dayId, data }) => ({
        type: 'update' as const,
        collection: this.getSubmissionPath(programId, userId),
        documentId: dayId,
        data,
      }));

      const result = await this.executeBatch(operations);
      if (result.success) {
        return this.createSuccessResponse(undefined);
      } else {
        return this.createErrorResponse({
          operation: 'update',
          collection: 'submissions',
          originalError: new Error(result.error || 'Batch update failed'),
          timestamp: new Date(),
        });
      }
    } catch (error) {
      const dbError = this.handleError('update', 'submissions', error as Error);
      return this.createErrorResponse(dbError);
    }
  }
}
