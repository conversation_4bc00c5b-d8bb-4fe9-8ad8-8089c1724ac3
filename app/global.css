/* Add this file to your project if it doesn't exist */
body {
  margin: 0;
  padding: 0;
  background-color: #121212;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

#root {
  display: flex;
  flex: 1;
}

/* Hide scrollbar for Chrome, Safari and Opera */
::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
* {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

/* Web-specific calendar improvements */
.calendar-container {
  max-width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

.calendar-grid {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  box-sizing: border-box;
}

.calendar-day-cell {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.1s ease;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.calendar-day-cell:hover {
  transform: scale(1.02);
}

.calendar-day-cell.inactive {
  cursor: not-allowed;
  opacity: 0.3;
}

.calendar-day-cell.inactive:hover {
  transform: none;
}

.program-band {
  pointer-events: none;
  box-sizing: border-box;
}

.calendar-button {
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.1s ease;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  outline: none;
  border: none;
  background: transparent;
}

.calendar-button:hover {
  opacity: 0.8;
}

.calendar-button:active {
  transform: scale(0.98);
}

.calendar-button:focus {
  outline: 2px solid #007AFF;
  outline-offset: 2px;
}

.calendar-text {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Responsive improvements */
@media (max-width: 480px) {
  .calendar-container {
    padding: 8px;
  }

  .calendar-day-cell {
    font-size: 14px;
  }
}

@media (min-width: 481px) {
  .calendar-container {
    padding: 12px;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .calendar-day-cell,
  .calendar-button,
  .program-band {
    transition: none;
  }

  .calendar-day-cell:hover {
    transform: none;
  }

  .calendar-button:active {
    transform: none;
  }
}

/* Focus improvements for keyboard navigation */
.calendar-day-cell:focus-visible {
  outline: 2px solid #007AFF;
  outline-offset: 2px;
  z-index: 10;
}

/* Web-specific calendar cell sizing improvements */
@media (min-width: 481px) {
  /* Target calendar day cells on web */
  [data-testid="calendar-day-cell"],
  .calendar-day-cell {
    min-height: 32px !important;
    max-height: 42px !important;
    height: 38px !important;
  }

  /* Timeline calendar cells */
  [data-testid="timeline-day-cell"] {
    height: 42px !important;
    min-height: 38px !important;
    max-height: 50px !important;
  }
}

/* Ensure calendar containers don't get too tall */
@media (min-width: 481px) {
  .calendar-container {
    max-height: 400px;
  }

  .calendar-grid {
    gap: 1px;
  }
}