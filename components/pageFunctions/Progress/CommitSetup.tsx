import React, { useState, useEffect } from "react";
import {
  View,
  TextInput,
  TouchableOpacity,
  Text,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from "react-native";
import { useCameraPermissions } from "expo-camera";
import * as ImagePicker from "expo-image-picker";
import * as Location from "expo-location";
import { useTheme } from "@/contexts/ThemeContext";
import { firestoreService } from "@/services/database";
import { Commit } from "@/services/database/types";
import { githubService } from "@/services/githubService";

interface CommitSetupProps {
  commit: Commit;
  onSetupComplete: (updatedCommit: Commit) => void;
}

const CommitSetup: React.FC<CommitSetupProps> = ({
  commit,
  onSetupComplete,
}) => {
  const { colors } = useTheme();
  const [inputValue, setInputValue] = useState<string>("");
  const [isConnecting, setIsConnecting] = useState(false);
  const [permission, requestPermission] = useCameraPermissions();
  const [locationPermission, setLocationPermission] = useState<Location.PermissionResponse | null>(null);
  const [galleryPermission, setGalleryPermission] = useState<ImagePicker.PermissionResponse | null>(null);

  const evidenceType = commit.evidence?.type || 'photo';

  // Request permissions on mount
  useEffect(() => {
    const requestInitialPermissions = async () => {
      if (evidenceType === 'photo' || evidenceType === 'video' || evidenceType === 'honor') {
        const galleryPerm = await ImagePicker.requestMediaLibraryPermissionsAsync();
        setGalleryPermission(galleryPerm);
      }
      
      if (evidenceType === 'gps-checkin' || evidenceType === 'gps-avoid') {
        const locationPerm = await Location.requestForegroundPermissionsAsync();
        setLocationPermission(locationPerm);
      }
    };

    requestInitialPermissions();
  }, [evidenceType]);

  const requestLocationPermission = async () => {
    const result = await Location.requestForegroundPermissionsAsync();
    setLocationPermission(result);
  };

  const handleConnect = async () => {
    setIsConnecting(true);

    // Validate based on evidence type
    switch (evidenceType) {
      case 'strava':
      case 'screen-time':
        if (!inputValue.trim()) {
          Alert.alert(
            "Input Required",
            `Please enter the required ${evidenceType === 'strava' ? 'Strava profile link' : 'screen time information'}.`
          );
          setIsConnecting(false);
          return;
        }
        break;

      case 'photo':
        if (!permission?.granted || !galleryPermission?.granted) {
          Alert.alert("Permission Required", "Please grant camera and gallery access first.");
          setIsConnecting(false);
          return;
        }
        break;

      case 'video':
      case 'video-timelapse':
      case 'camera-only':
      case 'honor':
        if (!permission?.granted) {
          Alert.alert("Permission Required", "Please grant camera access first.");
          setIsConnecting(false);
          return;
        }
        break;

      case 'gps-checkin':
      case 'gps-avoid':
        if (!locationPermission?.granted) {
          Alert.alert("Permission Required", "Please grant location access first.");
          setIsConnecting(false);
          return;
        }
        break;

      case 'github':
        // Handle GitHub OAuth
        try {
          await githubService.initialize();
          const authResult = await githubService.authenticate();

          if (!authResult.success) {
            Alert.alert(
              "GitHub Authentication Failed",
              authResult.error || "Failed to connect to GitHub"
            );
            setIsConnecting(false);
            return;
          }
        } catch (error) {
          Alert.alert(
            "GitHub Connection Error",
            "Failed to connect to GitHub. Please try again."
          );
          setIsConnecting(false);
          return;
        }
        break;

      default:
        Alert.alert("Error", "Unknown evidence type");
        setIsConnecting(false);
        return;
    }

    try {
      // Update commit setupStatus in database
      const result = await firestoreService.commits.updateCommit(commit.id!, {
        setupStatus: true
      });

      if (result.success) {
        Alert.alert("Success", "Your commit setup has been completed!");
        
        // Call the callback with updated commit
        onSetupComplete({
          ...commit,
          setupStatus: true
        });
      } else {
        throw new Error(result.error || 'Setup failed');
      }
    } catch (error) {
      console.error("Error completing commit setup:", error);
      Alert.alert(
        "Error",
        "Failed to complete setup. Please try again."
      );
    }

    setIsConnecting(false);
  };

  const getSetupTitle = () => {
    switch (evidenceType) {
      case 'photo':
        return 'Enable Camera & Gallery for Photos';
      case 'video':
        return 'Enable Camera for Video Recording';
      case 'video-timelapse':
        return 'Enable Camera for Video Timelapse';
      case 'camera-only':
        return 'Enable Camera Access';
      case 'gps-checkin':
        return 'Enable Location for Check-ins';
      case 'gps-avoid':
        return 'Enable Location for Avoidance Tracking';
      case 'strava':
        return 'Connect Your Strava Profile';
      case 'github':
        return 'Connect Your GitHub Account';
      case 'screen-time':
        return 'Setup Screen Time Tracking';
      case 'honor':
        return 'Enable Camera for Honor System';
      default:
        return 'Setup Required';
    }
  };

  const getSetupDescription = () => {
    switch (evidenceType) {
      case 'photo':
        return 'To capture or select photos for your submissions, please enable camera and gallery access.';
      case 'video':
        return 'To record videos for your submissions, please enable camera access.';
      case 'video-timelapse':
        return 'To record timelapse videos for your submissions, please enable camera access.';
      case 'camera-only':
        return 'To use camera for your submissions, please enable camera access.';
      case 'gps-checkin':
        return 'To verify your location check-ins, please enable location access.';
      case 'gps-avoid':
        return 'To track location avoidance, please enable location access.';
      case 'strava':
        return 'To track your activities seamlessly, please paste your Strava profile URL below.';
      case 'github':
        return 'To verify your coding commits, please connect your GitHub account using OAuth authentication.';
      case 'screen-time':
        return 'To monitor your screen time progress, please provide your tracking information.';
      case 'honor':
        return 'To capture evidence for your honor system submissions, please enable camera access.';
      default:
        return 'Please complete the setup to continue.';
    }
  };

  const styles = createStyles(colors);

  // Text input for Strava and Screen Time
  if (evidenceType === 'strava' || evidenceType === 'screen-time') {
    return (
      <View style={styles.cardContainer}>
        <Text style={styles.cardTitle}>{getSetupTitle()}</Text>
        <Text style={styles.cardDescription}>{getSetupDescription()}</Text>
        <TextInput
          style={styles.inputField}
          placeholder={`Enter your ${evidenceType === 'strava' ? 'Strava URL' : 'screen time info'}`}
          placeholderTextColor="#aaa"
          value={inputValue}
          onChangeText={setInputValue}
        />
        <TouchableOpacity
          style={[styles.connectButton, isConnecting && styles.disabledButton]}
          onPress={handleConnect}
          disabled={isConnecting}
        >
          {isConnecting ? (
            <ActivityIndicator size="small" color="#000" />
          ) : (
            <Text style={styles.connectButtonText}>Connect</Text>
          )}
        </TouchableOpacity>
      </View>
    );
  }

  // GitHub OAuth for coding evidence
  if (evidenceType === 'github') {
    return (
      <View style={styles.cardContainer}>
        <Text style={styles.cardTitle}>{getSetupTitle()}</Text>
        <Text style={styles.cardDescription}>{getSetupDescription()}</Text>
        <TouchableOpacity
          style={[styles.connectButton, isConnecting && styles.disabledButton]}
          onPress={handleConnect}
          disabled={isConnecting}
        >
          {isConnecting ? (
            <ActivityIndicator size="small" color="#000" />
          ) : (
            <Text style={styles.connectButtonText}>Connect GitHub</Text>
          )}
        </TouchableOpacity>
      </View>
    );
  }

  // Camera access for photo, video, honor
  if (evidenceType === 'photo') {
    return (
      <View style={styles.cardContainer}>
        <Text style={styles.cardTitle}>{getSetupTitle()}</Text>
        <Text style={styles.cardDescription}>{getSetupDescription()}</Text>
        <Text style={styles.privacyNote}>
          These permissions are only active during submission time.
        </Text>

        <TouchableOpacity
          style={[
            styles.cameraAccessButton,
            permission?.granted && { backgroundColor: "#4CAF50" },
            isConnecting && styles.disabledButton,
          ]}
          onPress={async () => {
            if (!permission?.granted) {
              await requestPermission();
            } else {
              Alert.alert("Permission Granted", "Camera access already enabled!");
            }
          }}
          disabled={isConnecting}
        >
          <Text style={styles.cameraAccessButtonText}>
            Enable Camera Access {permission?.granted && "✓"}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.galleryAccessButton,
            galleryPermission?.granted && { backgroundColor: "#4CAF50" },
            isConnecting && styles.disabledButton,
          ]}
          onPress={async () => {
            if (!galleryPermission?.granted) {
              const result = await ImagePicker.requestMediaLibraryPermissionsAsync();
              setGalleryPermission(result);
            } else {
              Alert.alert("Permission Granted", "Gallery access already enabled!");
            }
          }}
          disabled={isConnecting}
        >
          <Text style={styles.galleryAccessButtonText}>
            Enable Gallery Access {galleryPermission?.granted && "✓"}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.connectButton, isConnecting && styles.disabledButton]}
          onPress={handleConnect}
          disabled={isConnecting}
        >
          {isConnecting ? (
            <ActivityIndicator size="small" color="#000" />
          ) : (
            <Text style={styles.connectButtonText}>Continue</Text>
          )}
        </TouchableOpacity>
      </View>
    );
  }

  // Camera only for video, video-timelapse, camera-only, and honor
  if (evidenceType === 'video' || evidenceType === 'video-timelapse' || evidenceType === 'camera-only' || evidenceType === 'honor') {
    return (
      <View style={styles.cardContainer}>
        <Text style={styles.cardTitle}>{getSetupTitle()}</Text>
        <Text style={styles.cardDescription}>{getSetupDescription()}</Text>
        <TouchableOpacity
          style={[
            styles.cameraAccessButton,
            permission?.granted && { backgroundColor: "#4CAF50" },
            isConnecting && styles.disabledButton,
          ]}
          onPress={async () => {
            if (!permission?.granted) {
              await requestPermission();
            } else {
              Alert.alert("Permission Granted", "Camera access already enabled!");
            }
          }}
          disabled={isConnecting}
        >
          <Text style={styles.cameraAccessButtonText}>
            Enable Camera Access {permission?.granted && "✓"}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.connectButton, isConnecting && styles.disabledButton]}
          onPress={handleConnect}
          disabled={isConnecting}
        >
          {isConnecting ? (
            <ActivityIndicator size="small" color="#000" />
          ) : (
            <Text style={styles.connectButtonText}>Continue</Text>
          )}
        </TouchableOpacity>
      </View>
    );
  }

  // Location access for GPS
  if (evidenceType === 'gps-checkin' || evidenceType === 'gps-avoid') {
    return (
      <View style={styles.cardContainer}>
        <Text style={styles.cardTitle}>{getSetupTitle()}</Text>
        <Text style={styles.cardDescription}>{getSetupDescription()}</Text>
        <Text style={styles.privacyNote}>
          Location access is only active during submission time.
        </Text>

        <TouchableOpacity
          style={[
            styles.locationAccessButton,
            locationPermission?.granted && { backgroundColor: "#4CAF50" },
            isConnecting && styles.disabledButton,
          ]}
          onPress={async () => {
            if (!locationPermission?.granted) {
              await requestLocationPermission();
            } else {
              Alert.alert("Permission Granted", "Location access already enabled!");
            }
          }}
          disabled={isConnecting}
        >
          <Text style={styles.locationAccessButtonText}>
            Enable Location Access {locationPermission?.granted && "✓"}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.connectButton, isConnecting && styles.disabledButton]}
          onPress={handleConnect}
          disabled={isConnecting}
        >
          {isConnecting ? (
            <ActivityIndicator size="small" color="#000" />
          ) : (
            <Text style={styles.connectButtonText}>Continue</Text>
          )}
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <Text style={styles.errorText}>
      Evidence type is missing or unknown.
    </Text>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  cardContainer: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 20,
    margin: 16,
    borderWidth: 1,
    borderColor: colors.border || colors.textMuted + '20',
    borderStyle: 'dashed',
  },
  cardTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: colors.text,
    marginBottom: 12,
    textAlign: 'center',
  },
  cardDescription: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 20,
  },
  privacyNote: {
    fontSize: 12,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
    textAlign: 'center',
    marginBottom: 16,
    fontStyle: 'italic',
  },
  inputField: {
    borderWidth: 1,
    borderColor: colors.border || colors.textMuted + '40',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    fontFamily: 'MontserratRegular',
    color: colors.text,
    backgroundColor: colors.background,
    marginBottom: 16,
  },
  connectButton: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    padding: 14,
    alignItems: 'center',
    marginTop: 8,
  },
  connectButtonText: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    color: '#000',
  },
  cameraAccessButton: {
    backgroundColor: colors.warning,
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginBottom: 12,
  },
  cameraAccessButtonText: {
    fontSize: 14,
    fontFamily: 'MontserratSemiBold',
    color: '#000',
  },
  galleryAccessButton: {
    backgroundColor: colors.info,
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginBottom: 12,
  },
  galleryAccessButtonText: {
    fontSize: 14,
    fontFamily: 'MontserratSemiBold',
    color: '#000',
  },
  locationAccessButton: {
    backgroundColor: colors.success,
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    marginBottom: 12,
  },
  locationAccessButtonText: {
    fontSize: 14,
    fontFamily: 'MontserratSemiBold',
    color: '#000',
  },
  disabledButton: {
    opacity: 0.6,
  },
  errorText: {
    fontSize: 16,
    fontFamily: 'MontserratSemiBold',
    color: colors.error,
    textAlign: 'center',
    padding: 20,
  },
});

export default CommitSetup;
