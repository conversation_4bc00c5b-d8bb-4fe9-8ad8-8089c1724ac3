import React, { useState, useEffect } from "react";
import { Text, View, StyleSheet, TouchableOpacity } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import { getId } from "@/utilis/variables";
import BreathingDot from "./pageFunctions/Progress/BreathingDot";
import { firestoreService, addSubscription, removeSubscription } from "../services/database";
import { useTheme } from "@/contexts/ThemeContext";

export default function Header() {
  const router = useRouter();
  const { colors } = useTheme();
  const [hasUnreadNotifications, setHasUnreadNotifications] = useState(false);

  useEffect(() => {
    const setupNotificationListener = async () => {
      try {
        const userEmail = await getId();
        if (!userEmail) return;

        const unsubscribe = firestoreService.notifications.subscribeToUnreadCount(
          userEmail,
          (count) => setHasUnreadNotifications(count > 0)
        );

        addSubscription('header-notifications', unsubscribe);
      } catch (error) {
        console.error("Error setting up notification listener:", error);
      }
    };

    setupNotificationListener();

    // Cleanup subscription on unmount
    return () => removeSubscription('header-notifications');
  }, []);

  return (
    <View style={[styles.headerContainer, { backgroundColor: colors.header }]}>
      <View style={styles.header}>
        <Text style={[styles.headerText, { color: colors.text }]}>Habit Royale</Text>
        <View style={styles.notificationContainer}>
          {hasUnreadNotifications && (
            <View style={styles.dotContainer}>
              <BreathingDot />
            </View>
          )}
          <TouchableOpacity
            style={styles.notificationButton}
            onPress={() => router.push("/Notifications")}
          >
            <Ionicons name="notifications-outline" size={24} color={colors.text} />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  headerContainer: {
    // backgroundColor will be set dynamically based on theme
    zIndex: 100, // Ensure header stays above other elements
  },
  header: {
    paddingTop: 50,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    paddingBottom: 12,
    position: "relative",
    minHeight: 80, // Ensure consistent height
  },
  headerText: {
    // color will be set dynamically based on theme
    fontSize: 28,
    fontFamily: "CasinoFlatShadowItalic",
  },
  notificationContainer: {
    position: "absolute",
    right: 15,
    top: 49,
  },
  notificationButton: {
    padding: 5,
  },
  dotContainer: {
    position: "absolute",
    right: 3,
    top: 3,
    zIndex: 1,
  }
});
