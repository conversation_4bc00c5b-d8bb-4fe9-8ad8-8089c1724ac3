#!/bin/bash

# GitHub OAuth Cloud Function Deployment Script
# This script builds and deploys the Firebase Cloud Functions

echo "🚀 Deploying GitHub OAuth Cloud Functions..."

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI is not installed. Please install it first:"
    echo "npm install -g firebase-tools"
    exit 1
fi

# Check if logged in to Firebase
if ! firebase projects:list &> /dev/null; then
    echo "❌ Not logged in to Firebase. Please login first:"
    echo "firebase login"
    exit 1
fi

# Build TypeScript
echo "📦 Building TypeScript..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ TypeScript build failed"
    exit 1
fi

# Set GitHub environment variables (if not already set)
echo "🔧 Setting up environment variables..."
echo "Please make sure you have set your GitHub OAuth credentials:"
echo "firebase functions:config:set github.client_id=\"YOUR_GITHUB_CLIENT_ID\""
echo "firebase functions:config:set github.client_secret=\"YOUR_GITHUB_CLIENT_SECRET\""
echo ""
echo "Current GitHub config:"
firebase functions:config:get github

# Deploy functions
echo "🚀 Deploying Cloud Functions..."
firebase deploy --only functions:githubOAuthCallback,functions:getGitHubToken

if [ $? -eq 0 ]; then
    echo "✅ GitHub OAuth Cloud Functions deployed successfully!"
    echo ""
    echo "📋 Next steps:"
    echo "1. Update your GitHub OAuth App callback URL to:"
    echo "   https://betonself.cloudfunctions.net/githubOAuthCallback"
    echo ""
    echo "2. Set your environment variables if you haven't already:"
    echo "   firebase functions:config:set github.client_id=\"YOUR_CLIENT_ID\""
    echo "   firebase functions:config:set github.client_secret=\"YOUR_CLIENT_SECRET\""
    echo ""
    echo "3. Test the OAuth flow in your app!"
else
    echo "❌ Deployment failed"
    exit 1
fi
