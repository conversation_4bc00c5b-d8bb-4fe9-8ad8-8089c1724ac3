// Event Manager Initialization Script
// This file should be imported and called during app startup

import { Platform } from 'react-native';
import { initializeEventManager, EventManagerConfig } from './index';

/**
 * Initialize the event management system with platform-specific configuration
 * Call this function during app startup, preferably in App.tsx or index.js
 */
export function initializeEventSystem(): void {
  try {
    console.log('Initializing Event Management System...');

    // Platform-specific configuration
    const config: Partial<EventManagerConfig> = {
      maxRetries: 3,
      retryDelay: 1000,
      batchSize: Platform.OS === 'web' ? 20 : 10, // Larger batches on web
      enableAnalytics: true,
      enableLogging: __DEV__, // Only log in development
      firebaseConfig: {
        collection: 'events',
        enablePersistence: true,
        enableRealtime: false,
      },
      notificationConfig: {
        enabled: true,
        types: ['account', 'program', 'points', 'reminder', 'system', 'achievement'],
        priorities: ['low', 'medium', 'high', 'critical'],
        deliveryMethods: Platform.OS === 'web' ? ['firebase'] : ['firebase', 'push'],
        quietHours: {
          enabled: false, // Can be enabled based on user preferences
          start: '22:00',
          end: '08:00',
          timezone: 'UTC',
        },
      },
    };

    // Initialize the event manager
    const eventManager = initializeEventManager(config);

    // Log initialization success
    console.log('✅ Event Management System initialized successfully');
    console.log(`📊 Handlers registered: ${eventManager.getHandlers().join(', ')}`);
    console.log(`🔧 Middleware active: ${eventManager.getMiddleware().join(', ')}`);

    // Set up error handling for unhandled promise rejections
    if (Platform.OS === 'web' && typeof window !== 'undefined' && window.addEventListener) {
      window.addEventListener('unhandledrejection', (event) => {
        console.error('Unhandled promise rejection in event system:', event.reason);
      });
    } else if (Platform.OS !== 'web') {
      // For React Native, set up global error handler
      const originalHandler = global.ErrorUtils?.getGlobalHandler?.();
      global.ErrorUtils?.setGlobalHandler?.((error, isFatal) => {
        console.error('Unhandled error in event system:', error);
        if (originalHandler) {
          originalHandler(error, isFatal);
        }
      });
    }

    // Set up periodic cleanup (every 24 hours)
    if (Platform.OS !== 'web') {
      setInterval(() => {
        performPeriodicCleanup();
      }, 24 * 60 * 60 * 1000); // 24 hours
    }

  } catch (error) {
    console.error('❌ Failed to initialize Event Management System:', error);
    throw error;
  }
}

/**
 * Perform periodic cleanup tasks
 */
async function performPeriodicCleanup(): Promise<void> {
  try {
    console.log('🧹 Performing periodic event system cleanup...');

    // Import handlers dynamically to avoid circular dependencies
    const { FirebaseEventHandler } = await import('./handlers/FirebaseEventHandler');
    const { AnalyticsEventHandler } = await import('./handlers/AnalyticsEventHandler');

    // Cleanup old events (older than 30 days)
    const firebaseHandler = new FirebaseEventHandler();
    const deletedCount = await firebaseHandler.cleanupOldEvents(30);
    console.log(`🗑️ Cleaned up ${deletedCount} old events`);

    // Flush any pending analytics
    const analyticsHandler = new AnalyticsEventHandler();
    await analyticsHandler.flush();
    console.log('📊 Flushed pending analytics');

    console.log('✅ Periodic cleanup completed');
  } catch (error) {
    console.error('❌ Periodic cleanup failed:', error);
  }
}

/**
 * Gracefully shutdown the event system
 * Call this when the app is closing or during hot reload in development
 */
export async function shutdownEventSystem(): Promise<void> {
  try {
    console.log('🔄 Shutting down Event Management System...');

    // Import handlers dynamically
    const { FirebaseEventHandler } = await import('./handlers/FirebaseEventHandler');
    const { AnalyticsEventHandler } = await import('./handlers/AnalyticsEventHandler');

    // Flush any pending batches
    const firebaseHandler = new FirebaseEventHandler();
    await firebaseHandler.flush();

    const analyticsHandler = new AnalyticsEventHandler();
    await analyticsHandler.flush();

    console.log('✅ Event Management System shutdown completed');
  } catch (error) {
    console.error('❌ Event system shutdown failed:', error);
  }
}

/**
 * Health check for the event system
 * Returns true if the system is functioning properly
 */
export async function healthCheck(): Promise<boolean> {
  try {
    const { getEventManager, sendNotification, EventPriority, NotificationType } = await import('./index');
    
    const eventManager = getEventManager();
    
    // Check if handlers are registered
    const handlers = eventManager.getHandlers();
    if (handlers.length === 0) {
      console.error('❌ No event handlers registered');
      return false;
    }

    // Check if middleware is active
    const middleware = eventManager.getMiddleware();
    if (middleware.length === 0) {
      console.warn('⚠️ No middleware active');
    }

    // Test event emission (without actually sending notification)
    const testEventId = await sendNotification(
      '<EMAIL>',
      'Health Check',
      'Event system health check',
      NotificationType.SYSTEM,
      EventPriority.LOW,
      { healthCheck: true, skipDelivery: true }
    );

    if (!testEventId) {
      console.error('❌ Failed to emit test event');
      return false;
    }

    console.log('✅ Event system health check passed');
    return true;
  } catch (error) {
    console.error('❌ Event system health check failed:', error);
    return false;
  }
}

/**
 * Get event system statistics
 */
export async function getSystemStats(): Promise<any> {
  try {
    const { getEventManager } = await import('./index');
    const eventManager = getEventManager();
    
    const stats = eventManager.getStats();
    const queueSize = eventManager.getQueueSize();
    
    return {
      ...stats,
      queueSize,
      handlers: eventManager.getHandlers(),
      middleware: eventManager.getMiddleware(),
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error('Failed to get system stats:', error);
    return null;
  }
}

/**
 * Development helper to test the event system
 * Only available in development mode
 */
export async function testEventSystem(): Promise<void> {
  if (!__DEV__) {
    console.warn('testEventSystem is only available in development mode');
    return;
  }

  try {
    console.log('🧪 Testing Event Management System...');

    const { 
      emitUserEvent, 
      emitProgramEvent, 
      sendNotification,
      EventType, 
      EventPriority, 
      NotificationType 
    } = await import('./index');

    // Test user event
    await emitUserEvent(EventType.USER_LOGIN, {
      userEmail: '<EMAIL>',
      source: 'TestSystem',
      deviceInfo: { platform: Platform.OS },
    });

    // Test program event
    await emitProgramEvent(EventType.PROGRAM_JOIN, {
      userEmail: '<EMAIL>',
      programId: 'test-program',
      programName: 'Test Program',
      source: 'TestSystem',
    });

    // Test notification
    await sendNotification(
      '<EMAIL>',
      'Test Notification',
      'This is a test notification from the event system',
      NotificationType.SYSTEM,
      EventPriority.LOW
    );

    console.log('✅ Event system test completed successfully');
  } catch (error) {
    console.error('❌ Event system test failed:', error);
  }
}

// Export for easy access
export default {
  initialize: initializeEventSystem,
  shutdown: shutdownEventSystem,
  healthCheck,
  getStats: getSystemStats,
  test: testEventSystem,
};
