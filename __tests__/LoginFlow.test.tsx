import { jest } from '@jest/globals';

// Mock Firebase Auth
jest.mock('firebase/auth', () => ({
  signInWithEmailAndPassword: jest.fn(),
  createUserWithEmailAndPassword: jest.fn(),
  sendPasswordResetEmail: jest.fn(),
  sendEmailVerification: jest.fn(),
}));

// Mock Firebase config
jest.mock('../config/firebase', () => ({
  auth: {},
}));

// Mock firestoreService
jest.mock('../services/database', () => ({
  firestoreService: {
    users: {
      createUser: jest.fn(),
      getUserById: jest.fn(),
    },
    notifications: {
      createNotification: jest.fn(),
      createWelcomeNotification: jest.fn(),
    },
    analytics: {
      recordUserLogin: jest.fn(),
    },
  },
}));

// Mock event manager
jest.mock('../utilis/eventManager', () => ({
  emitUserEvent: jest.fn(),
  EventType: {
    USER_LOGIN: 'USER_LOGIN',
    USER_SIGNUP: 'USER_SIGNUP',
  },
  EventPriority: {
    LOW: 'LOW',
    MEDIUM: 'MEDIUM',
    HIGH: 'HIGH',
  },
}));

// Mock variables
jest.mock('../utilis/variables', () => ({
  updateToken: jest.fn(),
  updateId: jest.fn(),
  updateFname: jest.fn(),
  updateLname: jest.fn(),
}));

// Mock fetchUserData
jest.mock('../components/pageFunctions/SignUp/fetchUserData', () => ({
  __esModule: true,
  default: jest.fn(),
}));

// Mock AuthContext
jest.mock('../contexts/AuthContext', () => ({
  useAuth: () => ({
    isAuthenticated: false,
  }),
}));

// Mock React Native components
jest.mock('react-native', () => ({
  Platform: { OS: 'ios' },
  Alert: { alert: jest.fn() },
  StyleSheet: { create: jest.fn() },
}));

describe('Login Flow Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should handle successful login with event emission', async () => {
    const { signInWithEmailAndPassword } = require('firebase/auth');
    const { emitUserEvent, EventType, EventPriority } = require('../utilis/eventManager');
    const fetchUserData = require('../components/pageFunctions/SignUp/fetchUserData').default;
    const { updateToken, updateId } = require('../utilis/variables');

    // Mock successful login
    const mockUser = {
      email: '<EMAIL>',
      emailVerified: true,
    };
    signInWithEmailAndPassword.mockResolvedValue({ user: mockUser });
    fetchUserData.mockResolvedValue(true);
    emitUserEvent.mockResolvedValue('event-id-123');

    // Import the login handler (we would need to extract this to a testable function)
    // For now, let's test the event emission directly
    await emitUserEvent(EventType.USER_LOGIN, {
      userEmail: '<EMAIL>',
      source: 'LoginScreen',
      deviceInfo: {
        platform: 'ios',
        version: '1.0.0',
      },
      metadata: {
        loginMethod: 'email_password',
        loginTime: new Date().toISOString(),
      },
    }, EventPriority.LOW);

    // Verify event was emitted
    expect(emitUserEvent).toHaveBeenCalledWith(
      EventType.USER_LOGIN,
      expect.objectContaining({
        userEmail: '<EMAIL>',
        source: 'LoginScreen',
        deviceInfo: expect.objectContaining({
          platform: 'ios',
          version: '1.0.0',
        }),
        metadata: expect.objectContaining({
          loginMethod: 'email_password',
        }),
      }),
      EventPriority.LOW
    );
  });

  it('should handle analytics recording for login', async () => {
    const { firestoreService } = require('../services/database');
    
    // Test analytics recording
    await firestoreService.analytics.recordUserLogin('<EMAIL>', {
      loginTime: new Date().toISOString(),
      platform: 'ios',
      deviceInfo: {
        platform: 'ios',
        version: '1.0.0',
      },
      success: true,
    });

    expect(firestoreService.analytics.recordUserLogin).toHaveBeenCalledWith(
      '<EMAIL>',
      expect.objectContaining({
        platform: 'ios',
        success: true,
      })
    );
  });

  it('should handle notification creation for login', async () => {
    const { firestoreService } = require('../services/database');
    
    // Test notification creation
    await firestoreService.notifications.createNotification('<EMAIL>', {
      title: "New Login Detected",
      message: expect.stringContaining("Successfully logged in"),
      type: "account",
      priority: "low"
    });

    expect(firestoreService.notifications.createNotification).toHaveBeenCalledWith(
      '<EMAIL>',
      expect.objectContaining({
        title: "New Login Detected",
        type: "account",
        priority: "low"
      })
    );
  });
});
